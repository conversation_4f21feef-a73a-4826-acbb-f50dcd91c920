import React from 'react';

const ReactComponent = () => {
  const handleExport = (type: 'selected' | 'all') => {
    if (type === 'selected') {
      if (selectedRows.length === 0) {
        console.log('no selection');
        return;
      }
      console.log('export selected');
    }

    if (type === 'all') {
      if (!dataSource?.length) {
        console.log('no data');
        return;
      }
      console.log('export all');
    }
  }

  const rowSelection = {
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      if (selectedRows) {
        console.log('rows changed');
      }
    },
  }

  return (
    <div>
      <button onClick={() => handleExport('selected')}>Export Selected</button>
      <button onClick={() => handleExport('all')}>Export All</button>
    </div>
  );
}

export default ReactComponent;