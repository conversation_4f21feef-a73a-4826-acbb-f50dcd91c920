import { analyzeFile } from './src/index';

async function testImprovedFunctionNames() {
  console.log('=== 测试改进后的函数名提取 ===\n');
  
  // 创建包含各种函数类型的测试代码
  const testCode = `
// 命名函数声明
function namedFunction() {
  if (true) return 1;
}

// 变量赋值的函数表达式
const assignedFunction = function() {
  if (false) return 2;
};

// 箭头函数赋值
const arrowFunction = () => {
  if (true) return 3;
};

// 对象方法
const obj = {
  methodName: function() {
    if (true) return 4;
  },
  
  // 方法简写
  shortMethod() {
    if (false) return 5;
  },
  
  // 箭头方法
  arrowMethod: () => {
    if (true) return 6;
  }
};

// 类方法
class TestClass {
  classMethod() {
    if (true) return 7;
  }
  
  static staticMethod() {
    if (false) return 8;
  }
}
`;

  // 写入测试文件
  await import('fs/promises').then(fs => 
    fs.writeFile('function-names-test.ts', testCode)
  );
  
  try {
    console.log('📝 测试各种函数类型的名称提取:');
    
    // 使用详细信息以便查看函数名
    const result = await analyzeFile('function-names-test.ts', {
      enableDetails: true
    });
    
    console.log(`✅ 文件: ${result.filePath}`);
    console.log(`✅ 检测到 ${result.functions.length} 个函数`);
    console.log(`✅ 总复杂度: ${result.complexity}`);
    
    console.log('\n📋 函数列表:');
    result.functions.forEach((fn, index) => {
      console.log(`   ${index + 1}. ${fn.name} (复杂度: ${fn.complexity}, 位置: ${fn.line}:${fn.column}, 详情: ${fn.details?.length || 0})`);
    });
    
    console.log('\n🎯 预期的改进:');
    console.log('   - namedFunction 应该显示为 "namedFunction"');
    console.log('   - assignedFunction 应该显示为 "assignedFunction"'); 
    console.log('   - arrowFunction 应该显示为 "arrowFunction"');
    console.log('   - methodName 应该显示为 "methodName"');
    console.log('   - shortMethod 应该显示为 "shortMethod"');
    console.log('   - classMethod 应该显示为 "classMethod"');
    console.log('   - 箭头函数应该使用 "arrow_行_列" 格式');
    
  } finally {
    // 清理测试文件
    await import('fs/promises').then(fs => 
      fs.unlink('function-names-test.ts').catch(() => {})
    );
  }
}

testImprovedFunctionNames();