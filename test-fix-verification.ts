#!/usr/bin/env bun

/**
 * Bug修复验证测试
 * 验证函数边界管理时序问题的修复效果
 */

import { analyzeCode } from './src/index';

// 测试用的React组件代码
const testCode = `
import React from 'react';

export const TestComponent = () => {
  const handleExport = () => {
    if (condition1) {
      console.log('condition1');
      if (condition2) {
        console.log('condition2');
        if (condition3) {
          console.log('condition3');
        }
      }
    }
    return result;
  };

  const onChange = (value: string) => {
    if (value) {
      console.log('value exists');
    }
  };

  return (
    <div>
      <button onClick={handleExport}>Export</button>
      <input onChange={(e) => onChange(e.target.value)} />
    </div>
  );
};
`;

async function testBugFix() {
  console.log('🚀 开始测试Bug修复效果...\n');
  
  try {
    const result = await analyzeCode(testCode, 'test.tsx', {
      enableDebugLog: true,
      enableDetails: true,
    });

    console.log('📊 分析结果:');
    console.log(`文件复杂度: ${result.complexity}`);
    console.log(`函数数量: ${result.functions.length}`);
    console.log('');

    // 验证函数识别
    console.log('🔍 函数识别验证:');
    result.functions.forEach((func, index) => {
      console.log(`${index + 1}. ${func.name} - 复杂度: ${func.complexity}, 位置: (${func.line}:${func.column})`);
      if (func.details && func.details.length > 0) {
        console.log(`   详细信息: ${func.details.length} 个复杂度增量`);
        func.details.forEach((detail: any) => {
          console.log(`   - 第${detail.line}行: +${detail.increment} (${detail.description})`);
        });
      }
    });

    // 验证修复效果
    console.log('\n✅ 修复效果验证:');
    
    // 1. 验证函数识别是否正确
    const expectedFunctions = ['TestComponent', 'handleExport', 'onChange'];
    const actualFunctionNames = result.functions.map(f => f.name);
    
    console.log('期望识别的函数:', expectedFunctions);
    console.log('实际识别的函数:', actualFunctionNames);
    
    // 2. 验证handleExport函数的复杂度是否正确
    // 第一个if: 1(base) + 1(nesting=1) = 2
    // 第二个if: 1(base) + 2(nesting=2) = 3  
    // 第三个if: 1(base) + 3(nesting=3) = 4
    // 总计: 2 + 3 + 4 = 9
    const handleExportFunc = result.functions.find(f => f.name === 'handleExport');
    if (handleExportFunc) {
      console.log(`handleExport函数复杂度: ${handleExportFunc.complexity} (期望: 9)`);
      console.log(`handleExport函数位置: (${handleExportFunc.line}:${handleExportFunc.column})`);
      
      if (handleExportFunc.complexity === 9) {
        console.log('✅ handleExport复杂度计算正确');
      } else {
        console.log('❌ handleExport复杂度计算错误');
      }
    } else {
      console.log('❌ 未找到handleExport函数');
    }
    
    // 3. 验证onChange函数的复杂度是否正确
    // 第一个if: 1(base) + 1(nesting=1) = 2 (因为onChange函数本身增加了嵌套层级)
    const onChangeFunc = result.functions.find(f => f.name === 'onChange');
    if (onChangeFunc) {
      console.log(`onChange函数复杂度: ${onChangeFunc.complexity} (期望: 2)`);
      console.log(`onChange函数位置: (${onChangeFunc.line}:${onChangeFunc.column})`);
      
      if (onChangeFunc.complexity === 2) {
        console.log('✅ onChange复杂度计算正确');
      } else {
        console.log('❌ onChange复杂度计算错误');
      }
    } else {
      console.log('❌ 未找到onChange函数');
    }

    console.log('\n🎉 测试完成！');
    return result;
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    return null;
  }
}

// 运行测试
testBugFix().then(result => {
  if (result) {
    console.log('\n✅ Bug修复验证测试通过');
    process.exit(0);
  } else {
    console.log('\n❌ Bug修复验证测试失败');
    process.exit(1);
  }
});