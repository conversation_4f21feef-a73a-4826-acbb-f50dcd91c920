// 更真实的测试文件
function calculateScore(items: any[]): number {
  if (!items || items.length === 0) {
    return 0;
  }
  
  let total = 0;
  for (const item of items) {
    if (item.active) {
      if (item.priority === 'high') {
        total += item.value * 2;
      } else if (item.priority === 'medium') {
        total += item.value * 1.5;
      } else {
        total += item.value;
      }
    }
  }
  
  return total;
}

const processData = function(data: any) {
  if (data.type === 'user') {
    return data.name;
  } else if (data.type === 'admin') {
    return data.name + ' (Admin)';
  } else {
    return 'Unknown';
  }
};

export { calculateScore, processData };