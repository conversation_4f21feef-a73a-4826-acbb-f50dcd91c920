import { parseSync } from '@swc/core';
import { readFileSync } from 'fs';

const filePath = '/Users/<USER>/projj/git.imile.com/ux/ds-web/apps/ds/src/pages/InventoryEE/NDR/index.tsx';
const sourceCode = readFileSync(filePath, 'utf-8');

console.log('验证 SWC 解析和位置计算...');

// 定位到具体行的源代码
const lines = sourceCode.split('\n');
console.log('\n===== 问题行的源代码 =====');
console.log(`L243: "${lines[242]?.trim()}"`); // 0索引，所以242对应243行
console.log(`L245: "${lines[244]?.trim()}"`);
console.log(`L263: "${lines[262]?.trim()}"`);
console.log(`L264: "${lines[263]?.trim()}"`);
console.log(`L234: "${lines[233]?.trim()}"`);

// 计算字符位置
function getCharacterPosition(lineNumber: number, columnNumber: number = 1): number {
  let position = 0;
  for (let i = 0; i < lineNumber - 1; i++) {
    position += (lines[i]?.length || 0) + 1; // +1 for newline
  }
  return position + columnNumber - 1;
}

// 反向计算：从字符位置到行号
function getLineFromPosition(position: number): number {
  let currentPos = 0;
  for (let i = 0; i < lines.length; i++) {
    const lineLength = (lines[i]?.length || 0) + 1;
    if (currentPos + lineLength > position) {
      return i + 1;
    }
    currentPos += lineLength;
  }
  return lines.length;
}

try {
  const ast = parseSync(sourceCode, {
    syntax: 'typescript',
    tsx: true,
    target: 'es2020',
  });
  
  console.log('\n===== SWC 解析结果验证 =====');
  
  // 查找所有 IfStatement 节点并验证其实际位置
  function findAllIfStatements(node: any, path: string[] = []): any[] {
    const results: any[] = [];
    
    if (!node || typeof node !== 'object') return results;
    
    if (node.type === 'IfStatement' && node.span) {
      const startLine = getLineFromPosition(node.span.start);
      const endLine = getLineFromPosition(node.span.end);
      
      // 获取该span对应的实际代码
      const spanCode = sourceCode.slice(node.span.start, node.span.end);
      const firstLine = spanCode.split('\n')[0];
      
      results.push({
        type: node.type,
        startLine,
        endLine,
        span: node.span,
        path: path.join(' -> '),
        firstLineCode: firstLine,
        actualSourceAtLine: lines[startLine - 1]?.trim()
      });
    }
    
    // 递归查找
    for (const [key, value] of Object.entries(node)) {
      if (key === 'span' || key === 'type') continue;
      
      if (Array.isArray(value)) {
        value.forEach((child, index) => {
          if (child && typeof child === 'object') {
            results.push(...findAllIfStatements(child, [...path, `${key}[${index}]`]));
          }
        });
      } else if (value && typeof value === 'object') {
        results.push(...findAllIfStatements(value, [...path, key]));
      }
    }
    
    return results;
  }
  
  const ifStatements = findAllIfStatements(ast);
  
  ifStatements.forEach((ifStmt, index) => {
    console.log(`\n${index + 1}. IfStatement 在第 ${ifStmt.startLine} 行 (span: ${ifStmt.span.start}-${ifStmt.span.end})`);
    console.log(`   路径: ${ifStmt.path}`);
    console.log(`   Span首行代码: "${ifStmt.firstLineCode}"`);
    console.log(`   实际源码行: "${ifStmt.actualSourceAtLine}"`);
    
    // 检查是否真的是 if 语句
    const sourceAtLine = lines[ifStmt.startLine - 1]?.trim() || '';
    const isRealIfStatement = sourceAtLine.startsWith('if ') || sourceAtLine.includes('if (');
    console.log(`   ✅ 真实的 if 语句: ${isRealIfStatement ? '是' : '❌ 否'}`);
    
    if (!isRealIfStatement) {
      console.log(`   ⚠️  这不是真正的 if 语句，SWC解析可能有问题`);
    }
  });
  
  console.log('\n===== 位置计算验证 =====');
  
  // 验证问题位置的字符偏移量
  const problemPositions = [
    { line: 243, desc: 'fireExport({' },
    { line: 245, desc: 'bizType,' },
    { line: 263, desc: 'exportParams: {' },
    { line: 264, desc: 'bizType,' },
    { line: 234, desc: 'const handleExport' }
  ];
  
  problemPositions.forEach(pos => {
    const charPos = getCharacterPosition(pos.line);
    const backToLine = getLineFromPosition(charPos);
    console.log(`L${pos.line} (${pos.desc}):`);
    console.log(`  字符位置: ${charPos}`);
    console.log(`  回转换行号: ${backToLine}`);
    console.log(`  源码: "${lines[pos.line - 1]?.trim()}"`);
    console.log(`  字符位置的代码: "${sourceCode.slice(charPos, charPos + 20)}..."`);
    console.log('');
  });
  
} catch (error) {
  console.error('解析失败:', error);
}