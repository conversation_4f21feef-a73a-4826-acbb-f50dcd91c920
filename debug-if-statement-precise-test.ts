import { parseSync } from '@swc/core';

// 分别测试每个 if 语句
const testCases = [
  `if (condition) { console.log('test'); }`,
  `if(condition){console.log('test');}`,  
  `function test() { if (a > b) { return true; } }`,
  `function test() { if (!data?.length) { throw new Error('No data'); } }`,
  `function test() { if (type === 'selected') { fireExport({ exportParams: { bizType } }); } }`
];

console.log('=== 单独测试每个 if 语句 ===\n');

testCases.forEach((code, index) => {
  console.log(`测试用例 ${index + 1}:`);
  console.log(`代码: ${code}`);
  console.log('─'.repeat(60));
  
  try {
    const ast = parseSync(code, {
      syntax: 'typescript',
      tsx: false,
      target: 'es2020',
    });
    
    // 递归查找 IfStatement 节点
    function findIfStatements(node: any, path: string[] = []): any[] {
      const results: any[] = [];
      
      if (!node || typeof node !== 'object') return results;
      
      if (node.type === 'IfStatement' && node.span) {
        results.push({
          node,
          path: path.join(' -> '),
          span: node.span
        });
      }
      
      for (const [key, value] of Object.entries(node)) {
        if (key === 'span' || key === 'type') continue;
        
        if (Array.isArray(value)) {
          value.forEach((child, idx) => {
            if (child && typeof child === 'object') {
              results.push(...findIfStatements(child, [...path, `${key}[${idx}]`]));
            }
          });
        } else if (value && typeof value === 'object') {
          results.push(...findIfStatements(value, [...path, key]));
        }
      }
      
      return results;
    }
    
    const ifStatements = findIfStatements(ast);
    
    if (ifStatements.length === 0) {
      console.log('  ❌ 未找到 IfStatement 节点');
    } else {
      ifStatements.forEach((ifStmt, idx) => {
        const { span, path } = ifStmt;
        
        console.log(`\n  IfStatement ${idx + 1}:`);
        console.log(`    路径: ${path}`);
        console.log(`    Span: ${span.start}-${span.end}`);
        
        // 检查 span 指向的字符
        if (span.start >= 0 && span.start < code.length) {
          const charAtSpan = code[span.start];
          const spanCode = code.slice(span.start, span.end);
          const spanFirstLine = spanCode.split('\n')[0];
          
          console.log(`    Span 开始字符: "${charAtSpan}"`);
          console.log(`    Span 首行: "${spanFirstLine}"`);
          
          // 检查是否需要修正（向前查找 'if'）
          let correctionNeeded = false;
          let correctedPosition = span.start;
          
          if (charAtSpan === 'f' && span.start > 0 && code[span.start - 1] === 'i') {
            correctionNeeded = true;
            correctedPosition = span.start - 1;
          }
          
          console.log(`    需要修正: ${correctionNeeded ? '✅ 是' : '❌ 否'}`);
          
          if (correctionNeeded) {
            const correctedSpanCode = code.slice(correctedPosition, span.end);
            const correctedFirstLine = correctedSpanCode.split('\n')[0];
            console.log(`    修正后位置: ${correctedPosition}`);
            console.log(`    修正后首行: "${correctedFirstLine}"`);
          }
        } else {
          console.log(`    ⚠️  Span 位置超出代码范围`);
        }
      });
    }
    
  } catch (error) {
    console.log(`  ❌ 解析失败: ${error}`);
  }
  
  console.log('\n');
});

// 专门测试真实文件中的问题模式
console.log('=== 测试真实问题模式 ===\n');

const realCode = `
const handleExport = (type: 'selected' | 'all') => {
  const exportParams = handleExportParams(formatedQueryParams)

  if (type === 'selected') {
    if (selectedRows.length === 0) {
      enqueueSnackbar(t('请先选中'), { variant: 'info' })
      return
    }

    fireExport({
      exportParams: {
        bizType,
        jobName: 'NDR',
      },
    })
  }

  if (type === 'all') {
    fireExport({
      exportParams: {
        bizType,
        jobName: 'NDR',
      },
    })
  }
}
`;

try {
  const ast = parseSync(realCode, {
    syntax: 'typescript',
    tsx: false,
    target: 'es2020',
  });
  
  function findIfStatements(node: any, path: string[] = []): any[] {
    const results: any[] = [];
    
    if (!node || typeof node !== 'object') return results;
    
    if (node.type === 'IfStatement' && node.span) {
      results.push({
        node,
        path: path.join(' -> '),
        span: node.span
      });
    }
    
    for (const [key, value] of Object.entries(node)) {
      if (key === 'span' || key === 'type') continue;
      
      if (Array.isArray(value)) {
        value.forEach((child, idx) => {
          if (child && typeof child === 'object') {
            results.push(...findIfStatements(child, [...path, `${key}[${idx}]`]));
          }
        });
      } else if (value && typeof value === 'object') {
        results.push(...findIfStatements(value, [...path, key]));
      }
    }
    
    return results;
  }
  
  const ifStatements = findIfStatements(ast);
  
  console.log(`找到 ${ifStatements.length} 个 IfStatement 节点:\n`);
  
  ifStatements.forEach((ifStmt, idx) => {
    const { span, path } = ifStmt;
    
    // 计算行号
    const linesBeforeSpan = realCode.slice(0, span.start).split('\n');
    const lineNumber = linesBeforeSpan.length;
    
    console.log(`IfStatement ${idx + 1} (第 ${lineNumber} 行):`);
    console.log(`  路径: ${path}`);
    console.log(`  Span: ${span.start}-${span.end}`);
    
    // 显示 span 对应的代码
    const spanCode = realCode.slice(span.start, span.end);
    const firstLine = spanCode.split('\n')[0];
    
    console.log(`  Span 首行: "${firstLine}"`);
    console.log(`  Span 开始字符: "${realCode[span.start]}"`);
    
    // 检查修正
    const needsCorrection = realCode[span.start] === 'f' && 
                           span.start > 0 && 
                           realCode[span.start - 1] === 'i';
    
    console.log(`  需要修正: ${needsCorrection ? '✅ 是' : '❌ 否'}`);
    
    if (needsCorrection) {
      const correctedFirstLine = realCode.slice(span.start - 1, span.end).split('\n')[0];
      console.log(`  修正后首行: "${correctedFirstLine}"`);
    }
    
    console.log('');
  });
  
} catch (error) {
  console.error('真实代码解析失败:', error);
}