/**
 * 简单的箭头函数定位测试
 */

import { ComplexityVisitor } from './src/core/complexity-visitor';
import { ASTParser } from './src/core/parser';

async function simpleTest() {
  const sourceCode = `function test() {
  const add = (a, b) => a + b;
  const square = x => x * x;
  return add(1, 2);
}`;

  console.log('源代码:');
  console.log(sourceCode);
  console.log('');

  const parser = new ASTParser();
  const ast = await parser.parseCode(sourceCode, 'test.ts');
  const visitor = new ComplexityVisitor(sourceCode);

  // 查找箭头函数
  function findArrowFunctions(node: any): any[] {
    const results: any[] = [];
    
    function traverse(n: any) {
      if (!n || typeof n !== 'object') return;
      
      if (n.type === 'ArrowFunctionExpression') {
        results.push(n);
      }
      
      for (const key in n) {
        if (key === 'span' || key === 'type') continue;
        const value = n[key];
        if (Array.isArray(value)) {
          value.forEach(traverse);
        } else if (value && typeof value === 'object') {
          traverse(value);
        }
      }
    }
    
    traverse(node);
    return results;
  }

  const arrowFunctions = findArrowFunctions(ast);
  console.log(`找到 ${arrowFunctions.length} 个箭头函数:`);

  for (let i = 0; i < arrowFunctions.length; i++) {
    const arrowFunc = arrowFunctions[i];
    console.log(`\n箭头函数 ${i + 1}:`);
    console.log(`- span: ${arrowFunc.span ? `${arrowFunc.span.start}-${arrowFunc.span.end}` : 'undefined'}`);
    
    const position = visitor.findArrowFunctionPosition(arrowFunc);
    console.log(`- 定位结果: ${position}`);
    
    if (position !== null) {
      const beforeArrow = sourceCode.slice(Math.max(0, position - 10), position);
      const atArrow = sourceCode.slice(position, position + 2);
      const afterArrow = sourceCode.slice(position + 2, position + 12);
      console.log(`- 上下文: "${beforeArrow}${atArrow}${afterArrow}"`);
      console.log(`- 箭头位置: ${' '.repeat(beforeArrow.length)}^^`);
    }
  }
}

simpleTest().catch(console.error);