import { parseSync } from '@swc/core';
import { SemanticComplexityVisitorFactory } from './src/core/semantic-complexity-visitor-factory';
import { DetailCollector } from './src/core/detail-collector';
import { CodeFrameGenerator } from './src/utils/code-frame-generator';
import { writeFileSync } from 'fs';

const testCode = `const handleExport = (type: 'selected' | 'all') => {
  const exportParams = handleExportParams(formatedQueryParams)

  if (type === 'selected') {
    if (selectedRows.length === 0) {
      enqueueSnackbar(t('请先选中'), { variant: 'info' })
      return
    }

    fireExport({
      exportParams: {
        bizType,
        jobName: 'NDR',
      },
    })
  }

  if (type === 'all') {
    fireExport({
      exportParams: {
        bizType,
        jobName: 'NDR',
      },
    })
  }
}`;

console.log('=== 调试复杂度分析流程 ===\n');

async function debugAnalysisFlow() {
  try {
    // 创建临时文件以便代码框架生成器能够访问
    const testFilePath = './debug-test-file.ts';
    writeFileSync(testFilePath, testCode);
    
    const ast = parseSync(testCode, {
      syntax: 'typescript',
      tsx: false,
      target: 'es2020',
    });
    
    const detailCollector = new DetailCollector();
    const visitor = SemanticComplexityVisitorFactory.createComplete(testCode, detailCollector);
    
    // 访问AST
    visitor.visit(ast);
    
    // 获取详细步骤
    const functionResults = visitor.getFunctionResults();
    console.log(`找到 ${functionResults.size} 个函数:\n`);
    
    for (const [funcName, result] of functionResults) {
      console.log(`函数: ${funcName}`);
      console.log(`复杂度: ${result.complexity}`);
      console.log(`详细步骤数量: ${result.details?.length || 0}`);
      
      if (result.details) {
        for (let i = 0; i < result.details.length; i++) {
          const step = result.details[i];
          console.log(`\n  步骤 ${i + 1}:`);
          console.log(`    行号: ${step.line}`);
          console.log(`    描述: ${step.description}`);
          console.log(`    节点类型: ${step.nodeType || '未知'}`);
          console.log(`    是否有span: ${step.span ? '✅' : '❌'}`);
          
          if (step.span) {
            console.log(`    Span: ${step.span.start}-${step.span.end}`);
            const spanText = testCode.slice(step.span.start, step.span.end);
            const firstLine = spanText.split('\n')[0];
            console.log(`    Span内容: "${firstLine}"`);
            
            // 测试代码框架生成
            const frameGenerator = new CodeFrameGenerator();
            const frameResult = await frameGenerator.generateFrameFromSpan(
              testFilePath,
              step.span,
              {},
              step.nodeType
            );
            
            console.log(`    框架生成成功: ${frameResult.success ? '✅' : '❌'}`);
            if (frameResult.success) {
              const lines = frameResult.frame.split('\n');
              const targetLine = lines.find(line => line.includes('>'));
              console.log(`    框架内容: ${targetLine?.trim() || '未找到'}`);
            } else {
              console.log(`    框架生成错误: ${frameResult.error}`);
            }
          } else {
            console.log('    无Span信息，使用行列生成框架...');
            const frameGenerator = new CodeFrameGenerator();
            const frameResult = await frameGenerator.generateFrame(
              testFilePath,
              step.line,
              step.column,
              {}
            );
            
            console.log(`    框架生成成功: ${frameResult.success ? '✅' : '❌'}`);
            if (frameResult.success) {
              const lines = frameResult.frame.split('\n');
              const targetLine = lines.find(line => line.includes('>'));
              console.log(`    框架内容: ${targetLine?.trim() || '未找到'}`);
            } else {
              console.log(`    框架生成错误: ${frameResult.error}`);
            }
          }
        }
      }
      
      console.log('\n' + '='.repeat(50) + '\n');
    }
    
  } catch (error) {
    console.error('调试失败:', error);
  }
}

// 使用async包装器
debugAnalysisFlow();