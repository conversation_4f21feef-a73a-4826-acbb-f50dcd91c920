/**
 * 箭头函数定位功能演示脚本 - Task 6
 * 演示各种箭头函数形式的精确定位能力
 */

import { ComplexityVisitor } from './src/core/complexity-visitor';
import { ASTParser } from './src/core/parser';
import { PositionConverter } from './src/utils/position-converter';

/**
 * 在 AST 中查找所有箭头函数节点
 */
function findArrowFunctions(ast: any): any[] {
  const arrowFunctions: any[] = [];
  
  const traverse = (node: any) => {
    if (!node || typeof node !== 'object') return;
    
    if (node.type === 'ArrowFunctionExpression') {
      arrowFunctions.push(node);
    }
    
    // 递归遍历所有子节点
    for (const key in node) {
      if (key === 'span' || key === 'type') continue;
      
      const value = node[key];
      if (Array.isArray(value)) {
        value.forEach(traverse);
      } else if (value && typeof value === 'object') {
        traverse(value);
      }
    }
  };
  
  traverse(ast);
  return arrowFunctions;
}

/**
 * 测试箭头函数定位功能
 */
async function testArrowFunctionPositioning() {
  console.log('🎯 箭头函数精确定位功能演示 - Task 6\n');

  const testCases = [
    {
      name: '单参数箭头函数',
      code: `
function test() {
  const square = x => x * x;
  const isEven = n => n % 2 === 0;
  return square(5);
}
      `
    },
    {
      name: '多参数箭头函数',
      code: `
function test() {
  const add = (a, b) => a + b;
  const calculate = (x, y, z) => x * y + z;
  return add(1, 2);
}
      `
    },
    {
      name: '无参数箭头函数',
      code: `
function test() {
  const getValue = () => 42;
  const getTime = () => Date.now();
  return getValue();
}
      `
    },
    {
      name: '类型注解箭头函数',
      code: `
function test() {
  const typed: (x: number) => number = (x: number): number => x * 2;
  const generic = <T>(value: T): T => value;
  return typed(5);
}
      `
    },
    {
      name: '异步箭头函数',
      code: `
function test() {
  const fetchData = async (url: string) => await fetch(url);
  const processAsync = async () => Promise.resolve(42);
  return fetchData('/api/data');
}
      `
    },
    {
      name: '复杂嵌套箭头函数',
      code: `
function test() {
  const curry = a => b => c => a + b + c;
  const numbers = [1, 2, 3, 4, 5];
  const result = numbers
    .filter(x => x > 2)
    .map((x, index) => ({ value: x, index }))
    .reduce((acc, item) => acc + item.value, 0);
  return curry(1)(2)(3);
}
      `
    },
    {
      name: '解构参数箭头函数',
      code: `
function test() {
  const extractName = ({name, age}) => name;
  const getFirst = ([first, ...rest]) => first;
  const complex = ({items: [first, second]}) => first + second;
  return extractName({name: 'John', age: 30});
}
      `
    }
  ];

  const parser = new ASTParser();

  for (const testCase of testCases) {
    console.log(`📍 测试: ${testCase.name}`);
    console.log(`📝 代码:\n${testCase.code.trim()}\n`);

    try {
      const ast = await parser.parseCode(testCase.code, 'test.ts');
      const visitor = new ComplexityVisitor(testCase.code);
      const arrowFunctions = findArrowFunctions(ast);

      console.log(`🔍 找到 ${arrowFunctions.length} 个箭头函数:`);

      for (let i = 0; i < arrowFunctions.length; i++) {
        const arrowFunc = arrowFunctions[i];
        const position = visitor.findArrowFunctionPosition(arrowFunc);
        
        if (position !== null) {
          const lineCol = PositionConverter.spanToPosition(testCase.code, position);
          const lines = testCase.code.split('\n');
          const targetLine = lines[lineCol.line - 1];
          const beforeArrow = targetLine?.slice(0, lineCol.column) || '';
          const atArrow = targetLine?.slice(lineCol.column, lineCol.column + 2) || '';
          const afterArrow = targetLine?.slice(lineCol.column + 2) || '';
          
          console.log(`  ✅ 箭头函数 ${i + 1}: 位置 ${position} (行 ${lineCol.line}, 列 ${lineCol.column})`);
          console.log(`     "${beforeArrow}${atArrow}${afterArrow}"`);
          console.log(`     ${' '.repeat(beforeArrow.length)}^^`);
        } else {
          console.log(`  ❌ 箭头函数 ${i + 1}: 未找到位置`);
        }
      }
    } catch (error) {
      console.error(`❌ 解析失败:`, error);
    }

    console.log('─'.repeat(60) + '\n');
  }
}

// 运行演示
testArrowFunctionPositioning().catch(console.error);