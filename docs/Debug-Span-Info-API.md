# Debug Span Info API 文档

## 概览

Debug Span Info 功能为认知复杂度分析工具提供了 SWC 原始 span 信息的调试显示功能。该功能帮助开发者在调试模式下理解 SWC 解析器的匹配逻辑，快速判断是匹配逻辑问题还是代码框架定位问题。

## 核心特性

- **语义感知定位**: 基于四层策略回退系统的智能代码定位
- **安全转义**: 防止终端注入攻击的代码片段转义
- **优雅降级**: 三层错误处理策略确保功能稳定性
- **性能优化**: 仅在调试模式下启用，利用文件缓存机制
- **函数式设计**: 所有函数都是纯函数，无副作用

## API 参考

### 主要接口

#### `formatSpanDebugInfo(step, filePath, options?)`

生成完整的 SWC Span 调试信息。

**参数:**
- `step: DetailStep` - 详细步骤信息，包含 span 和节点类型
- `filePath: string` - 源文件路径
- `options?: CLIOptions` - CLI 选项配置

**返回值:**
- `Promise<string | null>` - 格式化的调试信息字符串，失败时返回 null

**使用示例:**
```typescript
const debugInfo = await formatSpanDebugInfo(step, '/path/to/file.ts', { debug: true });
if (debugInfo) {
  console.log(debugInfo);
}
```

**输出格式示例:**
```
🔍 SWC Span 调试信息:
  节点类型: IfStatement
  位置信息: span: 245-267, 行: 12, 列: 4
  代码片段: "if (condition) {"
```

### 工具函数

#### `extractSpanSourceCode(sourceCode, span)`

从源代码中提取 span 对应的代码片段。

**参数:**
- `sourceCode: string` - 完整的源代码内容
- `span: { start: number; end: number }` - SWC span 位置信息

**返回值:**
- `string` - 提取的代码片段

**使用示例:**
```typescript
const snippet = extractSpanSourceCode(sourceCode, { start: 100, end: 120 });
console.log(snippet); // "if (condition) {"
```

#### `sanitizeCodeSnippet(snippet, maxLength?)`

安全转义代码片段，防止终端注入攻击。

**参数:**
- `snippet: string` - 原始代码片段
- `maxLength?: number` - 最大长度限制，默认为 50

**返回值:**
- `string` - 经过转义和截取的安全代码片段

**使用示例:**
```typescript
const safe = sanitizeCodeSnippet('console.log("hello\nworld")', 20);
console.log(safe); // "console.log(\"hello\\n..."
```

#### `formatPositionInfo(span, position)`

格式化位置信息（行号和列号）。

**参数:**
- `span: { start: number; end: number }` - SWC span 位置信息
- `position: { line: number; column: number }` - 计算得出的行列位置

**返回值:**
- `string` - 格式化的位置信息字符串

**使用示例:**
```typescript
const info = formatPositionInfo(
  { start: 100, end: 120 },
  { line: 5, column: 10 }
);
console.log(info); // "span: 100-120, 行: 5, 列: 10"
```

### 数据类型

#### `SpanDebugInfo` 接口

```typescript
interface SpanDebugInfo {
  /** SWC 原始 span 位置 */
  readonly span: { start: number; end: number };
  /** AST 节点类型 */
  readonly nodeType: string;
  /** 从源码中提取的代码片段 */
  readonly sourceSnippet: string;
  /** 计算出的行列位置信息 */
  readonly position: { line: number; column: number };
  /** 是否有有效的 span 信息 */
  readonly hasValidSpan: boolean;
  /** 错误信息（如果有） */
  readonly errorMessage?: string;
}
```

## 错误处理策略

### 三层错误处理机制

1. **优雅降级**: 主函数内部异常捕获，返回降级调试信息
2. **边界条件处理**: 处理 span 信息缺失、文件读取失败等边界情况
3. **主流程保护**: 确保调试信息生成错误不影响主要功能

### 错误场景处理

| 错误场景 | 处理方式 | 输出内容 |
|---------|---------|----------|
| span 信息缺失 | 显示降级信息 | 节点类型 + "无可用 span 信息" |
| 文件读取失败 | 显示错误原因 | 节点类型 + 错误详情 |
| span 超出范围 | 显示警告信息 | span 数值 + 文件长度警告 |
| 位置计算失败 | 回退到原始位置 | 使用 step.line/column |

## 性能特性

- **条件执行**: 仅在 `--debug` 模式下启用
- **文件缓存**: 复用 `getGlobalFileCache()` 避免重复读取
- **内存安全**: 代码片段长度限制，防止内存耗尽
- **异步优化**: 使用 Promise 处理异步文件操作

## 安全特性

- **转义保护**: 转义所有控制字符和特殊字符
- **长度限制**: 默认截取 50 字符，防止输出过长
- **路径安全**: 避免泄露敏感文件系统路径
- **注入防护**: 防止终端注入攻击

## 集成使用

### CLI 参数组合

启用 debug span 信息显示需要以下参数组合：

```bash
complexity --debug --details --show-context
```

### 程序化使用

```typescript
import { formatSpanDebugInfo } from '@/utils/span-debug-info';

// 在 TextFormatter 中使用
if (options?.debug) {
  const debugInfo = await formatSpanDebugInfo(step, filePath, options);
  if (debugInfo) {
    // 将调试信息添加到输出中
    output = debugInfo + '\n' + output;
  }
}
```

## 最佳实践

### 调试信息使用建议

1. **开发阶段**: 使用调试信息验证 span 定位的准确性
2. **问题排查**: 通过 span 信息判断是解析器问题还是定位逻辑问题
3. **性能考虑**: 仅在需要调试时启用，避免生产环境使用
4. **日志记录**: 可以将调试信息写入日志文件供后续分析

### 错误处理最佳实践

1. **始终检查返回值**: `formatSpanDebugInfo` 可能返回 null
2. **优雅处理异常**: 不要让调试信息错误影响主要功能
3. **提供上下文**: 在错误日志中包含文件路径和步骤信息
4. **监控性能**: 关注调试模式下的性能影响

## 扩展指南

### 添加新的调试信息类型

1. 扩展 `SpanDebugInfo` 接口
2. 修改 `formatDebugInfoOutput` 函数
3. 更新相关的错误处理逻辑
4. 添加相应的测试用例

### 自定义格式化

```typescript
// 自定义调试信息格式化
function customFormatDebugInfo(debugInfo: SpanDebugInfo): string {
  // 实现自定义格式化逻辑
  return `Custom: ${debugInfo.nodeType} at ${debugInfo.position.line}:${debugInfo.position.column}`;
}
```

## 相关文档

- [Debug Span Info 设计文档](../specs/debug-span-info/design.md)
- [Debug Span Info 需求文档](../specs/debug-span-info/requirements.md)
- [CLI 使用指南](CLI-Usage-Guide.md)
- [API 参考](API.md)