const handleExport = (type: 'selected' | 'all') => {
  const exportParams = handleExportParams(formatedQueryParams)

  if (type === 'selected') {
    if (selectedRows.length === 0) {
      enqueueSnackbar(t('请先选中'), { variant: 'info' })
      return
    }

    fireExport({
      exportParams: {
        bizType,
        jobName: 'NDR',
      },
    })
  }

  if (type === 'all') {
    fireExport({
      exportParams: {
        bizType,
        jobName: 'NDR',
      },
    })
  }
}