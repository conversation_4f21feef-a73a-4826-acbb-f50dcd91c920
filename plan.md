# SWC Span 智能回退机制改进方案

## 问题概述

当前的 SWC span 智能回退机制在结构层面是健全的，但在语义层面存在不足，导致某些情况下位置定位不够准确。主要问题包括：

1. **SWC Parser 固有限制**：对于导出对象、类型定义、JSX 表达式等结构，span 定位可能不准确
2. **缺乏语义感知**：现有回退策略主要基于结构层次，缺乏对代码语义的理解
3. **节点类型特异性不足**：没有针对不同类型节点的特殊处理策略

## 当前架构分析

### 现有智能回退机制
位于 `src/core/complexity-visitor.ts` 中的多层级回退策略：

1. **节点自身 span 验证** (`isValidSpan()`)
2. **父节点回退** (`attemptParentSpanFallback()`)
   - 直接父节点 → 祖父节点 → 祖先链 → 类型推断
3. **默认回退** (`getDefaultSpan()`)
4. **错误回退** (返回 0)

### 核心组件
- `src/utils/position-converter.ts` - 位置转换核心逻辑
- `src/core/complexity-visitor.ts` - 主要的智能回退实现
- `src/core/complexity-visitor-refactored.ts` - 重构版本
- `src/core/parser.ts` - SWC 解析器封装

## 改进方案

### 1. 增强 PositionConverter 的语义感知能力

**目标文件**: `src/utils/position-converter.ts`

**新增方法**:
```typescript
/**
 * 智能位置转换，包含语义感知的回退机制
 */
public static spanToPositionWithSmartFallback(
  sourceCode: string, 
  spanStart: number,
  nodeType?: string,
  nodeContext?: any
): Position

/**
 * 判断是否为无意义行（空行、纯注释、只有括号等）
 */
private static isInsignificantLine(lineContent: string): boolean

/**
 * 寻找最近的有意义代码行
 */
private static findNearestMeaningfulLine(
  sourceCode: string, 
  currentPosition: Position, 
  nodeType?: string
): Position

/**
 * 寻找最后一个有意义的代码行
 */
private static findLastMeaningfulLine(sourceCode: string): Position
```

### 2. 实现节点类型特定的位置策略

**新增策略映射**:
```typescript
const NODE_POSITION_STRATEGIES = {
  'FunctionExpression': 'prefer-function-keyword',
  'ArrowFunctionExpression': 'prefer-arrow-or-params',
  'JSXElement': 'prefer-opening-tag',
  'JSXExpressionContainer': 'prefer-expression-content',
  'ConditionalExpression': 'prefer-condition-start',
  'IfStatement': 'prefer-if-keyword',
  'ForStatement': 'prefer-for-keyword',
  'WhileStatement': 'prefer-while-keyword',
  'SwitchStatement': 'prefer-switch-keyword',
  'TryStatement': 'prefer-try-keyword',
  'CatchClause': 'prefer-catch-keyword',
  'TypeAliasDeclaration': 'prefer-type-keyword',
  'InterfaceDeclaration': 'prefer-interface-keyword',
  'ClassDeclaration': 'prefer-class-keyword',
  'MethodDefinition': 'prefer-method-name',
  'PropertyDefinition': 'prefer-property-name',
  'VariableDeclarator': 'prefer-variable-name',
  'ExportDeclaration': 'prefer-export-keyword',
  'ImportDeclaration': 'prefer-import-keyword',
};
```

### 3. 改进 ComplexityVisitor 中的智能推断

**目标文件**: `src/core/complexity-visitor.ts`

**增强现有方法**:
- `inferSpanFromContext()` - 基于节点类型的智能推断
- `findControlFlowKeywordPosition()` - 改进关键字查找逻辑
- `findFunctionKeywordPosition()` - 增强函数位置查找

**新增专门方法**:
```typescript
private findJSXExpressionContentPosition(node: Node): number | null
private findArrowFunctionPosition(node: Node): number | null
private findConditionalExpressionPosition(node: Node): number | null
private findGenericMeaningfulPosition(node: Node): number | null
```

### 4. 改进边界情况处理

**当前处理**:
```typescript
if (spanStart < 0 || spanStart > sourceCode.length) {
  return { line: 1, column: 1 };
}
```

**改进为智能处理**:
```typescript
if (spanStart < 0) {
  return { line: 1, column: 1 };
}
if (spanStart >= sourceCode.length) {
  return this.findLastMeaningfulLine(sourceCode);
}
```

### 5. 特殊场景处理

#### JSX 表达式处理
- 识别 JSX 表达式容器中的逻辑代码
- 区分 JSX 标签和内部表达式
- 优先定位到实际的逻辑代码位置

#### 箭头函数处理
- 优先定位到参数列表
- 其次定位到箭头符号
- 处理单行和多行箭头函数的差异

#### 复杂嵌套结构处理
- 深度嵌套对象的属性定位
- 链式调用的准确定位
- 泛型和类型约束的位置处理

## 实现要求

### 兼容性要求
1. **API 兼容性**: 所有现有公共方法签名保持不变
2. **向后兼容性**: 现有调用代码无需修改
3. **性能要求**: 新机制不应显著影响性能
4. **错误恢复**: 智能机制失败时有可靠回退

### 实现优先级
1. **高优先级**: JSX 表达式和箭头函数位置准确性
2. **中优先级**: 复杂嵌套结构和类型定义
3. **低优先级**: 装饰器和高级 TypeScript 特性

### 测试要求
1. 为每种节点类型创建测试用例
2. 测试边界情况和错误恢复
3. 性能基准测试
4. 现有测试兼容性验证

## 预期效果

实现后应该能够：

1. **准确定位 JSX 表达式**中的逻辑代码位置
2. **正确处理箭头函数**和复杂嵌套结构
3. **智能跳过空行和注释**，定位到有意义的代码行
4. **为不同类型节点**提供最合适的位置策略
5. **保持现有功能**的稳定性和性能

## 风险评估

### 低风险
- 新增方法不影响现有功能
- 智能机制失败时有完整的回退策略

### 中风险
- 复杂的语义分析可能影响性能
- 需要充分测试各种边界情况

### 缓解措施
- 实现渐进式改进，优先处理常见场景
- 保持现有回退机制作为最后保障
- 添加性能监控和调试信息

## 实施计划

### 第一阶段：基础改进
1. 增强 `PositionConverter` 的边界处理
2. 实现基本的语义感知方法
3. 添加节点类型策略映射

### 第二阶段：专门处理
1. 实现 JSX 表达式专门处理
2. 改进箭头函数位置查找
3. 处理复杂嵌套结构

### 第三阶段：优化完善
1. 性能优化和缓存改进
2. 边界情况完善
3. 测试覆盖率提升

通过这个改进方案，可以显著提高 SWC span 定位的准确性，特别是在处理现代 JavaScript/TypeScript 复杂语法结构时的表现。
