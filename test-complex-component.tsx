/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  Ellipsis, enqueueSnackbar, useFireExport,
} from '@imile/components'

export default function ComplexComponent() {
  // 第39行 - 应该有一个条件语句
  if (someCondition) {
    console.log('test1');
    
    // 嵌套条件
    if (anotherCondition) {
      console.log('nested');
    }
  }
  
  // 第48行 - 循环
  for (let i = 0; i < 10; i++) {
    console.log('loop', i);
    
    // 循环内条件
    if (i > 5) {
      break;
    }
  }
  
  // 第56行 - while循环
  let counter = 0;
  while (counter < 5) {
    counter++;
    
    // 嵌套条件
    if (counter === 3) {
      continue;
    }
  }
  
  // 第66行 - 三元运算符
  const result = someValue ? 'yes' : 'no';
  
  // 第69行 - 复杂条件
  if (condition1 && condition2) {
    // 复杂逻辑
    if (condition3 || condition4) {
      return 'complex';
    }
  }
  
  return null;
}