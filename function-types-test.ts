// 测试各种函数类型的名称提取
class TestClass {
  // 类方法
  methodName() {
    if (true) return 1;
  }
  
  // 静态方法
  static staticMethod() {
    if (false) return 2;
  }
}

// 对象方法
const obj = {
  // 方法简写
  shortMethod() {
    if (true) return 3;
  },
  
  // 传统方法定义
  longMethod: function() {
    if (false) return 4;
  }
};

// 箭头函数赋值
const arrowFunction = () => {
  if (true) return 5;
};

export { TestClass, obj, arrowFunction };