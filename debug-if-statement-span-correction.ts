import { parseSync } from '@swc/core';

// 测试不同的 if 语句格式
const testCases = [
  `if (condition) { console.log('test'); }`,
  `if(condition){console.log('test');}`,
  `if (a > b) {
    return true;
  }`,
  `if (!data?.length) {
    throw new Error('No data');
  }`,
  `if (type === 'selected') {
    fireExport({ exportParams: { bizType } });
  }`
];

console.log('=== SWC IfStatement Span 偏移测试 ===\n');

testCases.forEach((code, index) => {
  console.log(`测试用例 ${index + 1}: ${code.slice(0, 30)}...`);
  console.log('─'.repeat(50));
  
  try {
    const ast = parseSync(code, {
      syntax: 'typescript',
      tsx: false,
      target: 'es2020',
    });
    
    // 查找 IfStatement 节点
    function findIfStatement(node: any): any {
      if (!node || typeof node !== 'object') return null;
      
      if (node.type === 'IfStatement') {
        return node;
      }
      
      for (const [key, value] of Object.entries(node)) {
        if (key === 'span' || key === 'type') continue;
        
        if (Array.isArray(value)) {
          for (const child of value) {
            const result = findIfStatement(child);
            if (result) return result;
          }
        } else if (value && typeof value === 'object') {
          const result = findIfStatement(value);
          if (result) return result;
        }
      }
      
      return null;
    }
    
    const ifNode = findIfStatement(ast);
    
    if (ifNode && ifNode.span) {
      const spanStart = ifNode.span.start;
      const spanEnd = ifNode.span.end;
      
      console.log(`  Span: ${spanStart}-${spanEnd}`);
      console.log(`  原始代码: "${code}"`);
      console.log(`  Span 代码: "${code.slice(spanStart, spanEnd)}"`);
      console.log(`  开头字符: "${code[spanStart]}" (应该是 'i' 还是 'f'?)`);
      
      // 检查是否需要修正
      const needsCorrection = code[spanStart] !== 'i' && code[spanStart - 1] === 'i';
      console.log(`  需要修正: ${needsCorrection ? '✅ 是' : '❌ 否'}`);
      
      if (needsCorrection) {
        console.log(`  修正后位置: ${spanStart - 1}`);
        console.log(`  修正后代码: "${code.slice(spanStart - 1, spanEnd)}"`);
      }
      
    } else {
      console.log('  ❌ 未找到 IfStatement 节点');
    }
    
  } catch (error) {
    console.log(`  ❌ 解析失败: ${error}`);
  }
  
  console.log('');
});

console.log('\n=== 修正逻辑实现 ===\n');

// 实现修正逻辑
function correctIfStatementSpan(sourceCode: string, node: any): number {
  if (node.type !== 'IfStatement' || !node.span) {
    return node.span?.start || 0;
  }
  
  const spanStart = node.span.start;
  
  // 检查当前位置是否是 'f'，前一个位置是否是 'i'
  if (spanStart > 0 && 
      sourceCode[spanStart] === 'f' && 
      sourceCode[spanStart - 1] === 'i') {
    return spanStart - 1; // 修正到 'i' 的位置
  }
  
  return spanStart; // 不需要修正
}

// 测试修正逻辑
console.log('测试修正逻辑：');
testCases.forEach((code, index) => {
  try {
    const ast = parseSync(code, {
      syntax: 'typescript',
      tsx: false,
      target: 'es2020',
    });
    
    function findAndCorrect(node: any): void {
      if (!node || typeof node !== 'object') return;
      
      if (node.type === 'IfStatement') {
        const originalSpan = node.span?.start || 0;
        const correctedSpan = correctIfStatementSpan(code, node);
        
        console.log(`  用例 ${index + 1}:`);
        console.log(`    原始 span: ${originalSpan} -> "${code[originalSpan]}"`);
        console.log(`    修正 span: ${correctedSpan} -> "${code[correctedSpan]}"`);
        console.log(`    修正后代码: "${code.slice(correctedSpan, correctedSpan + 10)}..."`);
        return;
      }
      
      for (const [key, value] of Object.entries(node)) {
        if (key === 'span' || key === 'type') continue;
        
        if (Array.isArray(value)) {
          value.forEach(child => findAndCorrect(child));
        } else if (value && typeof value === 'object') {
          findAndCorrect(value);
        }
      }
    }
    
    findAndCorrect(ast);
    
  } catch (error) {
    console.log(`  用例 ${index + 1}: 解析失败`);
  }
});