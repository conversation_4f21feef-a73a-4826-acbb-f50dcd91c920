import { parseSync } from '@swc/core';

// 单独测试每个 if 语句
const singleTests = [
  `if (condition) { console.log('test'); }`,
  `if(condition){console.log('test');}`,  
  `function test() {
  if (a > b) {
    return true;
  }
}`,
  `const fn = () => {
  if (!data?.length) {
    throw new Error('No data');
  }
}`,
  `const handleExport = (type) => {
  if (type === 'selected') {
    fireExport({
      exportParams: {
        bizType,
      }
    });
  }
}`
];

console.log('=== 独立测试每个 if 语句 ===\n');

singleTests.forEach((code, index) => {
  console.log(`\n测试 ${index + 1}:`);
  console.log(`代码:\n${code}`);
  console.log('─'.repeat(60));
  
  try {
    const ast = parseSync(code, {
      syntax: 'typescript',
      tsx: false,
      target: 'es2020',
    });
    
    function findIfStatements(node: any, path: string[] = []): any[] {
      const results: any[] = [];
      
      if (!node || typeof node !== 'object') return results;
      
      if (node.type === 'IfStatement' && node.span) {
        results.push({
          node,
          path: path.join(' -> '),
          span: node.span
        });
      }
      
      for (const [key, value] of Object.entries(node)) {
        if (key === 'span' || key === 'type') continue;
        
        if (Array.isArray(value)) {
          value.forEach((child, idx) => {
            if (child && typeof child === 'object') {
              results.push(...findIfStatements(child, [...path, `${key}[${idx}]`]));
            }
          });
        } else if (value && typeof value === 'object') {
          results.push(...findIfStatements(value, [...path, key]));
        }
      }
      
      return results;
    }
    
    const ifStatements = findIfStatements(ast);
    
    if (ifStatements.length === 0) {
      console.log('❌ 未找到 IfStatement 节点');
    } else {
      ifStatements.forEach((ifStmt, idx) => {
        const { span, path } = ifStmt;
        
        // 计算行号
        const linesBeforeSpan = code.slice(0, span.start).split('\n');
        const lineNumber = linesBeforeSpan.length;
        
        console.log(`\n  IfStatement ${idx + 1} (第 ${lineNumber} 行):`);
        console.log(`    路径: ${path}`);
        console.log(`    Span: ${span.start}-${span.end}`);
        
        // 检查 span 指向的字符和周围环境
        const charAtSpan = code[span.start];
        const charBefore = span.start > 0 ? code[span.start - 1] : null;
        const charAfter = code[span.start + 1];
        
        console.log(`    Span 字符: "${charAtSpan}"`);
        console.log(`    前一字符: ${charBefore ? `"${charBefore}"` : 'null'}`);
        console.log(`    后一字符: "${charAfter}"`);
        
        // 显示 span 对应的代码片段
        const spanCode = code.slice(span.start, span.end);
        const firstLine = spanCode.split('\n')[0];
        console.log(`    Span 首行: "${firstLine}"`);
        
        // 显示实际的源代码行
        const sourceLines = code.split('\n');
        const actualLine = sourceLines[lineNumber - 1];
        console.log(`    实际源码行: "${actualLine}"`);
        
        // 检查是否需要修正
        const needsCorrection = charAtSpan === 'f' && charBefore === 'i';
        console.log(`    需要修正: ${needsCorrection ? '✅ 是' : '❌ 否'}`);
        
        if (needsCorrection) {
          const correctedCode = code.slice(span.start - 1, span.end);
          const correctedFirstLine = correctedCode.split('\n')[0];
          console.log(`    修正后首行: "${correctedFirstLine}"`);
        }
        
        // 为调试显示更多上下文
        const contextStart = Math.max(0, span.start - 10);
        const contextEnd = Math.min(code.length, span.start + 10);
        const context = code.slice(contextStart, contextEnd);
        console.log(`    上下文: "${context}"`);
      });
    }
    
  } catch (error) {
    console.log(`❌ 解析失败: ${error.message}`);
  }
});