// 测试函数识别的简化版本
const TestComponent = () => {
  // 这是一个箭头函数，应该作为独立函数处理
  const handleExport = (type: 'selected' | 'all') => {
    if (type === 'selected') {
      if (selectedRows.length === 0) {
        return;
      }
    }

    if (type === 'all') {
      if (!dataSource?.length) {
        return;
      }
    }
  }

  // 这是另一个箭头函数
  const onChange = (rows: any[]) => {
    if (rows) {
      setSelectedRows(rows);
    }
  }

  return <div>Test</div>;
}

export default TestComponent;