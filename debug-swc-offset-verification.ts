import { parseSync } from '@swc/core';
import { PositionConverter } from './src/utils/position-converter';

const testCode = `const handleExport = (type: 'selected' | 'all') => {
  const exportParams = handleExportParams(formatedQueryParams)

  if (type === 'selected') {
    if (selectedRows.length === 0) {
      enqueueSnackbar(t('请先选中'), { variant: 'info' })
      return
    }

    fireExport({
      exportParams: {
        bizType,
        jobName: 'NDR',
      },
    })
  }

  if (type === 'all') {
    fireExport({
      exportParams: {
        bizType,
        jobName: 'NDR',
      },
    })
  }
}`;

console.log('=== 验证 SWC Offset 修正逻辑 ===\n');

try {
  const ast = parseSync(testCode, {
    syntax: 'typescript',
    tsx: false,
    target: 'es2020',
  });
  
  function findIfStatements(node: any): any[] {
    const results: any[] = [];
    
    if (!node || typeof node !== 'object') return results;
    
    if (node.type === 'IfStatement' && node.span) {
      results.push(node);
    }
    
    for (const [key, value] of Object.entries(node)) {
      if (key === 'span' || key === 'type') continue;
      
      if (Array.isArray(value)) {
        value.forEach(child => {
          if (child && typeof child === 'object') {
            results.push(...findIfStatements(child));
          }
        });
      } else if (value && typeof value === 'object') {
        results.push(...findIfStatements(value));
      }
    }
    
    return results;
  }
  
  const ifStatements = findIfStatements(ast);
  
  console.log(`找到 ${ifStatements.length} 个 IfStatement 节点:\n`);
  
  ifStatements.forEach((node, idx) => {
    const span = node.span;
    console.log(`IfStatement ${idx + 1}:`);
    console.log(`  原始 span: ${span.start}-${span.end}`);
    console.log(`  原始代码片段: "${testCode.slice(span.start, span.end).split('\n')[0]}"`);
    console.log(`  span.start 字符: "${testCode[span.start]}"`);
    
    // 测试旧的 fastSpanToPosition
    const oldPosition = PositionConverter.fastSpanToPosition(testCode, span.start);
    console.log(`  旧方法位置: Line ${oldPosition.line}, Col ${oldPosition.column}`);
    
    // 测试新的智能 spanToPosition (包含SWC修正)
    const newPosition = PositionConverter.spanToPosition(testCode, span.start, undefined, undefined, 'IfStatement');
    console.log(`  新方法位置: Line ${newPosition.line}, Col ${newPosition.column}`);
    
    // 手动验证修正逻辑
    const needsCorrection = testCode[span.start] === 'f' && 
                           span.start > 0 && 
                           testCode[span.start - 1] === 'i';
    console.log(`  需要修正: ${needsCorrection ? '✅ 是' : '❌ 否'}`);
    
    if (needsCorrection) {
      const correctedSpan = span.start - 1;
      console.log(`  修正后 span: ${correctedSpan}`);
      console.log(`  修正后代码: "${testCode.slice(correctedSpan, span.end).split('\n')[0]}"`);
    }
    
    // 获取实际的行内容
    const actualLine = PositionConverter.getLineContent(testCode, newPosition.line);
    console.log(`  实际行内容: "${actualLine}"`);
    
    console.log('');
  });
  
} catch (error) {
  console.error('解析失败:', error);
}