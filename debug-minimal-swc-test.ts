import { parseSync } from '@swc/core';

// 创建一个简化的测试用例，模拟问题文件的结构
const testCode = `
const handleExport = (type: 'selected' | 'all') => {
  const exportParams = handleExportParams(formatedQueryParams)

  if (type === 'selected') {
    if (selectedRows.length === 0) {
      enqueueSnackbar(t('请先选中'), { variant: 'info' })
      return
    }

    fireExport({
      exportParams: {
        bizType,
        jobName: 'NDR',
        queryContent: {
          ...exportParams,
          ids: selectedRows.map((i) => i.waybillNo).join(','),
        },
        system: 'tms',
      },
    })
  }

  if (type === 'all') {
    if (!tableProps.dataSource?.length) {
      enqueueSnackbar(t('未查询到数据'), { variant: 'info' })
      return
    }

    fireExport({
      exportParams: {
        bizType,
        jobName: 'NDR',
        queryContent: { ...exportParams },
        system: 'tms',
      },
    })
  }
}
`;

console.log('测试 SWC 解析行为...');

try {
  const ast = parseSync(testCode, {
    syntax: 'typescript',
    tsx: false,
    target: 'es2020',
  });
  
  console.log('\n===== AST 结构分析 =====');
  
  // 递归查找所有节点类型
  function analyzeNodeTypes(node: any, depth = 0, path: string[] = []): void {
    if (!node || typeof node !== 'object') return;
    
    const indent = '  '.repeat(depth);
    const nodeType = node.type || 'unknown';
    
    // 计算该节点在源码中的行号（如果有span）
    let lineInfo = '';
    if (node.span) {
      const beforeSpan = testCode.slice(0, node.span.start);
      const lineNumber = beforeSpan.split('\n').length;
      const spanCode = testCode.slice(node.span.start, node.span.end);
      const firstLine = spanCode.split('\n')[0];
      lineInfo = ` [L${lineNumber}: "${firstLine.trim().slice(0, 30)}..."]`;
    }
    
    console.log(`${indent}${nodeType}${lineInfo} (${path.join(' -> ')})`);
    
    // 特别关注 IfStatement 节点
    if (nodeType === 'IfStatement') {
      console.log(`${indent}  🚨 找到 IfStatement 节点！`);
      if (node.span) {
        const spanCode = testCode.slice(node.span.start, node.span.end);
        const firstLine = spanCode.split('\n')[0].trim();
        console.log(`${indent}  📝 实际代码: "${firstLine}"`);
        
        // 检查是否真的是 if 语句
        if (!firstLine.startsWith('if ') && !firstLine.includes('if (')) {
          console.log(`${indent}  ⚠️  这不是真正的 if 语句！`);
        }
      }
    }
    
    // 递归遍历子节点，但限制深度避免无限递归
    if (depth < 10) {
      for (const [key, value] of Object.entries(node)) {
        if (key === 'span' || key === 'type') continue;
        
        if (Array.isArray(value)) {
          value.forEach((child, index) => {
            if (child && typeof child === 'object') {
              analyzeNodeTypes(child, depth + 1, [...path, `${key}[${index}]`]);
            }
          });
        } else if (value && typeof value === 'object') {
          analyzeNodeTypes(value, depth + 1, [...path, key]);
        }
      }
    }
  }
  
  analyzeNodeTypes(ast, 0, ['root']);
  
  console.log('\n===== 具体 IfStatement 分析 =====');
  
  // 专门查找 IfStatement 节点
  function findIfStatements(node: any, path: string[] = []): any[] {
    const results: any[] = [];
    
    if (!node || typeof node !== 'object') return results;
    
    if (node.type === 'IfStatement' && node.span) {
      const beforeSpan = testCode.slice(0, node.span.start);
      const lineNumber = beforeSpan.split('\n').length;
      const spanCode = testCode.slice(node.span.start, node.span.end);
      const lines = spanCode.split('\n');
      const firstLine = lines[0].trim();
      
      results.push({
        line: lineNumber,
        span: node.span,
        path: path.join(' -> '),
        firstLine,
        fullSpanCode: spanCode.slice(0, 100) + (spanCode.length > 100 ? '...' : ''),
        isRealIf: firstLine.startsWith('if ') || firstLine.includes('if (')
      });
    }
    
    // 递归查找
    for (const [key, value] of Object.entries(node)) {
      if (key === 'span' || key === 'type') continue;
      
      if (Array.isArray(value)) {
        value.forEach((child, index) => {
          if (child && typeof child === 'object') {
            results.push(...findIfStatements(child, [...path, `${key}[${index}]`]));
          }
        });
      } else if (value && typeof value === 'object') {
        results.push(...findIfStatements(value, [...path, key]));
      }
    }
    
    return results;
  }
  
  const ifStatements = findIfStatements(ast);
  
  ifStatements.forEach((ifNode, index) => {
    console.log(`\n${index + 1}. IfStatement 在第 ${ifNode.line} 行`);
    console.log(`   路径: ${ifNode.path}`);
    console.log(`   首行: "${ifNode.firstLine}"`);
    console.log(`   Span: ${ifNode.span.start}-${ifNode.span.end}`);
    console.log(`   真实的 if: ${ifNode.isRealIf ? '✅ 是' : '❌ 否'}`);
    console.log(`   代码片段:\n${ifNode.fullSpanCode.split('\n').map((line, idx) => `     ${idx + 1}: ${line}`).join('\n')}`);
  });
  
} catch (error) {
  console.error('解析失败:', error);
}