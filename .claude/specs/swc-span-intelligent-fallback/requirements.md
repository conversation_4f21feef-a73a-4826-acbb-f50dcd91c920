# Requirements - SWC Span 智能回退机制改进

## 介绍

当前的 SWC span 智能回退机制在结构层面健全，但在语义层面存在不足。在处理现代 JavaScript/TypeScript 语法（如 JSX、箭头函数、复杂类型定义）时，代码位置定位不够精确，影响了开发者体验和工具的准确性。本功能旨在将纯粹的结构化回退升级为一套包含语义感知的、基于 AST 节点类型和上下文的多层级智能定位体系。

## 与产品愿景的契合

本改进与产品愿景完美契合：

- **准确性提升**: 实现更准确的复杂度计算，提供精确的代码位置定位
- **现代语法支持**: 优化对 JSX 和现代 JavaScript 特性的支持，符合产品对前端生态的专门优化目标
- **开发者体验**: 提供更符合直觉的语义化位置，改善工具的准确性和用户体验
- **技术优势**: 保持基于 SWC 解析器的高性能特性，同时提升定位精度

## 现有代码库分析

### 可复用组件
- **PositionConverter**: 已实现基础位置转换和缓存系统，可作为 L0 层基础
- **ComplexityVisitor**: 已有基础 span 验证机制，需要增强为 L1 层策略
- **BaseVisitor**: 提供父节点栈管理，为结构化回退提供基础

### 需要增强的部分
- PositionConverter 缺乏语义分析能力
- ComplexityVisitor 的位置策略过于简单
- 缺乏基于 Token 流的精确关键字查找

## 功能需求

### 需求 1: L0 基础语义位置服务增强

**用户故事:** 作为复杂度分析引擎，我需要高效的基础语义位置服务，以便为上层提供智能位置转换和语义分析能力。

#### 验收标准

1. WHEN span 超出文件边界 THEN 系统 SHALL 智能回退到最后一个有意义的代码行
2. WHEN 遇到无意义行（空行、纯注释）THEN 系统 SHALL 跳过并找到最近的有意义代码行
3. WHEN 执行位置转换 THEN 系统 SHALL 使用文件路径作为缓存键以提升性能
4. WHEN 分析同一文件多次 THEN 系统 SHALL 复用行分析结果缓存

### 需求 2: L1 节点类型策略映射系统

**用户故事:** 作为 AST 访问者，我需要基于节点类型的智能定位策略，以便为不同的代码结构提供最精确的位置信息。

#### 验收标准

1. WHEN 遇到函数表达式 THEN 系统 SHALL 定位到 'function' 关键字位置
2. WHEN 遇到箭头函数 THEN 系统 SHALL 定位到参数列表或箭头符号位置
3. WHEN 遇到 JSX 元素 THEN 系统 SHALL 定位到开放标签的起始位置
4. WHEN 遇到控制流语句 THEN 系统 SHALL 定位到相应关键字位置（如 'if', 'while', 'for'）
5. WHEN 传统字符串搜索失败 THEN 系统 SHALL 使用 SWC Token 流进行精确查找

### 需求 3: JSX 和现代语法精确定位

**用户故事:** 作为开发者，我需要工具精确定位 JSX 和现代 JavaScript 语法中的复杂度位置，以便快速理解和优化代码。

#### 验收标准

1. WHEN 分析 JSX 表达式 THEN 系统 SHALL 定位到表达式内容而非 JSX 标签
2. WHEN 分析 JSX 事件处理器 THEN 系统 SHALL 定位到处理函数的实际位置
3. WHEN 分析三元运算符 THEN 系统 SHALL 定位到条件表达式的起始位置
4. WHEN 分析链式调用 THEN 系统 SHALL 定位到调用链的起始位置
5. WHEN 分析类型定义 THEN 系统 SHALL 定位到类型注解的实际位置

### 需求 4: 智能回退和错误恢复

**用户故事:** 作为分析系统，我需要健壮的回退机制，以便在位置定位失败时仍能提供有效的位置信息。

#### 验收标准

1. WHEN 节点策略失败 THEN 系统 SHALL 回退到父节点信息
2. WHEN 父节点信息无效 THEN 系统 SHALL 回退到默认位置
3. WHEN 所有策略都失败 THEN 系统 SHALL 记录详细错误信息并提供保底位置
4. WHEN 出现位置转换异常 THEN 系统 SHALL 优雅处理并继续分析其他节点

### 需求 5: 性能优化和缓存增强

**用户故事:** 作为大型项目分析工具，我需要高效的位置分析性能，以便在分析超大文件时保持响应速度。

#### 验收标准

1. WHEN 分析超过 5000 行的文件 THEN 系统 SHALL 在 5 秒内完成位置分析
2. WHEN 重复分析相同文件 THEN 系统 SHALL 利用缓存减少 80% 的计算时间
3. WHEN Token 查找 THEN 系统 SHALL 限制搜索范围在节点 span 内以优化性能
4. WHEN 内存使用超过阈值 THEN 系统 SHALL 自动清理缓存并记录性能指标

## 非功能性需求

### 性能需求
- 位置转换操作耗时不超过原有实现的 120%
- 大文件（>5000 行）分析完成时间不超过 5 秒
- 缓存命中率达到 85% 以上
- Token 查找限制在节点 span 范围内，避免全文件扫描

### 可靠性需求
- 新位置策略的准确率达到 95% 以上
- 错误恢复机制确保部分失败不影响整体分析
- 保留现有父节点回退机制作为最后防线
- 100% 向后兼容现有 API

### 可维护性需求
- 分层架构清晰，L0 和 L1 职责明确
- 策略映射表使用函数/对象设计，易于扩展
- 详细的性能基准测试和监控
- 充分的单元测试覆盖率（>90%）

### 扩展性需求
- 支持未来新增的 AST 节点类型
- 策略系统支持运行时配置和热更新
- Token 查找机制支持不同的解析器后端
- 缓存系统支持自定义缓存策略

## 技术约束

### SWC 兼容性
- 必须与当前使用的 SWC 版本兼容
- Token 查找需要验证 SWC tokenize API 的可用性
- 保持与现有 span 信息结构的兼容性

### Node.js 兼容性
- 遵循项目的双运行时策略（开发用 Bun，生产兼容 Node.js）
- 所有新增代码必须在 Node.js 18+ 环境中正常运行
- 不得使用任何 Bun 特定的 API

### 内存和性能约束
- 新增内存使用不超过原实现的 150%
- 支持文件级缓存失效机制
- 在超大项目中保持稳定的内存使用

## 验收标准总结

该功能被认为完成，当且仅当：

1. ✅ L0 层 PositionConverter 实现所有语义分析方法
2. ✅ L1 层 ComplexityVisitor 实现基于节点类型的策略映射
3. ✅ JSX 和箭头函数定位精确度达到 95% 以上
4. ✅ 所有现有测试保持通过
5. ✅ 新功能的单元测试覆盖率达到 90% 以上
6. ✅ 性能基准测试显示改进符合预期
7. ✅ 在真实项目中验证改进效果