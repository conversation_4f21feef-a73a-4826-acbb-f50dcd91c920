# Design - SWC Span 智能回退机制改进

## 概览

SWC Span 智能回退机制改进采用分层架构设计，将现有的结构化 span 修正升级为语义感知的智能定位系统。设计包含两个核心层级：L0 基础位置转换层（PositionConverter 增强）和 L1 AST 节点策略层（ComplexityVisitor 增强）。该设计专门优化现代 JavaScript/TypeScript 语法（JSX、箭头函数、复杂类型定义）的定位精度，同时保持向后兼容性和高性能特性。

## 技术标准符合性

### 技术标准 (tech.md)

**架构原则符合性：**
- **简单性优先**: 采用清晰的分层设计，避免过度工程化
- **函数式接口**: L0 层提供纯函数式的位置转换 API
- **访问者模式**: L1 层基于现有访问者模式进行增强
- **单一职责**: L0 负责基础位置转换，L1 负责语义策略
- **开闭原则**: 策略映射表支持扩展新节点类型

**Node.js 兼容性：**
- 所有新增功能使用标准 Node.js API
- 禁用任何 Bun 特定功能
- 保持与 Node.js 18+ 的完全兼容
- SWC tokenize API 使用标准 npm 包接口

**性能要求：**
- 异步优先设计，支持大文件流式处理
- 智能缓存策略，减少重复计算
- 内存管理优化，支持文件级缓存失效
- 错误恢复机制，确保部分失败不影响整体

### 项目结构 (structure.md)

**模块组织：**
- `src/utils/position-converter.ts`: L0 层增强，新增语义分析方法
- `src/core/complexity-visitor.ts`: L1 层增强，新增策略映射系统
- `src/core/types.ts`: 新增相关类型定义
- `src/__test__/utils/`: L0 层单元测试
- `src/__test__/core/`: L1 层单元测试

**命名约定：**
- 类名使用 PascalCase: `PositionConverter`, `ComplexityVisitor`
- 方法名使用 camelCase: `spanToPositionWithSmartFallback`, `findKeywordPosition`
- 常量使用 UPPER_CASE: `NODE_POSITION_STRATEGIES`

## 架构设计

### 分层架构概览

```mermaid
graph TD
    A[ComplexityVisitor - L1层] --> B[PositionConverter - L0层]
    A --> C[NODE_POSITION_STRATEGIES]
    A --> D[Token查找系统]
    B --> E[语义分析缓存]
    B --> F[基础位置转换]
    C --> G[函数策略]
    C --> H[JSX策略]
    C --> I[控制流策略]
    D --> J[SWC Token流]
    E --> K[文件级缓存]
    E --> L[行分析缓存]
```

### 核心设计原则

1. **分层职责分离**
   - L0 层：基础位置转换和语义分析
   - L1 层：基于节点类型的智能策略

2. **向后兼容性**
   - 保持现有 API 不变
   - 新功能作为增强而非替换

3. **性能优化**
   - 多级缓存策略
   - Token 查找范围限制
   - 错误快速回退

## 组件和接口

### L0 层 - PositionConverter 增强

**目的：** 提供带语义感知的基础位置转换服务

**新增公共接口：**
```typescript
// 核心增强 API
static spanToPositionWithSmartFallback(
  sourceCode: string, 
  spanStart: number,
  filePath: string
): Position;

// 语义分析 API  
static isInsignificantLine(lineContent: string): boolean;
static findNearestMeaningfulLine(
  sourceCode: string, 
  startLine: number,
  direction: 'up' | 'down'
): Position;
static findLastMeaningfulLine(sourceCode: string): Position;
```

**内部缓存系统：**
```typescript
private static lineAnalysisCache: Map<string, Map<number, boolean>>;
```

**依赖关系：** 无新增外部依赖，基于现有 Node.js API

### L1 层 - ComplexityVisitor 增强

**目的：** 实现基于节点类型和上下文的顶层定位策略

**新增核心接口：**
```typescript
// 策略映射表
private static NODE_POSITION_STRATEGIES: Map<NodeType, PositionStrategy>;

// Token 查找系统
private findKeywordPosition(node: Node, keyword: string): number | null;
private findArrowFunctionPosition(node: Node): number | null;
private findJsxOpeningTagPosition(node: Node): number | null;
private findJSXExpressionContentPosition(node: Node): number | null;
```

**策略函数类型：**
```typescript
type PositionStrategy = (node: Node) => number | null;
```

**依赖关系：**
- 依赖 L0 层 PositionConverter
- 依赖 SWC tokenize API
- 依赖现有 BaseVisitor 父节点栈

### Token 查找系统

**目的：** 基于 SWC Token 流的精确关键字定位

**核心接口：**
```typescript
interface TokenSearchResult {
  position: number | null;
  confidence: 'high' | 'medium' | 'low';
  method: 'token-stream' | 'string-search' | 'fallback';
}

private tokenize(sourceCode: string, span: SpanRange): Token[];
private findTokenByType(tokens: Token[], tokenType: string): Token | null;
```

**搜索策略：**
1. 优先使用 SWC Token 流精确查找
2. 回退到优化的字符串搜索
3. 最后使用传统 indexOf 方法

## 数据模型

### 位置策略映射

```typescript
interface PositionStrategyEntry {
  nodeType: string;
  strategy: PositionStrategy;
  priority: number;
  fallbackStrategy?: PositionStrategy;
}

interface PositionStrategy {
  (node: Node, context: StrategyContext): PositionResult;
}

interface StrategyContext {
  sourceCode: string;
  parentStack: Node[];
  tokenizer?: TokenizerInstance;
}

interface PositionResult {
  position: number | null;
  confidence: 'high' | 'medium' | 'low';
  method: string;
}
```

### 语义分析缓存

```typescript
interface LineAnalysisCacheEntry {
  isSignificant: boolean;
  contentType: 'code' | 'comment' | 'empty' | 'mixed';
  timestamp: number;
}

interface FileAnalysisCache {
  filePath: string;
  contentHash: string;
  lineAnalysis: Map<number, LineAnalysisCacheEntry>;
  lastAccess: number;
}
```

### Token 信息模型

```typescript
interface TokenInfo {
  type: string;
  value: string;
  span: { start: number; end: number };
  line: number;
  column: number;
}

interface TokenSearchRange {
  start: number;
  end: number;
  maxTokens?: number;
}
```

## 错误处理

### 错误场景和处理策略

1. **SWC Token 解析失败**
   - **处理：** 回退到字符串搜索方法
   - **用户影响：** 定位精度略有下降，但不影响基本功能
   - **记录：** 记录到详细日志中供调试使用

2. **Span 超出文件边界**
   - **处理：** 使用 `findLastMeaningfulLine` 智能回退
   - **用户影响：** 获得有意义的代码位置而非错误位置
   - **记录：** 记录边界情况用于后续优化

3. **缓存系统故障**
   - **处理：** 禁用缓存，直接计算位置信息
   - **用户影响：** 性能轻微下降，功能完全正常
   - **记录：** 记录缓存故障，触发自动清理

4. **策略系统失败**
   - **处理：** 逐级回退到父节点、默认位置
   - **用户影响：** 确保始终有有效位置返回
   - **记录：** 详细记录失败策略，用于后续改进

5. **内存不足**
   - **处理：** 触发缓存清理，释放不必要的内存
   - **用户影响：** 短暂的性能影响，然后恢复正常
   - **记录：** 监控内存使用情况

### 错误恢复机制

```typescript
interface ErrorRecoveryChain {
  primary: PositionStrategy;
  fallback: PositionStrategy[];
  emergency: () => number; // 保底位置生成器
}
```

## 测试策略

### 单元测试

**L0 层测试（PositionConverter）：**
- 测试语义分析方法的准确性
- 测试缓存系统的命中率和失效机制
- 测试边界条件处理
- 测试性能优化效果

**L1 层测试（ComplexityVisitor）：**
- 测试每个策略函数的定位准确性
- 测试策略映射表的正确性
- 测试 Token 查找系统的可靠性
- 测试错误回退机制

**测试覆盖目标：**
- 行覆盖率 > 90%
- 分支覆盖率 > 85%
- 函数覆盖率 > 95%

### 集成测试

**完整流程测试：**
- 从 AST 节点到最终位置的完整转换流程
- 多种节点类型的混合场景测试
- 大文件和复杂语法的综合测试
- 缓存系统与主流程的集成测试

**兼容性测试：**
- 与现有 ComplexityVisitor API 的兼容性
- 与不同版本 SWC 的兼容性
- Node.js 不同版本的兼容性测试

### 端到端测试

**真实场景测试：**
- 大型 React 项目的 JSX 定位精度
- 复杂 TypeScript 项目的类型定义定位
- 箭头函数和现代语法的定位准确性
- 边界情况和异常场景的处理

**性能基准测试：**
- 大文件（>5000 行）的处理性能
- 缓存命中率和内存使用情况
- 与原始实现的性能对比
- 并发场景下的稳定性测试

### 测试数据和夹具

**测试样本：**
- 各种 JSX 复杂度场景
- 不同类型的箭头函数
- 嵌套的控制流结构
- 边界条件和错误情况

**性能测试数据：**
- 不同大小的代码文件（1K-10K+ 行）
- 不同复杂度的 AST 结构
- 各种现代 JavaScript/TypeScript 语法

## 实施计划

### 第一阶段：基础能力建设
- 实现 L0 层语义分析方法
- 搭建策略映射表框架
- 基础单元测试覆盖

### 第二阶段：核心痛点攻坚  
- 实现 Token 查找系统
- 解决 JSX 和箭头函数定位问题
- 集成测试和验证

### 第三阶段：全面覆盖与优化
- 完善所有节点类型支持
- 性能优化和基准测试
- 文档和最终验证

该设计确保了系统的健壮性、可维护性和扩展性，同时保持了与现有架构的兼容性。