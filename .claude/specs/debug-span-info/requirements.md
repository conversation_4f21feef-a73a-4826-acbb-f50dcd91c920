# Debug Span Info - Requirements

## Introduction

当前在代码复杂度分析的详细模式中，用户只能看到代码上下文和复杂度计算步骤，但无法了解SWC解析器匹配的原始span信息。这导致在调试定位问题时，用户难以判断是SWC匹配逻辑有误还是代码框架定位有误。该功能将在详细输出中新增原始span关键信息区域，帮助开发者更好地理解和调试复杂度定位过程。

## 代码库分析总结

通过分析现有代码库，发现：

**可复用的组件：**
- `--debug` 参数已在CLI中存在（src/cli/index.ts）
- `CodeFrameGenerator` 已有完整的代码上下文生成机制（src/utils/code-frame-generator.ts）
- `TextFormatter` 已有详细步骤格式化和上下文显示逻辑（src/formatters/text.ts:294-399）
- `DetailStep` 类型已包含 span 和 nodeType 信息（src/core/types.ts）

**集成点：**
- 主要修改点在 `TextFormatter.generateContextFrame()` 方法（第372-399行）
- 需要扩展调试信息显示逻辑，当前只有简单的调试日志（第387-389行）
- 可利用现有的 `step.span` 属性和文件路径信息

## Alignment with Product Vision

此功能直接支持产品文档中提到的**"调试可视化"**核心价值：
- **调试友好**: 内置调试系统和可视化工具，便于问题排查
- **开发体验优化**: 为开发者提供更详尽的定位信息，提升调试效率
- **准确性保障**: 帮助验证和改进SWC span定位的准确性

符合技术文档中强调的"语义定位系统的四层架构"调试需求。

## Requirements

### Requirement 1: 调试模式下显示SWC原始span信息

**User Story:** 作为一个CLI工具的开发者，我希望在开启--debug参数时，能够在代码上下文之上看到SWC匹配的原始span关键信息，以便我能快速判断是匹配逻辑问题还是代码框架定位问题

#### Acceptance Criteria

1. WHEN 用户使用 `--debug --details --show-context` 参数时 THEN 系统应在每个复杂度步骤的代码上下文框架之上显示一个"SWC Span 调试信息"区域
2. WHEN 显示SWC Span调试信息时 THEN 系统应包含以下关键信息：
   - span.start 和 span.end 位置
   - 节点类型 (nodeType)
   - 原始代码片段（span对应的源码内容）
   - 计算出的行号和列号位置
3. WHEN 没有开启--debug参数时 THEN 系统不应显示SWC Span调试信息，保持原有输出格式
4. WHEN step.span 信息缺失时 THEN 系统应显示"无可用span信息"的提示信息

### Requirement 2: 格式化和可读性优化

**User Story:** 作为一个使用调试功能的开发者，我希望SWC span信息以清晰易读的格式展示，以便我能快速理解和分析定位信息

#### Acceptance Criteria

1. WHEN 显示SWC Span调试信息时 THEN 系统应使用一致的格式化风格，包括颜色编码和缩进对齐
2. WHEN 显示span信息时 THEN 系统应使用灰色文本以区别于主要内容，并添加"🔍 SWC Span 调试信息："前缀
3. WHEN 显示原始代码片段时 THEN 系统应截取适当长度（最多50个字符）并进行转义处理，避免显示问题
4. WHEN span信息很长时 THEN 系统应适当截断并添加省略号，保持输出整洁

### Requirement 3: 错误处理和边界情况

**User Story:** 作为一个CLI工具的用户，我希望在出现异常情况时，调试信息显示仍然稳定可靠，不会影响主要功能的正常使用

#### Acceptance Criteria

1. WHEN 读取源文件内容失败时 THEN 系统应显示"无法读取源文件内容"的错误提示，但不影响其他调试信息显示
2. WHEN span.start 或 span.end 超出文件范围时 THEN 系统应显示警告信息并展示可用的span数值
3. WHEN 计算行列号出现异常时 THEN 系统应fallback到显示原始span位置，不中断处理流程
4. WHEN 调试信息生成过程出现任何异常时 THEN 系统应捕获错误并继续正常的代码上下文显示

## Non-Functional Requirements

### Performance
- SWC span信息显示不应显著增加分析时间（增加时间<5%）
- 仅在开启--debug参数时才执行额外的span信息处理逻辑
- 利用现有的文件缓存机制，避免重复读取源文件

### Security
- 确保显示的源代码片段经过适当转义，避免终端注入攻击
- 不泄露敏感的文件系统路径信息

### Reliability
- 调试信息显示错误不应影响主要的复杂度分析功能
- 提供优雅的降级处理，在信息不可用时给出清晰提示
- 保持与现有格式化逻辑的兼容性

### Usability
- 调试信息应与现有输出格式保持一致的视觉风格
- 信息层次清晰，便于快速定位和理解
- 支持彩色和无彩色输出模式