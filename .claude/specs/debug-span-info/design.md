# Debug Span Info - Design Document

## Overview

基于需求分析和代码库研究，debug-span-info功能将在现有的代码上下文显示功能基础上，增加SWC原始span信息的调试显示。该功能利用现有的`--debug`参数、`DetailStep`数据结构和`TextFormatter`机制，通过扩展`generateContextFrame()`方法来实现SWC span调试信息的显示。

该设计遵循现有的函数式编程范式，最小化对现有架构的影响，并确保调试信息的显示不会影响主要功能的性能和可靠性。

## Steering Document Alignment

### Technical Standards (tech.md)

**函数式编程优先**: 
- 新增的span信息格式化功能使用纯函数实现
- 避免创建新的类，使用函数式工具函数
- 所有数据结构保持不可变性

**Node.js兼容性**: 
- 仅使用Node.js标准API和现有依赖
- 不引入额外的第三方库
- 与现有的chalk颜色输出和文件读取机制保持兼容

**性能要求**:
- 仅在`--debug`模式下启用额外处理逻辑
- 利用现有的文件缓存机制，避免重复读取
- 控制性能影响在5%以内

### Project Structure (structure.md)

**模块职责分工**:
- 在`src/formatters/text.ts`中扩展格式化逻辑
- 在`src/utils/`中添加span信息提取和格式化工具函数
- 保持与`src/core/types.ts`中`DetailStep`接口的兼容性

**文件组织原则**:
- 单一职责：新功能专注于调试信息显示
- 低耦合：最小化对现有组件的修改
- 明确接口：通过清晰的函数接口与现有系统集成

## Code Reuse Analysis

**完全复用的组件**:
- `--debug` CLI参数配置 (src/cli/index.ts:51)
- `DetailStep` 接口中的 `span` 和 `nodeType` 属性 (src/core/types.ts:189-190)
- `CodeFrameGenerator` 的文件缓存机制 (src/utils/code-frame-generator.ts)
- `TextFormatter` 的现有格式化基础设施 (src/formatters/text.ts)

**扩展的组件**:
- `TextFormatter.generateContextFrame()` 方法：添加调试信息显示逻辑
- chalk颜色配置：复用现有的颜色方案

**新增的工具函数**:
- `extractSpanSourceCode()`: 从源文件中提取span对应的代码片段
- `formatSpanDebugInfo()`: 格式化span调试信息的纯函数
- `sanitizeCodeSnippet()`: 安全转义代码片段

## Architecture

整体架构采用"装饰器模式"的设计思路，在现有的代码上下文显示功能基础上，通过条件性地添加调试信息区域来实现功能扩展。

```mermaid
graph TD
    A[用户输入: --debug --details --show-context] --> B[CLI参数解析]
    B --> C[TextFormatter.formatDetailSteps]
    C --> D[TextFormatter.generateContextFrame]
    D --> E{debug模式?}
    E -->|是| F[生成SWC Span调试信息]
    E -->|否| G[跳过调试信息]
    F --> H[extractSpanSourceCode]
    F --> I[formatSpanDebugInfo]
    H --> J[文件内容读取]
    I --> K[格式化输出]
    J --> K
    K --> L[合并调试信息和代码框架]
    G --> M[原有代码框架显示]
    L --> N[最终输出]
    M --> N
    
    style F fill:#e1f5fe
    style H fill:#f3e5f5
    style I fill:#f3e5f5
```

### Data Flow

```mermaid
sequenceDiagram
    participant CLI as CLI Parser
    participant TF as TextFormatter
    participant CG as CodeFrameGenerator
    participant FC as FileCache
    participant Utils as SpanUtils
    
    CLI->>TF: formatDetailSteps(details, options)
    TF->>TF: shouldShowContext(step)
    TF->>TF: generateContextFrame(step, filePath, options)
    
    alt debug模式开启
        TF->>Utils: formatSpanDebugInfo(step, filePath)
        Utils->>FC: getFileContent(filePath)
        FC-->>Utils: 返回文件内容
        Utils->>Utils: extractSpanSourceCode(content, span)
        Utils->>Utils: sanitizeCodeSnippet(snippet)
        Utils-->>TF: 返回格式化的调试信息
    end
    
    TF->>CG: generateFrameFromSpan(filePath, span, options)
    CG-->>TF: 返回代码框架
    TF->>TF: 合并调试信息和代码框架
    TF-->>CLI: 返回完整输出
```

## Components and Interfaces

### Component 1: SpanDebugInfoFormatter (新增工具函数集)

**Purpose**: 提供span调试信息的格式化和提取功能
**Location**: `src/utils/span-debug-info.ts`
**Type**: 纯函数工具集

**Interfaces**:
```typescript
// 主要格式化函数
export function formatSpanDebugInfo(
  step: DetailStep, 
  filePath: string, 
  options?: CLIOptions
): Promise<string | null>

// 提取span对应的源代码片段
export function extractSpanSourceCode(
  sourceCode: string, 
  span: { start: number; end: number }
): string

// 安全转义代码片段
export function sanitizeCodeSnippet(snippet: string, maxLength?: number): string

// 格式化位置信息
export function formatPositionInfo(
  span: { start: number; end: number },
  position: { line: number; column: number }
): string
```

**Dependencies**: 
- `chalk` (用于颜色格式化)
- `src/utils/file-cache.ts` (复用文件缓存)

**Reuses**: 
- 现有的chalk颜色配置方案
- `getGlobalFileCache()`文件缓存机制

### Component 2: TextFormatter扩展 (修改现有组件)

**Purpose**: 在现有的代码上下文生成中集成调试信息显示
**Location**: `src/formatters/text.ts`
**Type**: 扩展现有方法

**Modified Interface**:
```typescript
// 扩展现有方法，增加调试信息显示逻辑
private async generateContextFrame(
  step: DetailStep, 
  filePath?: string, 
  options?: CLIOptions
): Promise<string | null>
```

**Integration Logic**:
1. 检查`options?.debug`标志
2. 在生成代码框架之前，先生成span调试信息
3. 将调试信息和代码框架合并输出

**Dependencies**: 
- 新增的`SpanDebugInfoFormatter`工具函数
- 现有的`CodeFrameGenerator`

### Component 3: CLI配置扩展 (复用现有组件)

**Purpose**: 复用现有的`--debug`参数配置
**Location**: `src/cli/index.ts`
**Type**: 无需修改，直接复用

**Reuses**: 
- 第51行已有的`--debug`参数定义
- 现有的`CLIOptions`类型定义

## Data Models

### Enhanced DetailStep Usage

复用现有的`DetailStep`接口，充分利用其中的调试相关字段：

```typescript
// 现有接口，无需修改
interface DetailStep {
  // ... 现有字段
  
  // 充分利用这些调试相关字段
  span?: { start: number; end: number };      // SWC原始span信息
  nodeType?: string;                          // AST节点类型
  diagnosticMarker?: DiagnosticMarker;        // 诊断标记
  diagnosticMessage?: string;                 // 诊断消息
}
```

### Span Debug Info Data Structure

新增内部使用的调试信息数据结构：

```typescript
// 内部使用的调试信息结构
interface SpanDebugInfo {
  readonly span: { start: number; end: number };
  readonly nodeType: string;
  readonly sourceSnippet: string;
  readonly position: { line: number; column: number };
  readonly hasValidSpan: boolean;
  readonly errorMessage?: string;
}
```

## Error Handling

采用分层错误处理策略，确保调试信息显示错误不影响主要功能：

### Level 1: 优雅降级
```typescript
// 在SpanDebugInfoFormatter中
export async function formatSpanDebugInfo(
  step: DetailStep, 
  filePath: string, 
  options?: CLIOptions
): Promise<string | null> {
  try {
    // 主要逻辑
  } catch (error) {
    // 返回简化的调试信息，不抛出异常
    return formatFallbackDebugInfo(step, error.message);
  }
}
```

### Level 2: 边界条件处理
- **span信息缺失**: 显示"无可用span信息"
- **文件读取失败**: 显示"无法读取源文件内容"
- **span超出范围**: 显示警告和可用的span数值
- **位置计算异常**: fallback到原始span位置

### Level 3: 主流程保护
```typescript
// 在TextFormatter.generateContextFrame中
if (options?.debug) {
  try {
    const debugInfo = await formatSpanDebugInfo(step, filePath, options);
    if (debugInfo) {
      lines.unshift(debugInfo); // 在代码框架前添加调试信息
    }
  } catch (error) {
    // 忽略调试信息错误，继续正常的代码框架生成
    if (options?.debug) {
      console.warn('调试信息生成失败:', error.message);
    }
  }
}
```

## Testing Strategy

基于现有测试框架和工具，采用分层测试方法：

### Unit Tests

**Location**: `src/__test__/utils/span-debug-info.test.ts`

**Test Cases**:
```typescript
describe('SpanDebugInfoFormatter', () => {
  describe('formatSpanDebugInfo', () => {
    it('should format complete span debug info');
    it('should handle missing span gracefully');
    it('should handle file read errors');
    it('should respect maxLength limits');
  });
  
  describe('extractSpanSourceCode', () => {
    it('should extract correct code snippet');
    it('should handle span out of bounds');
    it('should handle empty files');
  });
  
  describe('sanitizeCodeSnippet', () => {
    it('should escape special characters');
    it('should truncate long snippets');
    it('should preserve meaningful whitespace');
  });
});
```

### Integration Tests

**Location**: `src/__test__/formatters/debug-span-integration.test.ts`

**Test Scenarios**:
- 完整的`--debug --details --show-context`流程测试
- 与现有代码框架生成的集成测试
- 错误处理和降级场景测试
- 性能影响测试

### End-to-End Tests

**Location**: `src/__test__/e2e/debug-span-e2e.test.ts`

**Test Cases**:
- CLI参数组合测试
- 实际文件分析结果验证
- 输出格式验证
- 彩色和无彩色输出测试

### Performance Tests

**Benchmarks**:
- 对比开启和关闭debug模式的性能差异
- 确保性能影响小于5%
- 大文件和多文件场景的性能测试

### Test Utilities

复用现有的测试工具和夹具：
- `src/__test__/helpers/cli-testing-utils.ts`
- `src/__test__/helpers/fixture-manager.ts`
- `src/__test__/fixtures/` 目录下的测试文件

## Implementation Plan

### Phase 1: 核心工具函数实现
1. 创建`src/utils/span-debug-info.ts`
2. 实现`extractSpanSourceCode()`和`sanitizeCodeSnippet()`
3. 实现基础的`formatSpanDebugInfo()`函数

### Phase 2: TextFormatter集成
1. 修改`TextFormatter.generateContextFrame()`方法
2. 添加调试信息显示逻辑
3. 确保错误处理和降级机制

### Phase 3: 测试和优化
1. 编写完整的单元测试和集成测试
2. 性能基准测试和优化
3. 边界条件和错误场景测试

### Phase 4: 文档和验收
1. 更新相关代码注释和文档
2. 端到端功能验证
3. 性能回归测试

## Security Considerations

- **代码片段转义**: 确保显示的源代码经过适当转义，防止终端注入
- **路径信息保护**: 避免泄露敏感的文件系统路径信息  
- **内存安全**: 控制代码片段长度，防止内存耗尽
- **权限控制**: 仅在debug模式下启用，避免生产环境信息泄露