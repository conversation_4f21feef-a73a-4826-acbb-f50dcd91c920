# Debug Span Info - Implementation Tasks

## Task Overview

基于设计文档，debug-span-info功能的实施采用增量式开发方法，分为4个主要阶段：核心工具函数开发、TextFormatter集成、全面测试覆盖、性能优化和验收。每个任务都专注于代码实现，充分复用现有组件，遵循项目的函数式编程范式。

## Steering Document Compliance

**Technical Standards (tech.md)**:
- 所有新代码使用纯函数实现，避免创建新的类
- 仅使用Node.js标准API和现有依赖（chalk、file-cache等）
- 保持不可变数据结构，使用readonly修饰符

**Project Structure (structure.md)**:
- 工具函数放在`src/utils/`目录
- 扩展现有格式化器而非创建新的格式化器
- 测试文件遵循现有的目录结构约定

## Tasks

- [x] 1. 创建核心span调试信息工具函数集
  - 在`src/utils/`目录创建`span-debug-info.ts`
  - 实现`extractSpanSourceCode()`纯函数，从源代码中提取span对应的代码片段
  - 实现`sanitizeCodeSnippet()`纯函数，安全转义代码片段并控制长度
  - 实现`formatPositionInfo()`纯函数，格式化span和位置信息
  - 添加完整的TypeScript类型定义和JSDoc注释
  - _Leverage: src/utils/file-cache.ts (getGlobalFileCache), chalk颜色配置方案_
  - _Requirements: 1.2, 2.1, 2.3_

- [ ] 2. 实现主要格式化函数
  - 在`span-debug-info.ts`中实现`formatSpanDebugInfo()`主函数
  - 集成文件缓存机制，复用`getGlobalFileCache()`避免重复读取
  - 实现三层错误处理策略：优雅降级、边界条件处理、主流程保护
  - 添加`formatFallbackDebugInfo()`函数处理异常情况
  - 确保所有函数都是纯函数，无副作用
  - _Leverage: src/utils/file-cache.ts, src/core/types.ts (DetailStep), chalk_
  - _Requirements: 1.1, 1.4, 3.1, 3.2, 3.4_

- [ ] 3. 扩展TextFormatter集成调试信息显示
  - 修改`src/formatters/text.ts`中的`generateContextFrame()`方法
  - 添加debug模式检测逻辑（`options?.debug`）
  - 在代码框架生成前调用`formatSpanDebugInfo()`
  - 实现调试信息和代码框架的合并显示逻辑
  - 确保非debug模式下零性能影响
  - _Leverage: 现有的generateContextFrame()方法, CodeFrameGenerator, chalk配置_
  - _Requirements: 1.1, 1.3, 2.2_

- [ ] 4. 实现错误处理和边界条件
  - 在`formatSpanDebugInfo()`中处理span信息缺失的情况
  - 处理文件读取失败、span超出范围、位置计算异常等边界条件
  - 确保调试信息生成错误不影响主要功能
  - 添加适当的警告日志和用户友好的错误提示
  - _Leverage: 现有的错误处理模式, src/core/errors.ts_
  - _Requirements: 3.1, 3.2, 3.3, 3.4_

- [ ] 5. 创建核心工具函数单元测试
  - 创建`src/__test__/utils/span-debug-info.test.ts`
  - 为`extractSpanSourceCode()`编写完整测试用例（正常、边界、异常情况）
  - 为`sanitizeCodeSnippet()`编写测试（转义、截取、保留格式）
  - 为`formatPositionInfo()`编写测试（格式化验证）
  - 为`formatSpanDebugInfo()`编写集成测试
  - _Leverage: src/__test__/helpers/test-utils.ts, src/__test__/fixtures/_
  - _Requirements: 所有需求的测试覆盖_

- [ ] 6. 创建TextFormatter集成测试
  - 创建`src/__test__/formatters/debug-span-integration.test.ts`
  - 测试`--debug --details --show-context`完整流程
  - 测试调试信息和代码框架的正确合并
  - 测试debug模式开关的行为差异
  - 测试错误处理和降级场景
  - _Leverage: 现有的TextFormatter测试套件, src/__test__/helpers/cli-testing-utils.ts_
  - _Requirements: 1.1, 1.3, 2.1, 2.2_

- [ ] 7. 创建端到端CLI测试
  - 创建`src/__test__/e2e/debug-span-e2e.test.ts`
  - 测试完整的CLI参数组合（`--debug --details --show-context`）
  - 验证实际文件分析的调试信息输出
  - 测试彩色和无彩色输出模式
  - 测试多种文件类型和span情况的处理
  - _Leverage: src/__test__/helpers/cli-testing-utils.ts, src/__test__/fixtures/real-world/_
  - _Requirements: 1.1, 1.2, 2.2, 2.4_

- [ ] 8. 性能基准测试和优化
  - 创建`src/__test__/performance/debug-span-benchmark.test.ts`
  - 测试开启和关闭debug模式的性能差异
  - 确保性能影响小于5%的要求
  - 测试大文件和多文件场景的性能表现
  - 优化文件缓存使用，避免重复读取
  - _Leverage: 现有的性能测试工具, src/__test__/performance/benchmark.test.ts_
  - _Requirements: 非功能需求-性能_

- [ ] 9. 错误处理和边界条件测试
  - 扩展现有测试，覆盖所有错误处理路径
  - 测试span信息缺失、文件读取失败、span超出范围等场景
  - 验证所有错误都能优雅降级，不影响主功能
  - 测试错误提示信息的用户友好性
  - _Leverage: 现有的错误处理测试模式_
  - _Requirements: 3.1, 3.2, 3.3, 3.4_

- [ ] 10. 代码安全性和转义测试
  - 测试代码片段的安全转义处理
  - 验证特殊字符、控制字符、长字符串的处理
  - 测试终端注入攻击的防护
  - 验证文件路径信息的安全处理
  - _Leverage: 现有的安全测试实践_
  - _Requirements: 非功能需求-安全性_

- [x] 11. 文档更新和代码注释完善
  - 更新所有新增函数的JSDoc注释
  - 添加使用示例和API文档
  - 更新相关的TypeScript类型定义注释
  - 确保代码可读性和可维护性
  - _Leverage: 现有的文档风格和注释约定_
  - _Requirements: 可维护性需求_

- [ ] 12. 最终集成验证和回归测试
  - 运行完整的测试套件，确保无回归问题
  - 验证所有需求的完整实现
  - 进行真实场景的端到端功能验证
  - 确认性能要求和安全要求的满足
  - 验证与现有功能的兼容性
  - _Leverage: 现有的CI/CD测试流程_
  - _Requirements: 所有需求的最终验证_