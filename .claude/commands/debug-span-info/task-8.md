# debug-span-info - Task 8

Execute task 8 for the debug-span-info specification.

## Task Description
性能基准测试和优化

## Code Reuse
**Leverage existing code**: 现有的性能测试工具, src/

## Requirements Reference
**Requirements**: 非功能需求-性能

## Usage
```
/debug-span-info-task-8
```

## Instructions

**Agent-Based Execution (Recommended)**: If the `spec-task-executor` agent is available, use it for optimal task implementation:

```
Use the spec-task-executor agent to implement task 8: "性能基准测试和优化" for the debug-span-info specification.

The agent should:
1. Load all specification context from .claude/specs/debug-span-info/
2. Load steering documents from .claude/steering/ (if available)
3. Implement ONLY task 8: "性能基准测试和优化"
4. Follow all project conventions and leverage existing code
5. Mark the task as complete in tasks.md
6. Provide a completion summary

Context files to load:
- .claude/specs/debug-span-info/requirements.md
- .claude/specs/debug-span-info/design.md  
- .claude/specs/debug-span-info/tasks.md
- .claude/steering/product.md (if exists)
- .claude/steering/tech.md (if exists)
- .claude/steering/structure.md (if exists)

Task details:
- ID: 8
- Description: 性能基准测试和优化
- Leverage: 现有的性能测试工具, src/
- Requirements: 非功能需求-性能
```

**Fallback Execution**: If the agent is not available, you can execute:
```
/spec-execute 8 debug-span-info
```

**Context Loading**:
Before executing the task, you MUST load all relevant context:
1. **Specification Documents**:
   - Load `.claude/specs/debug-span-info/requirements.md` for feature requirements
   - Load `.claude/specs/debug-span-info/design.md` for technical design
   - Load `.claude/specs/debug-span-info/tasks.md` for the complete task list
2. **Steering Documents** (if available):
   - Load `.claude/steering/product.md` for product vision context
   - Load `.claude/steering/tech.md` for technical standards
   - Load `.claude/steering/structure.md` for project conventions

**Process**:
1. Load all context documents listed above
2. Execute task 8: "性能基准测试和优化"
3. **Prioritize code reuse**: Use existing components and utilities identified above
4. Follow all implementation guidelines from the main /spec-execute command
5. **Follow steering documents**: Adhere to patterns in tech.md and conventions in structure.md
6. **CRITICAL**: Mark the task as complete in tasks.md by changing [ ] to [x]
7. Confirm task completion to user
8. Stop and wait for user review

**Important Rules**:
- Execute ONLY this specific task
- **Leverage existing code** whenever possible to avoid rebuilding functionality
- **Follow project conventions** from steering documents
- Mark task as complete by changing [ ] to [x] in tasks.md
- Stop after completion and wait for user approval
- Do not automatically proceed to the next task
- Validate implementation against referenced requirements

## Task Completion Protocol
When completing this task:
1. **Update tasks.md**: Change task 8 status from `- [ ]` to `- [x]`
2. **Confirm to user**: State clearly "Task 8 has been marked as complete"
3. **Stop execution**: Do not proceed to next task automatically
4. **Wait for instruction**: Let user decide next steps

## Next Steps
After task completion, you can:
- Review the implementation
- Run tests if applicable
- Execute the next task using /debug-span-info-task-[next-id]
- Check overall progress with /spec-status debug-span-info
