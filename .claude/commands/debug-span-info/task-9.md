# debug-span-info - Task 9

Execute task 9 for the debug-span-info specification.

## Task Description
错误处理和边界条件测试

## Code Reuse
**Leverage existing code**: 现有的错误处理测试模式

## Requirements Reference
**Requirements**: 3.1, 3.2, 3.3, 3.4

## Usage
```
/debug-span-info-task-9
```

## Instructions

**Agent-Based Execution (Recommended)**: If the `spec-task-executor` agent is available, use it for optimal task implementation:

```
Use the spec-task-executor agent to implement task 9: "错误处理和边界条件测试" for the debug-span-info specification.

The agent should:
1. Load all specification context from .claude/specs/debug-span-info/
2. Load steering documents from .claude/steering/ (if available)
3. Implement ONLY task 9: "错误处理和边界条件测试"
4. Follow all project conventions and leverage existing code
5. Mark the task as complete in tasks.md
6. Provide a completion summary

Context files to load:
- .claude/specs/debug-span-info/requirements.md
- .claude/specs/debug-span-info/design.md  
- .claude/specs/debug-span-info/tasks.md
- .claude/steering/product.md (if exists)
- .claude/steering/tech.md (if exists)
- .claude/steering/structure.md (if exists)

Task details:
- ID: 9
- Description: 错误处理和边界条件测试
- Leverage: 现有的错误处理测试模式
- Requirements: 3.1, 3.2, 3.3, 3.4
```

**Fallback Execution**: If the agent is not available, you can execute:
```
/spec-execute 9 debug-span-info
```

**Context Loading**:
Before executing the task, you MUST load all relevant context:
1. **Specification Documents**:
   - Load `.claude/specs/debug-span-info/requirements.md` for feature requirements
   - Load `.claude/specs/debug-span-info/design.md` for technical design
   - Load `.claude/specs/debug-span-info/tasks.md` for the complete task list
2. **Steering Documents** (if available):
   - Load `.claude/steering/product.md` for product vision context
   - Load `.claude/steering/tech.md` for technical standards
   - Load `.claude/steering/structure.md` for project conventions

**Process**:
1. Load all context documents listed above
2. Execute task 9: "错误处理和边界条件测试"
3. **Prioritize code reuse**: Use existing components and utilities identified above
4. Follow all implementation guidelines from the main /spec-execute command
5. **Follow steering documents**: Adhere to patterns in tech.md and conventions in structure.md
6. **CRITICAL**: Mark the task as complete in tasks.md by changing [ ] to [x]
7. Confirm task completion to user
8. Stop and wait for user review

**Important Rules**:
- Execute ONLY this specific task
- **Leverage existing code** whenever possible to avoid rebuilding functionality
- **Follow project conventions** from steering documents
- Mark task as complete by changing [ ] to [x] in tasks.md
- Stop after completion and wait for user approval
- Do not automatically proceed to the next task
- Validate implementation against referenced requirements

## Task Completion Protocol
When completing this task:
1. **Update tasks.md**: Change task 9 status from `- [ ]` to `- [x]`
2. **Confirm to user**: State clearly "Task 9 has been marked as complete"
3. **Stop execution**: Do not proceed to next task automatically
4. **Wait for instruction**: Let user decide next steps

## Next Steps
After task completion, you can:
- Review the implementation
- Run tests if applicable
- Execute the next task using /debug-span-info-task-[next-id]
- Check overall progress with /spec-status debug-span-info
