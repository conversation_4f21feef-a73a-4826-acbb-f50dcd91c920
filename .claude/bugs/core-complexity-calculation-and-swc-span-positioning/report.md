# Bug Report

## Bug Summary
核心复杂度计算和SWC AST span源码定位系统存在严重的架构性问题，导致函数识别失败、复杂度归属错误和源码位置计算不准确。

## Bug Details

### Expected Behavior
工具应该能够：
1. 正确识别React组件中的所有函数（包括箭头函数、事件处理器等）
2. 准确计算每个函数内部的控制流复杂度（if语句、逻辑操作符等）
3. 精确定位函数和复杂度增量的源码位置（行号和列号）
4. 正确归属复杂度到相应的函数，而不是错误地标记为JSX复杂度

### Actual Behavior  
在分析真实的React组件文件时发生以下严重问题：
1. **函数识别失败**：只识别出2个函数，实际应该识别出更多函数
2. **位置计算错误**：handleExport函数被错误定位到39:43行，实际应该在234:271行
3. **复杂度归属错误**：真实的if语句复杂度被错误地标记为"JSX复杂度"
4. **控制流忽略**：文件中的多个if语句（4个在handleExport中，1个在onChange中）完全没有被识别

### Steps to Reproduce
1. 运行命令：`bun run src/cli/index.ts /Users/<USER>/projj/git.imile.com/ux/ds-web/apps/ds/src/pages/InventoryEE/NDR/index.tsx -d --show-all-context --show-context`
2. 观察输出结果显示：
   - handleExport函数定位错误（39:43而非234:271）
   - 复杂度被标记为"JSX复杂度"而非"条件语句"
   - 只检测到2个函数而非实际的多个函数
3. 用简单测试文件验证：同样的代码逻辑在测试文件中能正确识别
4. 发现架构问题：核心计算引擎在处理复杂真实文件时失效

### Environment
- **Version**: cognitive-complexity CLI (当前版本)
- **Platform**: Darwin 24.5.0, Bun 1.0+, TypeScript 5.0+
- **Configuration**: 默认配置，使用SWC解析器和语义感知访问者模式

## Impact Assessment

### Severity
- [x] Critical - System unusable
- [ ] High - Major functionality broken
- [ ] Medium - Feature impaired but workaround exists
- [ ] Low - Minor issue or cosmetic

### Affected Users
所有使用该工具分析真实React/TypeScript项目的用户都会受到影响，工具产生的复杂度报告完全不可信。

### Affected Features
- 核心复杂度计算功能
- 函数识别和边界检测
- 源码位置定位系统
- 复杂度归属和分类
- 详细报告生成

## Additional Context

### Error Messages
```
正常运行但结果错误：
- 函数位置: handleExport (39:43) <- 错误，应该是 (234:271)
- 复杂度类型: JSX复杂度 <- 错误，应该是条件语句复杂度
- 函数数量: 2个 <- 错误，应该识别出更多函数
```

### Screenshots/Media
对比测试显示：
- 简单测试文件：handleExport (5:21) 复杂度8，正确识别4个if语句
- 真实目标文件：handleExport (39:43) 复杂度1，标记为JSX复杂度

### Related Issues
- JSX复杂度过度计算问题（已修复）
- 语义定位系统设计（需要重新审视）
- 访问者模式的函数范围管理（核心问题）

## Initial Analysis

### Suspected Root Cause
1. **函数边界识别失败**：`SemanticComplexityVisitor`中的函数识别逻辑在复杂AST结构中失效
2. **访问者模式实现问题**：函数范围管理和子节点遍历存在逻辑错误
3. **SWC span位置计算**：真实文件的复杂AST结构导致span位置计算偏差
4. **复杂度归属机制缺陷**：复杂度被错误地归属到JSX容器而非实际的控制流节点

### Affected Components
**核心受影响文件**：
- `src/core/complexity-visitor-refactored.ts` - 主要计算引擎
- `src/core/semantic-position-service.ts` - 位置定位服务
- `src/core/function-semantic-analyzer.ts` - 函数语义分析
- `src/core/base-visitor.ts` - 访问者基类
- `src/core/parser.ts` - AST解析器

**核心问题模块**：
1. **函数识别系统** - `isFunctionNode()`, `extractFunctionName()`, `processFunction()`
2. **访问者遍历机制** - `visit()`, `visitChildren()`, 函数范围管理
3. **复杂度归属逻辑** - `addComplexity()`, `currentFunction`状态管理
4. **位置计算系统** - `getSemanticPosition()`, `calculateLineNumber()`
5. **语义定位策略** - 节点特定策略映射表，智能回退机制

**关键架构问题**：
- 函数访问的"后处理"机制（`finishFunction`）可能存在时序问题
- 复杂度状态管理在嵌套函数场景下可能混乱
- SWC AST的节点结构理解可能存在偏差
- 语义定位的回退策略在复杂代码中可能失效