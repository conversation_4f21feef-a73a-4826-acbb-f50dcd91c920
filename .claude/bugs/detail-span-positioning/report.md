# Bug Report - Detail Span Positioning Issue

## Bug Summary
在详情模式（`-d --show-context`）中，所有复杂度计算步骤的代码上下文都错误地显示文件第1行，而不是实际的复杂度增量所在位置。这导致用户无法准确定位代码中的复杂度问题。

## Bug Details

### Expected Behavior
当使用 `-d --show-context --context-lines 5` 参数时，每个复杂度计算步骤应该显示：
- 正确的行号和列号（如 L39:42, L238:35）
- 该位置周围的实际代码上下文（前后5行）
- 准确指向复杂度增量所在的具体代码行

### Actual Behavior  
所有复杂度计算步骤都显示：
- 相同的代码上下文（文件第1行的注释）
- 虽然函数位置信息正确（如 `(39:42)`, `(238:35)`），但详情步骤的位置都指向错误位置
- 所有代码框架都显示文件开头，用户无法定位具体问题代码

### Steps to Reproduce
1. 运行命令：`bun run src/cli/index.ts /path/to/tsx/file -d --show-context --context-lines 5`
2. 观察输出的详情部分
3. 注意所有步骤的"代码上下文"都显示相同内容（第1行）
4. 验证这与函数位置信息不匹配

### Environment
- **Version**: SWC Span 智能回退机制项目（当前开发版本）
- **Platform**: macOS (Darwin 24.5.0)
- **Configuration**: 使用默认配置，启用详情模式和代码上下文

## Impact Assessment

### Severity
- [x] High - Major functionality broken

### Affected Users
所有使用详情模式进行代码复杂度分析的开发者

### Affected Features
- 详情模式的代码上下文显示功能
- 复杂度定位和代码导航功能
- 开发者调试和优化体验

## Additional Context

### Error Messages
没有明确的错误消息，但看到了错误恢复信息：
```
📊 错误恢复: 2次尝试, 策略: generate-frame
```

### Screenshots/Media
从输出可以看到，所有步骤都显示相同的代码上下文：
```
┌─ 代码上下文 ─────
│ > 1 | /* eslint-disable react-hooks/exhaustive-deps */
│     | ^
│   2 |
│   3 | /* eslint-disable @typescript-eslint/no-explicit-any */
│   4 | import {
│   5 |   Ellipsis, enqueueSnackbar, useFireExport,
│   6 | } from '@imile/components'
└─────────────────
```

### Related Issues
这个问题可能与以下组件相关：
- SWC Span 智能回退机制的位置转换
- DetailCollector 的位置记录
- CodeFrameGenerator 的代码上下文生成

## Initial Analysis

### Suspected Root Cause
问题可能出现在以下几个方面：
1. **DetailCollector** 中记录的 `line` 和 `column` 信息不正确
2. **Position Converter** 的 span 转换逻辑存在问题
3. **Code Frame Generator** 使用了错误的位置信息生成代码框架
4. **错误恢复机制** 可能在定位失败时回退到了文件开头位置

### Affected Components
- `src/core/detail-collector.ts` - 复杂度步骤记录
- `src/utils/position-converter.ts` - Span 位置转换
- `src/utils/code-frame-generator.ts` - 代码框架生成
- `src/core/complexity-visitor.ts` - 访问者模式中的位置记录
- SWC Span 智能回退机制的策略映射系统

### Investigation Priority
1. 检查 DetailCollector 中 addStep 调用时的位置参数
2. 验证 PositionConverter 的 span 转换结果
3. 测试 CodeFrameGenerator 是否使用了正确的行列信息
4. 确认错误恢复策略是否影响了位置准确性

## Next Steps
1. 进入 `/bug-analyze` 阶段深入调查根本原因
2. 重点检查位置转换和代码框架生成逻辑
3. 创建针对性测试用例验证修复效果