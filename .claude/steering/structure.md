# 项目结构引导文档

## 目录结构概览

```
src/
├── core/           # 核心算法和类型定义
├── cli/            # 命令行界面
├── config/         # 配置管理
├── baseline/       # 基线管理
├── formatters/     # 输出格式化
├── cache/          # 缓存系统
├── engine/         # 执行引擎和性能优化
├── plugins/        # 插件系统
├── rules/          # 复杂度规则和规则集
├── ui/             # Web用户界面
├── utils/          # 通用工具函数
└── __test__/       # 测试文件
```

## 模块职责分工

### 核心模块 (`src/core/`)
**职责**: 认知复杂度计算的核心算法实现

**核心复杂度计算架构详解**:

**重大架构变更 (2024)**:
- ❌ **已移除**: `calculator.ts` - 删除了2484行的庞大ComplexityCalculator类
- ✅ **新增**: 基于语义感知的轻量级架构
- ✅ **简化**: 使用纯函数API替代复杂的类实例
- ✅ **语义增强**: 引入专门的语义分析服务解决代码定位精度问题

**当前核心文件及其在复杂度计算流程中的作用**:

#### AST 解析层
- `parser.ts`: **AST解析器封装** - SWC解析器的封装，负责源码→AST转换
- `types.ts`: **核心类型定义** - 定义了整个计算流程的数据结构和接口

#### 语义感知访问者层 (核心计算引擎)
- `complexity-visitor-refactored.ts`: **语义感知复杂度访问者** - 核心计算引擎
  - 基于访问者模式遍历AST节点
  - 集成语义服务进行智能代码定位
  - 自动识别函数边界并隔离复杂度计算
  - 管理嵌套层级和复杂度增量
- `base-visitor.ts`: **访问者基类** - 标准访问者模式实现，提供AST遍历基础设施
- `semantic-complexity-visitor-factory.ts`: **语义访问者工厂** - 创建配置完整的语义访问者实例

#### 语义分析服务层 (代码定位核心)
- `semantic-position-service.ts`: **语义位置服务** - 智能代码定位的核心服务
  - 四层策略回退机制 (节点特定→语义感知→智能回退→span原始位置)
  - 节点类型特定的定位策略 (整合自 visitor/utils.ts)
  - 语义上下文分析和代码模式识别
- `visitor/utils.ts`: **节点策略映射表** - 30+ 种AST节点类型的精确定位策略
  - 控制流语句策略 (IfStatement, WhileStatement, ForStatement, etc.)
  - 函数定义策略 (ArrowFunction, FunctionExpression, MethodDefinition, etc.)
  - JSX相关策略 (JSXElement, JSXFragment, JSXExpressionContainer)
  - 表达式操作符策略 (ConditionalExpression, LogicalExpression, MemberExpression, etc.)
  - 支持运行时扩展和动态配置的策略注册系统
- `intelligent-fallback-engine.ts`: **智能回退引擎** - 定位失败时的多种回退算法
- `jsx-semantic-parser.ts`: **JSX语义解析器** - JSX代码的专用语义分析
- `function-semantic-analyzer.ts`: **函数语义分析器** - 函数节点的语义理解
- `code-semantic-analyzer.ts`: **代码语义分析器** - 通用代码模式识别和分析
- `semantic-types.ts`: **语义类型定义** - 语义分析相关的数据结构

**语义定位系统的四层架构**:
```
第1层: 节点特定策略映射表 (visitor/utils.ts)
└─ 30+ 种AST节点类型的专门定位逻辑
└─ 每种节点的主策略 + 回退策略
└─ 支持运行时注册和优先级管理

第2层: 语义感知定位 (semantic-position-service.ts)  
└─ 代码模式识别 (jsx/function/control-flow/expression)
└─ 智能行筛选 (跳过注释、空行、结构代码)
└─ 逻辑重要性评级 (primary/secondary/structural)

第3层: 智能回退引擎 (intelligent-fallback-engine.ts)
└─ 关键字搜索回退策略
└─ 相邻节点位置推断算法  
└─ 语法结构模式匹配

第4层: span 原始位置
└─ SWC 原始 span.start 作为最后兜底
```

#### 规则系统层
- `rule-registry.ts`: **规则注册表** - 维护所有复杂度规则的标识符和描述
  - 静态Map存储，支持运行时注册和更新
  - kebab-case命名约定验证
  - 按RuleCategory枚举分类管理
  - 支持从Rule实例注册和批量注册
- `default-rules.ts`: **默认复杂度规则** - 预定义的标准复杂度规则集
- `rule-initialization.ts`: **规则系统初始化** - 规则引擎启动和配置管理

#### 详细信息收集和上下文管理
- `detail-collector.ts`: **详细复杂度信息收集** - 收集每个复杂度增量的详细信息
  - 跟踪每个函数的复杂度分解步骤
  - 记录精确的行号、列号和上下文信息
  - 支持调试和可视化
- `function-finder-visitor.ts`: **函数查找访问者** - 专门用于识别和定位函数节点

#### 性能和内存优化
- `object-pool.ts`: **对象池内存优化** - 减少GC压力的对象池实现
- `object-pool-types.ts`: **对象池类型定义** - 对象池相关的类型定义

#### 错误处理
- `errors.ts`: **自定义错误类型** - 复杂度计算相关的错误类型
- `type-safe-errors.ts`: **类型安全错误** - 增强的类型安全错误处理

**复杂度计算的完整流程**:
```
源代码 → ASTParser → AST节点树
    ↓
SemanticComplexityVisitor 遍历节点
    ↓
节点类型识别 (visitIfStatement, visitLogicalExpression, etc.)
    ↓
语义定位服务 → 找到逻辑有意义的代码位置
    ↓
规则系统查询 → 获取节点对应的复杂度规则
    ↓  
复杂度计算 → 基础复杂度 + 嵌套增量
    ↓
详细信息收集 → 记录每步计算的详情
    ↓
函数级汇总 → 按函数独立统计复杂度
    ↓
最终结果 → FileResult { functions: [...], totalComplexity }
```

**主要导出** (`src/index.ts`):
- `analyzeFile(filePath, options)`: **函数式核心API** - 纯函数实现，替代ComplexityCalculator
- `analyzeCode(code, filename, options)`: **纯函数式代码分析** - 无副作用的分析函数
- `SemanticComplexityVisitor`: 语义感知访问者（仅在需要定制时使用）
- `SemanticComplexityVisitorFactory`: 语义访问者工厂
- 工具函数集合和类型定义（全部为纯函数）

**语义感知的核心优势**:
1. **精确定位**: 定位到逻辑代码而非结构代码（如括号、分号）
2. **上下文感知**: 理解JSX、TypeScript、函数等不同代码模式
3. **智能回退**: 多级回退策略确保定位可靠性
4. **可扩展性**: 新增节点类型或语义规则更容易

**命名约定**:
- 类名使用 PascalCase: `ComplexityVisitor`
- 接口使用 PascalCase: `CalculationOptions`
- 函数名使用 camelCase: `analyzeFile`
- 类型别名使用 PascalCase: `FunctionResult`

### CLI模块 (`src/cli/`)
**职责**: 命令行界面和用户交互

- `index.ts`: CLI入口点和命令定义
- `commands.ts`: 命令处理器实现
- `debug-commands.ts`: 调试相关命令
- `ui-helper.ts`: UI辅助工具

**设计原则**:
- 使用 Commander.js 统一管理命令
- 错误处理友好，提供清晰的错误信息
- 支持配置文件和命令行参数

### 配置模块 (`src/config/`)
**职责**: 配置文件管理和验证

- `types.ts`: 配置相关类型定义
- `manager.ts`: 配置加载和管理器
- `modern-manager.ts`: 现代化配置管理
- `factory.ts`: 配置工厂模式
- `schema.ts`: 配置架构验证
- `validator.ts`: 配置验证器
- `project-detector.ts`: 项目类型检测

**配置优先级**:
1. 命令行参数
2. 配置文件 (cosmiconfig)
3. 默认值

### 基线模块 (`src/baseline/`)
**职责**: 复杂度基线的创建、更新和比较

- `types.ts`: 基线相关类型定义
- `manager.ts`: 基线管理器实现

### 格式化模块 (`src/formatters/`)
**职责**: 分析结果的格式化输出

- `base.ts`: 格式化器基类
- `text.ts`: 文本格式输出
- `json.ts`: JSON格式输出
- `html.ts`: HTML格式输出

**扩展模式**: 继承 `BaseFormatter` 实现新的输出格式

### 缓存模块 (`src/cache/`)
**职责**: 智能缓存系统，提升分析性能

- `types.ts`: 缓存相关类型定义
- `manager.ts`: 缓存管理器
- `monitor.ts`: 缓存监控
- `index.ts`: 缓存模块导出

### 执行引擎 (`src/engine/`)
**职责**: 高性能异步分析引擎和优化系统

- `types.ts`: 引擎相关类型定义
- `async-engine.ts`: 异步执行引擎
- `execution-pool.ts`: 执行池管理
- `performance-monitor.ts`: 性能监控
- `performance-benchmark.ts`: 性能基准测试
- `debug-system.ts`: 调试系统
- `debug-visualizer.ts`: 调试可视化
- `streaming-processor.ts`: 流式处理器
- `object-pool.ts`: 对象池优化
- `registry.ts`: 引擎注册表
- `iterative-algorithms.ts`: 迭代算法优化
- `jsx-integration.ts`: JSX集成支持
- `demo.ts` & `demo-registry.ts`: 演示和示例

### 插件系统 (`src/plugins/`)
**职责**: 完整的插件架构，支持自定义扩展

- `types.ts`: 插件相关类型定义
- `manager.ts`: 插件管理器
- `sandbox.ts`: 插件沙箱隔离
- `dependency-resolver.ts`: 依赖解析器
- `version-manager.ts`: 版本管理
- `validator.ts`: 插件验证
- `communication.ts`: 插件通信
- `hot-reload.ts`: 热重载支持
- `dev-tools.ts`: 开发工具
- `error-recovery.ts`: 错误恢复

### 规则系统 (`src/rules/`)
**职责**: 复杂度规则定义和管理

- `base-rule.ts`: 规则基类
- `core-complexity-rule.ts`: 核心复杂度规则
- `logical-operator-rule.ts`: 逻辑运算符规则
- `jsx-hook-complexity.ts`: JSX Hook复杂度规则
- `jsx-event-handler-rule.ts`: JSX事件处理器规则
- `jsx-structural-exemption.ts`: JSX结构豁免规则
- `smart-conditional-rendering.ts`: 智能条件渲染规则
- `rule-sets.ts`: 规则集管理
- `jsx/`: JSX专用规则目录
- `base/`: 基础规则目录

### Web UI (`src/ui/`)
**职责**: Web可视化界面

- `server.ts`: Web服务器实现

## 文件组织原则

### 模块边界
- **高内聚**: 相关功能放在同一模块内
- **低耦合**: 模块间通过明确的接口通信
- **单向依赖**: 避免循环依赖

### 文件命名
- **功能描述**: 文件名清晰描述其功能
- **kebab-case**: 使用短横线分隔 (如需要)
- **扩展名**: TypeScript 文件使用 `.ts`

### 导出策略
- **index.ts**: 模块的统一导出入口
- **公共API**: 只导出外部需要使用的接口
- **内部实现**: 保持模块内部实现的私有性

## 代码组织模式

### 函数式编程模式

```typescript
// 推荐：纯函数模式
export const analyzeComplexity = (ast: Module, options: AnalysisOptions): ComplexityResult => {
  const visitor = createComplexityVisitor(options);
  return visitor.visit(ast);
};

// 推荐：高阶函数模式
export const createAnalyzer = (baseOptions: BaseOptions) => 
  (code: string, fileOptions?: FileOptions) => 
    analyzeCode(code, { ...baseOptions, ...fileOptions });

// 推荐：函数组合模式
export const analyzeProject = pipe(
  findSourceFiles,
  mapAsync(analyzeFile),
  aggregateResults,
  generateReport
);
```

### 类型定义模式
```typescript
// 基础类型
export interface CalculationOptions {
  readonly maxComplexity?: number;
  readonly includeDetails?: boolean;
}

// 结果类型
export interface FunctionResult {
  readonly name: string;
  readonly complexity: number;
  readonly startLine: number;
  readonly endLine: number;
}

// 错误类型
export class ComplexityError extends Error {
  constructor(message: string, public readonly filePath?: string) {
    super(message);
    this.name = 'ComplexityError';
  }
}
```

### 异步处理模式
- **Promise优先**: 所有异步操作使用 Promise
- **函数式异步**: 使用 `async/await` 配合纯函数
- **错误传播**: 使用函数式错误处理，避免 try-catch 嵌套
- **并发控制**: 使用 Promise.all 和函数组合进行并发处理

```typescript
// 推荐：函数式异步处理
export const analyzeFiles = async (filePaths: readonly string[]) =>
  Promise.all(filePaths.map(analyzeFile));

// 推荐：错误处理函数式组合
export const safeAnalyze = (filePath: string) =>
  analyzeFile(filePath).catch(handleAnalysisError);
```

## 测试结构

### 测试目录结构
```
src/__test__/
├── core/           # 核心模块测试
├── cli/            # CLI测试
├── config/         # 配置测试
├── baseline/       # 基线测试
├── formatters/     # 格式化测试
├── cache/          # 缓存系统测试
├── engine/         # 执行引擎测试
├── plugins/        # 插件系统测试
├── rules/          # 规则系统测试
├── ui/             # Web UI测试
├── integration/    # 集成测试
├── e2e/           # 端到端测试
├── performance/    # 性能测试
├── stress/         # 压力测试
├── react-ecosystem/ # React生态测试
├── fixtures/       # 测试夹具
├── helpers/        # 测试辅助工具
└── snapshots/      # 快照测试
```

### 测试命名约定
- 测试文件: `*.test.ts`
- 测试描述: 使用清晰的英文描述
- 测试数据: 放在 `fixtures/` 目录

### 测试覆盖率要求
- **核心算法**: 100% 行覆盖率
- **CLI功能**: 90% 分支覆盖率
- **插件系统**: 85% 覆盖率
- **缓存系统**: 90% 覆盖率
- **执行引擎**: 95% 覆盖率
- **规则系统**: 100% 覆盖率（包括JSX规则）
- **边界条件**: 充分测试错误情况和边界值

## 构建和部署

### 构建输出
- **库模式**: `dist/` 目录，包含类型声明
- **CLI模式**: `dist/cli/` 目录，可执行文件
- **类型声明**: 自动生成 `.d.ts` 文件

### 路径别名
```typescript
// tsconfig.json
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    }
  }
}
```

使用示例:
```typescript
import { ComplexityCalculator } from '@/core/calculator';
import type { CalculationOptions } from '@/core/types';
```

## 版本控制

### Git工作流
- **主分支**: `main` - 稳定版本
- **功能分支**: `feature/*` - 新功能开发
- **修复分支**: `fix/*` - 错误修复

### 提交规范
使用 Conventional Commits 格式:
- `feat: 添加新功能`
- `fix: 修复错误`
- `docs: 更新文档`
- `test: 添加测试`
- `refactor: 重构代码`