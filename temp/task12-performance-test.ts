/**
 * Task 12: 性能优化和基准测试实现
 * 
 * 本文件实现了完整的性能基准测试系统，包括：
 * 1. 大文件性能测试 (>5000行)
 * 2. 缓存系统性能验证
 * 3. Token查找性能优化测试
 * 4. 内存使用监控
 * 5. 性能对比分析
 */

import { performance } from 'perf_hooks';
import * as fs from 'fs';
import * as path from 'path';
import { ComplexityCalculator } from '../src/core/calculator';
import { PositionConverter } from '../src/utils/position-converter';
import { createLightweightFactory, CalculatorFactory } from '../src/core/calculator-factory';
import { BenchmarkSuite, RegressionTester, IntegratedPerformanceTest } from '../src/engine/performance-benchmark';

// 性能测试配置
interface PerformanceTestConfig {
  warmupRuns: number;
  benchmarkRuns: number;
  timeoutMs: number;
  memorySamplingInterval: number;
  cacheHitRateThreshold: number;
  maxMemoryIncrease: number; // 最大内存增长百分比
  targetAnalysisTime: number; // 目标分析时间(ms)
}

const PERFORMANCE_CONFIG: PerformanceTestConfig = {
  warmupRuns: 3,
  benchmarkRuns: 10,
  timeoutMs: 30000, // 30秒超时
  memorySamplingInterval: 100, // 100ms采样间隔
  cacheHitRateThreshold: 85, // 85%缓存命中率目标
  maxMemoryIncrease: 150, // 最大内存增长150%
  targetAnalysisTime: 5000, // 5秒目标分析时间
};

// 性能测试结果接口
interface PerformanceTestResult {
  testName: string;
  timestamp: Date;
  fileSize: number;
  lineCount: number;
  analysisTime: number;
  memoryUsage: {
    initial: NodeJS.MemoryUsage;
    peak: NodeJS.MemoryUsage;
    final: NodeJS.MemoryUsage;
    samples: NodeJS.MemoryUsage[];
  };
  cacheStats: {
    hitRate: number;
    size: number;
    memoryEstimate: number;
  };
  tokenSearchStats: {
    totalSearches: number;
    averageSearchTime: number;
    cacheHits: number;
    cacheMisses: number;
  };
  complexity: {
    totalComplexity: number;
    averageComplexity: number;
    maxComplexity: number;
    functionsAnalyzed: number;
  };
  passed: boolean;
  issues: string[];
}

// 内存监控器
class MemoryMonitor {
  private samples: NodeJS.MemoryUsage[] = [];
  private intervalId: NodeJS.Timeout | null = null;
  private initialMemory: NodeJS.MemoryUsage;
  private peakMemory: NodeJS.MemoryUsage;

  constructor() {
    this.initialMemory = process.memoryUsage();
    this.peakMemory = { ...this.initialMemory };
  }

  start(samplingInterval: number = 100): void {
    this.samples = [];
    this.intervalId = setInterval(() => {
      const currentMemory = process.memoryUsage();
      this.samples.push(currentMemory);
      
      // 更新峰值内存
      if (currentMemory.heapUsed > this.peakMemory.heapUsed) {
        this.peakMemory = currentMemory;
      }
    }, samplingInterval);
  }

  stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  getResults(): {
    initial: NodeJS.MemoryUsage;
    peak: NodeJS.MemoryUsage;
    final: NodeJS.MemoryUsage;
    samples: NodeJS.MemoryUsage[];
  } {
    return {
      initial: this.initialMemory,
      peak: this.peakMemory,
      final: process.memoryUsage(),
      samples: [...this.samples]
    };
  }

  getMemoryIncreasePercentage(): number {
    const current = process.memoryUsage();
    return ((current.heapUsed - this.initialMemory.heapUsed) / this.initialMemory.heapUsed) * 100;
  }
}

// Token搜索性能监控器
class TokenSearchMonitor {
  private searchCount = 0;
  private totalSearchTime = 0;
  private cacheHits = 0;
  private cacheMisses = 0;

  recordSearch(searchTime: number, wasFromCache: boolean): void {
    this.searchCount++;
    this.totalSearchTime += searchTime;
    
    if (wasFromCache) {
      this.cacheHits++;
    } else {
      this.cacheMisses++;
    }
  }

  getStats(): {
    totalSearches: number;
    averageSearchTime: number;
    cacheHits: number;
    cacheMisses: number;
  } {
    return {
      totalSearches: this.searchCount,
      averageSearchTime: this.searchCount > 0 ? this.totalSearchTime / this.searchCount : 0,
      cacheHits: this.cacheHits,
      cacheMisses: this.cacheMisses
    };
  }

  reset(): void {
    this.searchCount = 0;
    this.totalSearchTime = 0;
    this.cacheHits = 0;
    this.cacheMisses = 0;
  }
}

// 主性能测试器
export class Task12PerformanceTester {
  private benchmarkSuite: BenchmarkSuite;
  private regressionTester: RegressionTester;
  private config: PerformanceTestConfig;
  private memoryMonitor: MemoryMonitor;
  private tokenSearchMonitor: TokenSearchMonitor;

  constructor(config: Partial<PerformanceTestConfig> = {}) {
    this.config = { ...PERFORMANCE_CONFIG, ...config };
    this.benchmarkSuite = new BenchmarkSuite({
      warmupRuns: this.config.warmupRuns,
      benchmarkRuns: this.config.benchmarkRuns,
      memoryMonitoring: true,
      gcBetweenRuns: true,
      timeoutMs: this.config.timeoutMs,
    });
    this.regressionTester = new RegressionTester({
      baselineFile: 'performance-baseline-task12.json',
      tolerancePercent: 15, // 15%容忍度
      criticalMetrics: ['executionTime', 'memoryUsage.heapUsed', 'cacheHitRate'],
      autoSave: true,
      failOnRegression: false,
    });
    this.memoryMonitor = new MemoryMonitor();
    this.tokenSearchMonitor = new TokenSearchMonitor();
  }

  /**
   * 运行完整的Task 12性能测试套件
   */
  async runCompletePerformanceTest(): Promise<{
    largeFileTest: PerformanceTestResult;
    cacheSystemTest: PerformanceTestResult;
    tokenSearchTest: PerformanceTestResult;
    memoryStressTest: PerformanceTestResult;
    benchmarkComparison: any;
    summary: {
      allTestsPassed: boolean;
      totalIssues: number;
      performanceGrade: 'A' | 'B' | 'C' | 'D' | 'F';
      recommendations: string[];
    };
  }> {
    console.log('🚀 开始Task 12性能优化和基准测试...');
    
    // 清理缓存以确保测试准确性
    PositionConverter.clearCache();
    if (global.gc) {
      global.gc();
    }

    const results = {
      largeFileTest: await this.testLargeFilePerformance(),
      cacheSystemTest: await this.testCacheSystemPerformance(),
      tokenSearchTest: await this.testTokenSearchPerformance(),
      memoryStressTest: await this.testMemoryStressScenarios(),
      benchmarkComparison: await this.runBenchmarkComparison(),
      summary: {
        allTestsPassed: false,
        totalIssues: 0,
        performanceGrade: 'F' as const,
        recommendations: [] as string[],
      }
    };

    // 生成综合评估
    results.summary = this.generatePerformanceSummary([
      results.largeFileTest,
      results.cacheSystemTest,
      results.tokenSearchTest,
      results.memoryStressTest
    ]);

    // 保存详细报告
    await this.savePerformanceReport(results);

    console.log(`📊 性能测试完成! 总体评级: ${results.summary.performanceGrade}`);
    return results;
  }

  /**
   * 测试大文件性能 (>5000行)
   */
  private async testLargeFilePerformance(): Promise<PerformanceTestResult> {
    console.log('📁 测试大文件性能...');
    
    const testFilePath = path.join(__dirname, 'large-test-file.ts');
    let sourceCode: string;
    
    try {
      sourceCode = await fs.promises.readFile(testFilePath, 'utf-8');
    } catch (error) {
      throw new Error(`无法读取测试文件: ${testFilePath}`);
    }

    const lineCount = sourceCode.split('\n').length;
    const fileSize = Buffer.byteLength(sourceCode, 'utf-8');
    
    console.log(`📋 文件统计: ${lineCount} 行, ${(fileSize / 1024).toFixed(2)} KB`);

    if (lineCount < 5000) {
      throw new Error(`测试文件行数不足: ${lineCount} < 5000`);
    }

    const issues: string[] = [];
    this.memoryMonitor = new MemoryMonitor();
    this.memoryMonitor.start(this.config.memorySamplingInterval);

    const startTime = performance.now();
    
    try {
      // 使用完整配置的工厂
      const factory = new CalculatorFactory({
        enableMonitoring: true,
        enableCaching: true,
        ruleEngineConfig: { maxRuleConcurrency: 8 }
      });
      
      const calculator = new ComplexityCalculator({}, factory);
      
      try {
        const results = await calculator.calculateCode(sourceCode, testFilePath);
        const analysisTime = performance.now() - startTime;

        this.memoryMonitor.stop();
        const memoryResults = this.memoryMonitor.getResults();
        const cacheStats = PositionConverter.getCacheStats();

        // 验证性能指标
        if (analysisTime > this.config.targetAnalysisTime) {
          issues.push(`分析时间超标: ${analysisTime.toFixed(2)}ms > ${this.config.targetAnalysisTime}ms`);
        }

        if (cacheStats.hitRate < this.config.cacheHitRateThreshold) {
          issues.push(`缓存命中率不足: ${cacheStats.hitRate.toFixed(2)}% < ${this.config.cacheHitRateThreshold}%`);
        }

        const memoryIncrease = this.memoryMonitor.getMemoryIncreasePercentage();
        if (memoryIncrease > this.config.maxMemoryIncrease) {
          issues.push(`内存使用超标: ${memoryIncrease.toFixed(2)}% > ${this.config.maxMemoryIncrease}%`);
        }

        // 计算复杂度统计
        const complexityStats = this.calculateComplexityStats(results);

        const result: PerformanceTestResult = {
          testName: 'large-file-performance',
          timestamp: new Date(),
          fileSize,
          lineCount,
          analysisTime,
          memoryUsage: memoryResults,
          cacheStats: {
            hitRate: cacheStats.hitRate * 100,
            size: cacheStats.size,
            memoryEstimate: cacheStats.memoryEstimate
          },
          tokenSearchStats: this.tokenSearchMonitor.getStats(),
          complexity: complexityStats,
          passed: issues.length === 0,
          issues
        };

        console.log(`✅ 大文件测试完成: ${result.passed ? '通过' : '失败'} (${issues.length} 个问题)`);
        return result;

      } finally {
        await calculator.dispose();
      }
    } catch (error) {
      this.memoryMonitor.stop();
      issues.push(`分析过程出错: ${error instanceof Error ? error.message : String(error)}`);
      
      return {
        testName: 'large-file-performance',
        timestamp: new Date(),
        fileSize,
        lineCount,
        analysisTime: performance.now() - startTime,
        memoryUsage: this.memoryMonitor.getResults(),
        cacheStats: { hitRate: 0, size: 0, memoryEstimate: 0 },
        tokenSearchStats: { totalSearches: 0, averageSearchTime: 0, cacheHits: 0, cacheMisses: 0 },
        complexity: { totalComplexity: 0, averageComplexity: 0, maxComplexity: 0, functionsAnalyzed: 0 },
        passed: false,
        issues
      };
    }
  }

  /**
   * 测试缓存系统性能
   */
  private async testCacheSystemPerformance(): Promise<PerformanceTestResult> {
    console.log('🗄️ 测试缓存系统性能...');
    
    const testFilePath = path.join(__dirname, 'large-test-file.ts');
    const sourceCode = await fs.promises.readFile(testFilePath, 'utf-8');
    const issues: string[] = [];

    // 清理缓存开始
    PositionConverter.clearCache();
    this.memoryMonitor = new MemoryMonitor();
    this.memoryMonitor.start();

    const factory = createLightweightFactory();
    const calculator = new ComplexityCalculator({}, factory);

    try {
      const startTime = performance.now();

      // 第一次运行 - 冷缓存
      await calculator.calculateCode(sourceCode, testFilePath);
      const firstRunTime = performance.now() - startTime;

      // 获取第一次运行后的缓存统计
      const cacheStatsAfterFirst = PositionConverter.getCacheStats();

      // 第二次运行 - 热缓存
      const secondStartTime = performance.now();
      await calculator.calculateCode(sourceCode, testFilePath);
      const secondRunTime = performance.now() - secondStartTime;

      // 获取第二次运行后的缓存统计
      const finalCacheStats = PositionConverter.getCacheStats();

      this.memoryMonitor.stop();
      const memoryResults = this.memoryMonitor.getResults();

      // 验证缓存性能
      const speedImprovement = ((firstRunTime - secondRunTime) / firstRunTime) * 100;
      if (speedImprovement < 20) {
        issues.push(`缓存性能提升不足: ${speedImprovement.toFixed(2)}% < 20%`);
      }

      if (finalCacheStats.hitRate < this.config.cacheHitRateThreshold / 100) {
        issues.push(`缓存命中率不足: ${(finalCacheStats.hitRate * 100).toFixed(2)}% < ${this.config.cacheHitRateThreshold}%`);
      }

      // 测试缓存容量和清理
      await this.testCacheCapacityAndCleanup();

      const result: PerformanceTestResult = {
        testName: 'cache-system-performance',
        timestamp: new Date(),
        fileSize: Buffer.byteLength(sourceCode, 'utf-8'),
        lineCount: sourceCode.split('\n').length,
        analysisTime: secondRunTime,
        memoryUsage: memoryResults,
        cacheStats: {
          hitRate: finalCacheStats.hitRate * 100,
          size: finalCacheStats.size,
          memoryEstimate: finalCacheStats.memoryEstimate
        },
        tokenSearchStats: this.tokenSearchMonitor.getStats(),
        complexity: { totalComplexity: 0, averageComplexity: 0, maxComplexity: 0, functionsAnalyzed: 0 },
        passed: issues.length === 0,
        issues
      };

      console.log(`✅ 缓存系统测试完成: ${result.passed ? '通过' : '失败'} (性能提升: ${speedImprovement.toFixed(2)}%)`);
      return result;

    } finally {
      await calculator.dispose();
    }
  }

  /**
   * 测试Token搜索性能
   */
  private async testTokenSearchPerformance(): Promise<PerformanceTestResult> {
    console.log('🔍 测试Token搜索性能...');
    
    const testCases = await this.generateTokenSearchTestCases();
    const issues: string[] = [];
    
    this.tokenSearchMonitor.reset();
    this.memoryMonitor = new MemoryMonitor();
    this.memoryMonitor.start();

    const startTime = performance.now();
    let totalSearches = 0;
    let totalSearchTime = 0;

    for (const testCase of testCases) {
      const searchStartTime = performance.now();
      
      try {
        // 模拟Token搜索 - 在实际实现中会调用具体的Token搜索方法
        const position = PositionConverter.spanToPositionWithSmartFallback(
          testCase.sourceCode,
          testCase.spanStart,
          testCase.spanEnd,
          testCase.filePath
        );
        
        const searchTime = performance.now() - searchStartTime;
        totalSearchTime += searchTime;
        totalSearches++;
        
        // 记录搜索统计
        this.tokenSearchMonitor.recordSearch(searchTime, false); // 简化实现，实际需要检测缓存命中
        
        // 验证搜索结果的合理性
        if (position.line < 1 || position.column < 1) {
          issues.push(`Token搜索返回无效位置: (${position.line}, ${position.column})`);
        }
      } catch (error) {
        issues.push(`Token搜索失败: ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    const totalTime = performance.now() - startTime;
    this.memoryMonitor.stop();
    
    const averageSearchTime = totalSearches > 0 ? totalSearchTime / totalSearches : 0;
    const searchesPerSecond = totalSearches > 0 ? (totalSearches / totalTime) * 1000 : 0;

    // 性能阈值验证
    if (averageSearchTime > 10) { // 平均搜索时间不应超过10ms
      issues.push(`Token搜索平均时间过长: ${averageSearchTime.toFixed(2)}ms > 10ms`);
    }

    if (searchesPerSecond < 100) { // 每秒至少应该能处理100次搜索
      issues.push(`Token搜索吞吐量不足: ${searchesPerSecond.toFixed(2)} searches/sec < 100`);
    }

    const result: PerformanceTestResult = {
      testName: 'token-search-performance',
      timestamp: new Date(),
      fileSize: 0,
      lineCount: 0,
      analysisTime: totalTime,
      memoryUsage: this.memoryMonitor.getResults(),
      cacheStats: {
        hitRate: 0,
        size: PositionConverter.getCacheStats().size,
        memoryEstimate: PositionConverter.getCacheStats().memoryEstimate
      },
      tokenSearchStats: {
        totalSearches,
        averageSearchTime,
        cacheHits: this.tokenSearchMonitor.getStats().cacheHits,
        cacheMisses: this.tokenSearchMonitor.getStats().cacheMisses
      },
      complexity: { totalComplexity: 0, averageComplexity: 0, maxComplexity: 0, functionsAnalyzed: 0 },
      passed: issues.length === 0,
      issues
    };

    console.log(`✅ Token搜索测试完成: ${result.passed ? '通过' : '失败'} (${searchesPerSecond.toFixed(2)} searches/sec)`);
    return result;
  }

  /**
   * 测试内存压力场景
   */
  private async testMemoryStressScenarios(): Promise<PerformanceTestResult> {
    console.log('💾 测试内存压力场景...');
    
    const issues: string[] = [];
    this.memoryMonitor = new MemoryMonitor();
    this.memoryMonitor.start();

    const startTime = performance.now();
    const testFiles: string[] = [];
    
    // 生成多个测试文件内容
    const baseContent = await fs.promises.readFile(
      path.join(__dirname, 'large-test-file.ts'), 
      'utf-8'
    );

    for (let i = 0; i < 5; i++) {
      testFiles.push(baseContent + `\n// File variation ${i}\n` + 'const var' + i + ' = ' + i + ';');
    }

    const factory = new CalculatorFactory({
      enableMonitoring: true,
      enableCaching: true,
      ruleEngineConfig: { maxRuleConcurrency: 4 }
    });

    const calculator = new ComplexityCalculator({}, factory);

    try {
      let totalComplexity = 0;
      let functionsAnalyzed = 0;

      // 批量处理文件以测试内存压力
      for (let batchIndex = 0; batchIndex < 3; batchIndex++) {
        console.log(`处理批次 ${batchIndex + 1}/3...`);
        
        const promises = testFiles.map(async (content, index) => {
          const filePath = `stress-test-${batchIndex}-${index}.ts`;
          const results = await calculator.calculateCode(content, filePath);
          
          totalComplexity += results.totalComplexity || 0;
          functionsAnalyzed += results.functions?.length || 0;
          
          return results;
        });

        await Promise.all(promises);

        // 检查内存使用情况
        const currentMemoryIncrease = this.memoryMonitor.getMemoryIncreasePercentage();
        if (currentMemoryIncrease > this.config.maxMemoryIncrease * 1.5) { // 压力测试允许更高内存使用
          issues.push(`批次 ${batchIndex + 1} 内存使用过高: ${currentMemoryIncrease.toFixed(2)}%`);
        }

        // 强制垃圾回收
        if (global.gc) {
          global.gc();
        }
      }

      const totalTime = performance.now() - startTime;
      this.memoryMonitor.stop();

      // 验证内存是否有泄漏
      const finalMemoryIncrease = this.memoryMonitor.getMemoryIncreasePercentage();
      if (finalMemoryIncrease > this.config.maxMemoryIncrease * 2) {
        issues.push(`最终内存增长过高，可能存在内存泄漏: ${finalMemoryIncrease.toFixed(2)}%`);
      }

      const result: PerformanceTestResult = {
        testName: 'memory-stress-test',
        timestamp: new Date(),
        fileSize: Buffer.byteLength(baseContent, 'utf-8') * testFiles.length * 3,
        lineCount: baseContent.split('\n').length * testFiles.length * 3,
        analysisTime: totalTime,
        memoryUsage: this.memoryMonitor.getResults(),
        cacheStats: {
          hitRate: PositionConverter.getCacheStats().hitRate * 100,
          size: PositionConverter.getCacheStats().size,
          memoryEstimate: PositionConverter.getCacheStats().memoryEstimate
        },
        tokenSearchStats: this.tokenSearchMonitor.getStats(),
        complexity: {
          totalComplexity,
          averageComplexity: functionsAnalyzed > 0 ? totalComplexity / functionsAnalyzed : 0,
          maxComplexity: 0, // 简化实现
          functionsAnalyzed
        },
        passed: issues.length === 0,
        issues
      };

      console.log(`✅ 内存压力测试完成: ${result.passed ? '通过' : '失败'} (内存增长: ${finalMemoryIncrease.toFixed(2)}%)`);
      return result;

    } finally {
      await calculator.dispose();
    }
  }

  /**
   * 运行基准测试对比
   */
  private async runBenchmarkComparison(): Promise<any> {
    console.log('📊 运行基准测试对比...');
    
    const testFilePath = path.join(__dirname, 'large-test-file.ts');
    const sourceCode = await fs.promises.readFile(testFilePath, 'utf-8');

    // 运行基准测试
    const benchmarkResult = await this.benchmarkSuite.runBenchmark(
      'complete-analysis-benchmark',
      async () => {
        const factory = createLightweightFactory();
        const calculator = new ComplexityCalculator({}, factory);
        
        try {
          await calculator.calculateCode(sourceCode, testFilePath);
        } finally {
          await calculator.dispose();
        }
      }
    );

    // 运行回归测试
    const regressionResult = await this.regressionTester.runRegressionTest(
      'complete-analysis-benchmark',
      benchmarkResult
    );

    return {
      benchmark: benchmarkResult,
      regression: regressionResult,
      comparison: this.benchmarkSuite.compareResults(
        benchmarkResult,
        benchmarkResult // 简化实现，实际应该与历史基线对比
      )
    };
  }

  /**
   * 生成性能摘要
   */
  private generatePerformanceSummary(results: PerformanceTestResult[]): {
    allTestsPassed: boolean;
    totalIssues: number;
    performanceGrade: 'A' | 'B' | 'C' | 'D' | 'F';
    recommendations: string[];
  } {
    const allTestsPassed = results.every(r => r.passed);
    const totalIssues = results.reduce((sum, r) => sum + r.issues.length, 0);
    const recommendations: string[] = [];

    // 性能等级评估
    let grade: 'A' | 'B' | 'C' | 'D' | 'F' = 'F';
    const largeFileTest = results.find(r => r.testName === 'large-file-performance');
    
    if (largeFileTest) {
      if (largeFileTest.analysisTime <= 3000 && largeFileTest.cacheStats.hitRate >= 90) {
        grade = 'A'; // 优秀
      } else if (largeFileTest.analysisTime <= 5000 && largeFileTest.cacheStats.hitRate >= 85) {
        grade = 'B'; // 良好
      } else if (largeFileTest.analysisTime <= 8000 && largeFileTest.cacheStats.hitRate >= 70) {
        grade = 'C'; // 一般
      } else if (largeFileTest.analysisTime <= 15000) {
        grade = 'D'; // 较差
      }
    }

    // 生成改进建议
    results.forEach(result => {
      if (result.issues.length > 0) {
        result.issues.forEach(issue => {
          if (issue.includes('分析时间超标')) {
            recommendations.push('优化算法复杂度，考虑并行处理');
          }
          if (issue.includes('缓存命中率不足')) {
            recommendations.push('改进缓存策略，增加缓存键的精确性');
          }
          if (issue.includes('内存使用超标')) {
            recommendations.push('实现更有效的内存管理和对象池');
          }
          if (issue.includes('Token搜索')) {
            recommendations.push('优化Token搜索算法，使用更高效的数据结构');
          }
        });
      }
    });

    // 去重建议
    const uniqueRecommendations = [...new Set(recommendations)];

    return {
      allTestsPassed,
      totalIssues,
      performanceGrade: grade,
      recommendations: uniqueRecommendations
    };
  }

  /**
   * 保存性能报告
   */
  private async savePerformanceReport(results: any): Promise<void> {
    const reportPath = path.join(__dirname, '../reports');
    
    // 确保报告目录存在
    try {
      await fs.promises.access(reportPath);
    } catch {
      await fs.promises.mkdir(reportPath, { recursive: true });
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportFile = path.join(reportPath, `task12-performance-report-${timestamp}.json`);
    
    const report = {
      ...results,
      metadata: {
        testVersion: '1.0.0',
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        cpuCount: require('os').cpus().length,
        totalMemory: require('os').totalmem(),
        timestamp: new Date().toISOString()
      }
    };

    await fs.promises.writeFile(reportFile, JSON.stringify(report, null, 2), 'utf-8');
    console.log(`📄 性能报告已保存到: ${reportFile}`);
  }

  // 辅助方法

  private calculateComplexityStats(results: any): {
    totalComplexity: number;
    averageComplexity: number;
    maxComplexity: number;
    functionsAnalyzed: number;
  } {
    if (!results.functions || results.functions.length === 0) {
      return {
        totalComplexity: 0,
        averageComplexity: 0,
        maxComplexity: 0,
        functionsAnalyzed: 0
      };
    }

    const complexities = results.functions.map((f: any) => f.complexity || 0);
    const totalComplexity = complexities.reduce((sum: number, c: number) => sum + c, 0);
    const maxComplexity = Math.max(...complexities);
    const averageComplexity = totalComplexity / complexities.length;

    return {
      totalComplexity,
      averageComplexity,
      maxComplexity,
      functionsAnalyzed: results.functions.length
    };
  }

  private async generateTokenSearchTestCases(): Promise<Array<{
    sourceCode: string;
    spanStart: number;
    spanEnd: number;
    filePath: string;
  }>> {
    const testCases = [];
    const baseContent = await fs.promises.readFile(
      path.join(__dirname, 'large-test-file.ts'), 
      'utf-8'
    );

    // 生成不同位置的测试用例
    for (let i = 0; i < 50; i++) {
      const start = Math.floor(Math.random() * (baseContent.length - 100));
      const end = start + Math.floor(Math.random() * 100) + 10;
      
      testCases.push({
        sourceCode: baseContent,
        spanStart: start,
        spanEnd: end,
        filePath: `test-case-${i}.ts`
      });
    }

    return testCases;
  }

  private async testCacheCapacityAndCleanup(): Promise<void> {
    // 测试缓存容量限制和清理机制
    const testFiles = [];
    for (let i = 0; i < 200; i++) {
      testFiles.push(`test content ${i}`.repeat(100));
    }

    // 填充缓存到接近容量上限
    testFiles.forEach((content, index) => {
      PositionConverter.spanToPosition(content, Math.floor(content.length / 2));
    });

    const cacheStats = PositionConverter.getCacheStats();
    console.log(`缓存测试: ${cacheStats.size} 个条目, 内存估算: ${(cacheStats.memoryEstimate / 1024).toFixed(2)} KB`);
  }
}

// 便捷函数用于快速运行测试
export async function runTask12PerformanceTest(): Promise<void> {
  const tester = new Task12PerformanceTester();
  const results = await tester.runCompletePerformanceTest();
  
  console.log('\n🎯 Task 12 性能测试总结:');
  console.log(`- 大文件测试: ${results.largeFileTest.passed ? '✅' : '❌'}`);
  console.log(`- 缓存系统测试: ${results.cacheSystemTest.passed ? '✅' : '❌'}`);
  console.log(`- Token搜索测试: ${results.tokenSearchTest.passed ? '✅' : '❌'}`);
  console.log(`- 内存压力测试: ${results.memoryStressTest.passed ? '✅' : '❌'}`);
  console.log(`- 性能等级: ${results.summary.performanceGrade}`);
  console.log(`- 总问题数: ${results.summary.totalIssues}`);
  
  if (results.summary.recommendations.length > 0) {
    console.log('\n📋 改进建议:');
    results.summary.recommendations.forEach((rec, index) => {
      console.log(`${index + 1}. ${rec}`);
    });
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runTask12PerformanceTest().catch(console.error);
}