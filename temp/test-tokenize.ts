#!/usr/bin/env bun

import { tokenize } from '@swc/core';

const sourceCode = `
function test() {
  if (condition) {
    for (let i = 0; i < 10; i++) {
      while (flag) {
        break;
      }
    }
  }
}
`;

async function testTokenizeAPI() {
  console.log('Testing @swc/core tokenize API...');
  
  try {
    // 测试 tokenize API
    const tokens = await tokenize(sourceCode, {
      syntax: 'typescript',
      jsx: false,
    });
    
    console.log(`✓ Tokenization successful! Found ${tokens.length} tokens`);
    
    // 查找关键字 tokens
    const keywords = ['if', 'for', 'while', 'function'];
    
    for (const keyword of keywords) {
      const found = tokens.filter(tokenAndSpan => {
        const { token } = tokenAndSpan;
        // 检查不同可能的 token 结构
        return (
          (token as any).Keyword === keyword ||
          (token as any).type === 'Keyword' && (token as any).value === keyword ||
          (token as any) === keyword
        );
      });
      
      console.log(`\n'${keyword}' keyword found ${found.length} times:`);
      found.forEach(t => {
        console.log(`  - Span: [${t.span.start}, ${t.span.end}]`);
        console.log(`  - Token:`, JSON.stringify(t.token, null, 2));
      });
    }
    
    // 展示前10个tokens的结构
    console.log('\nFirst 10 tokens structure:');
    tokens.slice(0, 10).forEach((t, i) => {
      console.log(`${i}: Span=[${t.span.start}, ${t.span.end}], Token=`, JSON.stringify(t.token, null, 2));
    });
    
  } catch (error) {
    console.error('✗ Tokenization failed:', error);
    console.error('Error details:', error.message);
    
    // 尝试替代方法
    console.log('\nTrying alternative approaches...');
    
    // 检查是否有其他可用的API
    try {
      const swcCore = await import('@swc/core');
      console.log('Available SWC exports:', Object.keys(swcCore));
    } catch (importError) {
      console.error('Failed to inspect SWC exports:', importError);
    }
  }
}

testTokenizeAPI().catch(console.error);