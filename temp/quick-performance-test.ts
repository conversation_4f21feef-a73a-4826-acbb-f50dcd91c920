/**
 * Task 12: 快速性能验证测试
 * 专注于核心性能指标的快速验证
 */

import { performance } from 'perf_hooks';
import * as fs from 'fs';
import * as path from 'path';
import { ComplexityCalculator } from '../src/core/calculator';
import { PositionConverter } from '../src/utils/position-converter';
import { createLightweightFactory, CalculatorFactory } from '../src/core/calculator-factory';

// 核心性能指标
interface QuickTestResult {
  testName: string;
  passed: boolean;
  analysisTime: number;
  fileSize: number;
  lineCount: number;
  memoryUsage: {
    before: NodeJS.MemoryUsage;
    after: NodeJS.MemoryUsage;
    increase: number;
  };
  cacheStats: {
    hitRate: number;
    size: number;
    memoryEstimate: number;
  };
  issues: string[];
}

class QuickPerformanceTester {
  async runQuickPerformanceTest(): Promise<{
    largeFileTest: QuickTestResult;
    cacheTest: QuickTestResult;
    memoryTest: QuickTestResult;
    summary: {
      allPassed: boolean;
      totalIssues: number;
      grade: 'A' | 'B' | 'C' | 'D' | 'F';
    };
  }> {
    console.log('🚀 开始Task 12快速性能验证...');
    
    const results = {
      largeFileTest: await this.testLargeFileQuick(),
      cacheTest: await this.testCacheQuick(),
      memoryTest: await this.testMemoryQuick(),
      summary: {
        allPassed: false,
        totalIssues: 0,
        grade: 'F' as const
      }
    };

    // 计算摘要
    const allPassed = results.largeFileTest.passed && results.cacheTest.passed && results.memoryTest.passed;
    const totalIssues = results.largeFileTest.issues.length + results.cacheTest.issues.length + results.memoryTest.issues.length;
    
    let grade: 'A' | 'B' | 'C' | 'D' | 'F' = 'F';
    if (allPassed && results.largeFileTest.analysisTime <= 3000) {
      grade = 'A';
    } else if (allPassed && results.largeFileTest.analysisTime <= 5000) {
      grade = 'B';
    } else if (results.largeFileTest.analysisTime <= 10000 && totalIssues <= 2) {
      grade = 'C';
    } else if (results.largeFileTest.analysisTime <= 15000) {
      grade = 'D';
    }

    results.summary = { allPassed, totalIssues, grade };

    console.log(`\n📊 快速性能测试完成! 总体评级: ${grade}`);
    return results;
  }

  private async testLargeFileQuick(): Promise<QuickTestResult> {
    console.log('📁 测试大文件性能...');
    
    const testFilePath = path.join(__dirname, 'large-test-file.ts');
    const sourceCode = await fs.promises.readFile(testFilePath, 'utf-8');
    const lineCount = sourceCode.split('\n').length;
    const fileSize = Buffer.byteLength(sourceCode, 'utf-8');
    
    console.log(`📋 文件统计: ${lineCount} 行, ${(fileSize / 1024).toFixed(2)} KB`);

    if (lineCount < 5000) {
      throw new Error(`测试文件行数不足: ${lineCount} < 5000`);
    }

    const issues: string[] = [];
    const memoryBefore = process.memoryUsage();
    PositionConverter.clearCache();

    const startTime = performance.now();
    
    try {
      const factory = new CalculatorFactory({
        enableMonitoring: true,
        enableCaching: true,
        ruleEngineConfig: { maxRuleConcurrency: 4 }
      });
      
      const calculator = new ComplexityCalculator({}, factory);
      
      try {
        const results = await calculator.calculateCode(sourceCode, testFilePath);
        const analysisTime = performance.now() - startTime;
        const memoryAfter = process.memoryUsage();
        const memoryIncrease = ((memoryAfter.heapUsed - memoryBefore.heapUsed) / memoryBefore.heapUsed) * 100;

        const cacheStats = PositionConverter.getCacheStats();

        // 验证核心性能指标
        if (analysisTime > 5000) {
          issues.push(`分析时间超标: ${analysisTime.toFixed(2)}ms > 5000ms`);
        }

        if (cacheStats.hitRate < 0.85) {
          issues.push(`缓存命中率不足: ${(cacheStats.hitRate * 100).toFixed(2)}% < 85%`);
        }

        if (memoryIncrease > 150) {
          issues.push(`内存使用超标: ${memoryIncrease.toFixed(2)}% > 150%`);
        }

        console.log(`✅ 大文件测试完成: ${issues.length === 0 ? '通过' : '失败'} (${issues.length} 个问题)`);
        
        return {
          testName: 'large-file-quick',
          passed: issues.length === 0,
          analysisTime,
          fileSize,
          lineCount,
          memoryUsage: {
            before: memoryBefore,
            after: memoryAfter,
            increase: memoryIncrease
          },
          cacheStats: {
            hitRate: cacheStats.hitRate * 100,
            size: cacheStats.size,
            memoryEstimate: cacheStats.memoryEstimate
          },
          issues
        };

      } finally {
        await calculator.dispose();
      }
    } catch (error) {
      issues.push(`分析过程出错: ${error instanceof Error ? error.message : String(error)}`);
      
      return {
        testName: 'large-file-quick',
        passed: false,
        analysisTime: performance.now() - startTime,
        fileSize,
        lineCount,
        memoryUsage: {
          before: memoryBefore,
          after: process.memoryUsage(),
          increase: 0
        },
        cacheStats: { hitRate: 0, size: 0, memoryEstimate: 0 },
        issues
      };
    }
  }

  private async testCacheQuick(): Promise<QuickTestResult> {
    console.log('🗄️ 测试缓存系统性能...');
    
    const testFilePath = path.join(__dirname, 'large-test-file.ts');
    const sourceCode = await fs.promises.readFile(testFilePath, 'utf-8');
    const issues: string[] = [];

    PositionConverter.clearCache();
    const memoryBefore = process.memoryUsage();
    const factory = createLightweightFactory();
    const calculator = new ComplexityCalculator({}, factory);

    try {
      const startTime = performance.now();

      // 第一次运行 - 冷缓存
      await calculator.calculateCode(sourceCode, testFilePath);
      const firstRunTime = performance.now() - startTime;

      // 第二次运行 - 热缓存
      const secondStartTime = performance.now();
      await calculator.calculateCode(sourceCode, testFilePath);
      const secondRunTime = performance.now() - secondStartTime;

      const memoryAfter = process.memoryUsage();
      const cacheStats = PositionConverter.getCacheStats();

      // 验证缓存性能
      const speedImprovement = ((firstRunTime - secondRunTime) / firstRunTime) * 100;
      if (speedImprovement < 20) {
        issues.push(`缓存性能提升不足: ${speedImprovement.toFixed(2)}% < 20%`);
      }

      if (cacheStats.hitRate < 0.85) {
        issues.push(`缓存命中率不足: ${(cacheStats.hitRate * 100).toFixed(2)}% < 85%`);
      }

      console.log(`✅ 缓存系统测试完成: ${issues.length === 0 ? '通过' : '失败'} (性能提升: ${speedImprovement.toFixed(2)}%)`);
      
      return {
        testName: 'cache-quick',
        passed: issues.length === 0,
        analysisTime: secondRunTime,
        fileSize: Buffer.byteLength(sourceCode, 'utf-8'),
        lineCount: sourceCode.split('\n').length,
        memoryUsage: {
          before: memoryBefore,
          after: memoryAfter,
          increase: ((memoryAfter.heapUsed - memoryBefore.heapUsed) / memoryBefore.heapUsed) * 100
        },
        cacheStats: {
          hitRate: cacheStats.hitRate * 100,
          size: cacheStats.size,
          memoryEstimate: cacheStats.memoryEstimate
        },
        issues
      };

    } finally {
      await calculator.dispose();
    }
  }

  private async testMemoryQuick(): Promise<QuickTestResult> {
    console.log('💾 测试内存使用情况...');
    
    const testFilePath = path.join(__dirname, 'large-test-file.ts');
    const sourceCode = await fs.promises.readFile(testFilePath, 'utf-8');
    const issues: string[] = [];

    const memoryBefore = process.memoryUsage();
    const factory = new CalculatorFactory({
      enableMonitoring: true,
      enableCaching: true,
      ruleEngineConfig: { maxRuleConcurrency: 2 }
    });

    const calculator = new ComplexityCalculator({}, factory);

    try {
      const startTime = performance.now();

      // 连续处理相同文件3次以测试内存累积
      for (let i = 0; i < 3; i++) {
        await calculator.calculateCode(sourceCode, `${testFilePath}-${i}`);
        
        if (global.gc) {
          global.gc();
        }
      }

      const analysisTime = performance.now() - startTime;
      const memoryAfter = process.memoryUsage();
      const memoryIncrease = ((memoryAfter.heapUsed - memoryBefore.heapUsed) / memoryBefore.heapUsed) * 100;

      // 验证内存使用
      if (memoryIncrease > 200) { // 多次处理允许更高内存使用
        issues.push(`内存使用过高: ${memoryIncrease.toFixed(2)}% > 200%`);
      }

      const cacheStats = PositionConverter.getCacheStats();

      console.log(`✅ 内存测试完成: ${issues.length === 0 ? '通过' : '失败'} (内存增长: ${memoryIncrease.toFixed(2)}%)`);
      
      return {
        testName: 'memory-quick',
        passed: issues.length === 0,
        analysisTime,
        fileSize: Buffer.byteLength(sourceCode, 'utf-8') * 3,
        lineCount: sourceCode.split('\n').length * 3,
        memoryUsage: {
          before: memoryBefore,
          after: memoryAfter,
          increase: memoryIncrease
        },
        cacheStats: {
          hitRate: cacheStats.hitRate * 100,
          size: cacheStats.size,
          memoryEstimate: cacheStats.memoryEstimate
        },
        issues
      };

    } finally {
      await calculator.dispose();
    }
  }
}

// 运行快速测试
async function runQuickTest(): Promise<void> {
  const tester = new QuickPerformanceTester();
  const results = await tester.runQuickPerformanceTest();
  
  console.log('\n🎯 Task 12 快速性能测试总结:');
  console.log(`- 大文件测试: ${results.largeFileTest.passed ? '✅' : '❌'} (${results.largeFileTest.analysisTime.toFixed(2)}ms)`);
  console.log(`- 缓存系统测试: ${results.cacheTest.passed ? '✅' : '❌'} (命中率: ${results.cacheTest.cacheStats.hitRate.toFixed(2)}%)`);
  console.log(`- 内存测试: ${results.memoryTest.passed ? '✅' : '❌'} (增长: ${results.memoryTest.memoryUsage.increase.toFixed(2)}%)`);
  console.log(`- 性能等级: ${results.summary.grade}`);
  console.log(`- 总问题数: ${results.summary.totalIssues}`);
  
  if (results.summary.totalIssues > 0) {
    console.log('\n❌ 发现的问题:');
    [results.largeFileTest, results.cacheTest, results.memoryTest].forEach(test => {
      if (test.issues.length > 0) {
        console.log(`\n${test.testName}:`);
        test.issues.forEach(issue => console.log(`  - ${issue}`));
      }
    });
  }

  // 保存结果
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const reportPath = path.join(__dirname, `../reports/task12-quick-test-${timestamp}.json`);
  
  try {
    await fs.promises.mkdir(path.dirname(reportPath), { recursive: true });
    await fs.promises.writeFile(reportPath, JSON.stringify({
      ...results,
      metadata: {
        testVersion: '1.0.0',
        nodeVersion: process.version,
        platform: process.platform,
        timestamp: new Date().toISOString()
      }
    }, null, 2));
    console.log(`\n📄 测试报告已保存到: ${reportPath}`);
  } catch (error) {
    console.warn('保存报告失败:', error);
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runQuickTest().catch(console.error);
}

export { QuickPerformanceTester, runQuickTest };