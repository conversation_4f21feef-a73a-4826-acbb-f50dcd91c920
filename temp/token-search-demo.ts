#!/usr/bin/env bun

import { ComplexityVisitor } from '../src/core/complexity-visitor';
import { DetailCollector } from '../src/core/detail-collector';
import { ASTParser } from '../src/core/parser';
import chalk from 'chalk';

/**
 * Token查找系统演示
 * 展示三级降级策略和精确关键字定位的功能
 */

const demoCode = `
function complexExample() {
  // 不同类型的控制流语句
  if (condition1) {                        // if关键字定位
    console.log('outer if');
    
    for (let i = 0; i < items.length; i++) {  // for关键字定位
      if (items[i].isValid) {                 // 嵌套if关键字定位
        processItem(items[i]);
        
        while (hasMoreWork()) {               // while关键字定位
          doWork();
        }
      }
    }
  } else if (condition2) {                   // else if组合
    switch (getType()) {                     // switch关键字定位
      case 'A':
        handleTypeA();
        break;
      case 'B':
        try {                                // try关键字定位
          handleTypeB();
        } catch (error) {                    // catch关键字定位
          console.error('Error:', error);
        }
        break;
      default:
        handleDefault();
    }
  }
  
  // 逻辑运算符（不是关键字但测试Token系统）
  return condition3 && condition4 || defaultValue;
}

// 箭头函数测试
const arrowFunction = (param) => {           // =>查找测试
  if (param) return param * 2;
};

// 紧凑格式测试
function compact(){if(a>0){for(let i=0;i<10;i++){if(b)break;}}}
`;

async function demonstrateTokenSearch() {
  console.log(chalk.blue.bold('🔍 SWC Token查找系统演示\n'));
  
  try {
    const parser = new ASTParser();
    const ast = await parser.parseCode(demoCode, 'demo.ts');
    
    // 创建带详细收集器的访问者
    const detailCollector = new DetailCollector();
    const visitor = new ComplexityVisitor(demoCode, detailCollector);
    
    console.log(chalk.yellow('源代码:'));
    console.log(chalk.gray(demoCode));
    console.log('\n' + '='.repeat(80) + '\n');
    
    // 分析代码
    const startTime = Date.now();
    visitor.visit(ast);
    const endTime = Date.now();
    
    const results = visitor.getResults();
    
    console.log(chalk.green.bold(`✅ 分析完成! 用时: ${endTime - startTime}ms\n`));
    
    // 显示函数分析结果
    results.forEach((result, index) => {
      console.log(chalk.blue.bold(`📊 函数 ${index + 1}: ${result.name}`));
      console.log(chalk.cyan(`   位置: 第${result.line}行，第${result.column}列`));
      console.log(chalk.cyan(`   总复杂度: ${result.complexity}`));
      
      if (result.details && result.details.length > 0) {
        console.log(chalk.yellow('\n   🔍 Token查找详情:'));
        
        // 筛选Token查找相关的步骤
        const tokenSearchSteps = result.details.filter(step => 
          step.ruleId === 'token-search' || 
          step.ruleId === 'span-validation' ||
          step.ruleId === 'span-fallback'
        );
        
        if (tokenSearchSteps.length > 0) {
          tokenSearchSteps.forEach(step => {
            const statusIcon = step.context?.includes('成功') ? '✅' : 
                              step.context?.includes('失败') ? '❌' : '🔍';
            
            console.log(`   ${statusIcon} ${step.description}`);
            console.log(chalk.gray(`      ${step.context || '无上下文信息'}`));
          });
        } else {
          console.log(chalk.gray('      (无Token查找记录)'));
        }
        
        console.log(chalk.yellow('\n   📋 复杂度计算步骤:'));
        const complexitySteps = result.details.filter(step => step.increment > 0);
        
        complexitySteps.forEach(step => {
          const ruleIcon = getRuleIcon(step.ruleId);
          console.log(`   ${ruleIcon} ${step.description} (+${step.increment}) 位置: ${step.line}:${step.column}`);
          if (step.context) {
            console.log(chalk.gray(`      ${step.context}`));
          }
        });
      }
      
      console.log('\n' + '-'.repeat(60) + '\n');
    });
    
    // 测试直接API调用
    console.log(chalk.magenta.bold('🧪 直接API测试:'));
    
    const testCases = [
      { type: 'IfStatement', keyword: 'if' },
      { type: 'ForStatement', keyword: 'for' },
      { type: 'WhileStatement', keyword: 'while' },
      { type: 'SwitchStatement', keyword: 'switch' },
      { type: 'ArrowFunctionExpression', keyword: '=>' }
    ];
    
    for (const testCase of testCases) {
      console.log(chalk.cyan(`\n测试查找 '${testCase.keyword}' 关键字:`));
      
      const mockNode = { 
        type: testCase.type, 
        span: { start: 0, end: demoCode.length, ctxt: 0 } 
      };
      
      const position = visitor.findKeywordPosition(mockNode, testCase.keyword);
      
      if (position !== null) {
        const line = demoCode.slice(0, position).split('\n').length;
        const lineStart = demoCode.lastIndexOf('\n', position - 1) + 1;
        const column = position - lineStart + 1;
        
        console.log(chalk.green(`  ✅ 找到位置: 字节偏移 ${position} (第${line}行，第${column}列)`));
        
        // 显示找到的上下文
        const contextStart = Math.max(0, position - 20);
        const contextEnd = Math.min(demoCode.length, position + 20);
        const context = demoCode.slice(contextStart, contextEnd);
        const keywordInContext = context.indexOf(testCase.keyword);
        
        if (keywordInContext >= 0) {
          const before = context.slice(0, keywordInContext);
          const keyword = context.slice(keywordInContext, keywordInContext + testCase.keyword.length);
          const after = context.slice(keywordInContext + testCase.keyword.length);
          
          console.log(chalk.gray(`  上下文: "${before}${chalk.yellow.bold(keyword)}${after}"`));
        }
      } else {
        console.log(chalk.red(`  ❌ 未找到 '${testCase.keyword}' 关键字`));
      }
    }
    
    // 性能统计
    console.log('\n' + '='.repeat(80));
    console.log(chalk.blue.bold('📈 性能统计:'));
    console.log(chalk.cyan(`• 分析时间: ${endTime - startTime}ms`));
    console.log(chalk.cyan(`• 源代码长度: ${demoCode.length} 字符`));
    console.log(chalk.cyan(`• 函数数量: ${results.length}`));
    console.log(chalk.cyan(`• 总复杂度: ${results.reduce((sum, r) => sum + r.complexity, 0)}`));
    
    const totalSteps = results.reduce((sum, r) => sum + (r.details?.length || 0), 0);
    console.log(chalk.cyan(`• 详细步骤数: ${totalSteps}`));
    
  } catch (error) {
    console.error(chalk.red.bold('❌ 演示失败:'), error);
    console.error(chalk.red(error.stack));
  }
}

function getRuleIcon(ruleId: string): string {
  const iconMap: Record<string, string> = {
    'if-statement': '🔀',
    'for-statement': '🔄',
    'while-statement': '♻️',
    'switch-statement': '🎯',
    'try-statement': '🛡️',
    'catch-clause': '⚠️',
    'conditional-expression': '❓',
    'logical-operator': '🔗',
    'recursive-call': '🔁',
    'logical-operator-mixing': '⚡'
  };
  
  return iconMap[ruleId] || '📋';
}

// 运行演示
console.log(chalk.green('启动Token查找系统演示...\n'));
demonstrateTokenSearch().catch(console.error);