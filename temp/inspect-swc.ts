#!/usr/bin/env bun

async function inspectSwcCore() {
  console.log('Inspecting @swc/core exports...');
  
  try {
    const swcCore = await import('@swc/core');
    console.log('Available exports:', Object.keys(swcCore));
    
    // 检查是否有parse相关的函数
    if (swcCore.parseSync) {
      console.log('\n✓ Found parseSync - we can use this for token analysis');
    }
    
    if (swcCore.parse) {
      console.log('✓ Found parse (async version)');
    }
    
    // 测试parse功能
    const sourceCode = `function test() { if (true) return 1; }`;
    
    try {
      const ast = await swcCore.parseSync(sourceCode, {
        syntax: 'typescript',
        jsx: false,
      });
      
      console.log('\n✓ Parse successful!');
      console.log('AST type:', ast.type);
      console.log('AST body length:', ast.body?.length);
      
      // 展示AST结构
      if (ast.body && ast.body.length > 0) {
        const firstNode = ast.body[0];
        console.log('First node type:', firstNode.type);
        console.log('First node span:', firstNode.span);
      }
      
    } catch (parseError) {
      console.error('Parse failed:', parseError);
    }
    
  } catch (error) {
    console.error('Failed to import @swc/core:', error);
  }
}

inspectSwcCore().catch(console.error);