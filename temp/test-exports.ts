/**
 * 测试模块导出是否正常
 */

import { formatSpanDebugInfo, extractSpanSourceCode, sanitizeCodeSnippet } from '../src/utils';

console.log('✅ 成功从 utils/index.ts 导入所有 span-debug-info 函数');
console.log('📦 导出的函数:');
console.log('  - formatSpanDebugInfo:', typeof formatSpanDebugInfo);
console.log('  - extractSpanSourceCode:', typeof extractSpanSourceCode);
console.log('  - sanitizeCodeSnippet:', typeof sanitizeCodeSnippet);

// 简单功能测试
const result = extractSpanSourceCode('hello world', { start: 0, end: 5 });
console.log('🧪 简单测试结果:', result);

console.log('\n✅ 模块导出测试完成');