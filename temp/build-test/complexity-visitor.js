// src/core/base-visitor.ts
class BaseVisitor {
  parentStack = [];
  getParent() {
    return this.parentStack.length > 1 ? this.parentStack[this.parentStack.length - 2] : undefined;
  }
  getGrandParent() {
    return this.parentStack.length >= 3 ? this.parentStack[this.parentStack.length - 3] : undefined;
  }
  getParentPath() {
    return this.parentStack.length > 0 ? [...this.parentStack.slice(0, -1)] : [];
  }
  hasAncestorOfType(nodeType) {
    return this.parentStack.slice(0, -1).some((parent) => parent.type === nodeType);
  }
  findNearestAncestorOfType(nodeType) {
    for (let i = this.parentStack.length - 2;i >= 0; i--) {
      const node = this.parentStack[i];
      if (node && node.type === nodeType) {
        return node;
      }
    }
    return;
  }
  visit(node) {
    this.parentStack.push(node);
    try {
      const result = this.visitNode(node);
      this.visitChildren(node);
      return result || node;
    } finally {
      this.parentStack.pop();
    }
  }
  visitChildren(node) {
    if (!node || typeof node !== "object") {
      return;
    }
    for (const key in node) {
      if (!node.hasOwnProperty(key)) {
        continue;
      }
      const value = node[key];
      if (this.isNode(value)) {
        this.visit(value);
      } else if (Array.isArray(value)) {
        for (const item of value) {
          if (this.isNode(item)) {
            this.visit(item);
          }
        }
      } else if (this.shouldVisitProperty(key, value)) {
        this.visitChildren(value);
      }
    }
  }
  isNode(obj) {
    return obj && typeof obj === "object" && typeof obj.type === "string" && obj.span && typeof obj.span === "object";
  }
  shouldVisitProperty(key, value) {
    const excludedProperties = new Set([
      "span",
      "type",
      "ctxt",
      "value",
      "raw",
      "optional",
      "start",
      "end",
      "loc",
      "range"
    ]);
    return !excludedProperties.has(key) && value && typeof value === "object";
  }
  reset() {
    this.parentStack = [];
  }
  getDepth() {
    return this.parentStack.length > 0 ? this.parentStack.length - 1 : 0;
  }
}

// src/utils/position-converter.ts
class PositionConverter {
  static lineMapCache = new Map;
  static lineAnalysisCache = new Map;
  static maxCacheSize = 100;
  static cacheCleanupThreshold = 150;
  static spanToPosition(sourceCode, spanStart) {
    const lineMap = this.getCachedLineMap(sourceCode);
    if (spanStart < 0 || spanStart > sourceCode.length) {
      return { line: 1, column: 1 };
    }
    const lineIndex = this.binarySearchLineIndex(lineMap, spanStart);
    const lineStartOffset = lineMap[lineIndex];
    const column = spanStart - lineStartOffset + 1;
    return {
      line: lineIndex + 1,
      column: Math.max(1, column)
    };
  }
  static spanToPositionWithSmartFallback(sourceCode, spanStart, spanEnd, filePath) {
    const basePosition = this.spanToPosition(sourceCode, spanStart);
    if (!this.isInsignificantLine(sourceCode, basePosition.line, filePath)) {
      return basePosition;
    }
    if (spanEnd !== undefined && spanEnd > spanStart) {
      const endPosition = this.spanToPosition(sourceCode, spanEnd);
      if (endPosition.line > basePosition.line) {
        const meaningfulLine = this.findMostMeaningfulLineInRange(sourceCode, basePosition.line, endPosition.line, filePath);
        if (meaningfulLine !== basePosition.line) {
          return {
            line: meaningfulLine,
            column: 1
          };
        }
      }
      if (!this.isInsignificantLine(sourceCode, endPosition.line, filePath)) {
        return this.findLastMeaningfulLine(sourceCode, endPosition.line, 3, filePath) === endPosition.line ? endPosition : { line: this.findLastMeaningfulLine(sourceCode, endPosition.line, 3, filePath), column: 1 };
      }
    }
    const nearestMeaningfulLine = this.findNearestMeaningfulLine(sourceCode, basePosition.line, 5, filePath);
    if (nearestMeaningfulLine !== basePosition.line) {
      return {
        line: nearestMeaningfulLine,
        column: basePosition.column
      };
    }
    if (basePosition.line <= 3) {
      const lineMap2 = this.getCachedLineMap(sourceCode);
      for (let line = basePosition.line + 1;line <= Math.min(lineMap2.length, basePosition.line + 10); line++) {
        if (!this.isInsignificantLine(sourceCode, line, filePath)) {
          return { line, column: 1 };
        }
      }
    }
    const lineMap = this.getCachedLineMap(sourceCode);
    if (basePosition.line >= lineMap.length - 3) {
      const lastMeaningfulLine = this.findLastMeaningfulLine(sourceCode, basePosition.line, 15, filePath);
      if (lastMeaningfulLine !== basePosition.line) {
        return { line: lastMeaningfulLine, column: 1 };
      }
    }
    return basePosition;
  }
  static lineColumnToOffset(sourceCode, line, column) {
    const lineMap = this.getCachedLineMap(sourceCode);
    const lineIndex = line - 1;
    const columnIndex = column - 1;
    if (lineIndex < 0 || lineIndex >= lineMap.length) {
      throw new Error(`Line ${line} is out of range. File has ${lineMap.length} lines.`);
    }
    const lineStartOffset = lineMap[lineIndex];
    const lineEndOffset = lineIndex + 1 < lineMap.length ? lineMap[lineIndex + 1] - 1 : sourceCode.length;
    const lineLength = lineEndOffset - lineStartOffset;
    if (columnIndex < 0 || columnIndex > lineLength) {
      throw new Error(`Column ${column} is out of range. Line ${line} has ${lineLength} characters.`);
    }
    return lineStartOffset + columnIndex;
  }
  static getLineContent(sourceCode, line) {
    const lineMap = this.getCachedLineMap(sourceCode);
    const lineIndex = line - 1;
    if (lineIndex < 0 || lineIndex >= lineMap.length) {
      throw new Error(`Line ${line} is out of range. File has ${lineMap.length} lines.`);
    }
    const lineStartOffset = lineMap[lineIndex];
    const lineEndOffset = lineIndex + 1 < lineMap.length ? lineMap[lineIndex + 1] - 1 : sourceCode.length;
    return sourceCode.slice(lineStartOffset, lineEndOffset);
  }
  static extractSpanText(sourceCode, span) {
    if (span.start < 0 || span.end > sourceCode.length || span.start > span.end) {
      throw new Error(`Invalid span: start=${span.start}, end=${span.end}, sourceLength=${sourceCode.length}`);
    }
    return sourceCode.slice(span.start, span.end);
  }
  static isValidPosition(sourceCode, position) {
    try {
      this.lineColumnToOffset(sourceCode, position.line, position.column);
      return true;
    } catch {
      return false;
    }
  }
  static getCachedLineMap(sourceCode, filePath) {
    const cacheKey = this.generateCacheKey(sourceCode, filePath);
    const cached = this.lineMapCache.get(cacheKey);
    if (cached) {
      cached.accessCount++;
      cached.timestamp = Date.now();
      return cached.lineMap;
    }
    const lineMap = this.buildLineMap(sourceCode);
    this.manageCacheSize();
    this.lineMapCache.set(cacheKey, {
      lineMap,
      timestamp: Date.now(),
      accessCount: 1
    });
    return lineMap;
  }
  static generateCacheKey(sourceCode, filePath) {
    let contentKey;
    if (sourceCode.length <= 1e4) {
      const start = sourceCode.substring(0, 100);
      const end = sourceCode.substring(Math.max(0, sourceCode.length - 100));
      contentKey = `${sourceCode.length}-${start}-${end}`;
    } else {
      const middle = sourceCode.substring(Math.floor(sourceCode.length / 2) - 50, Math.floor(sourceCode.length / 2) + 50);
      const start = sourceCode.substring(0, 100);
      const end = sourceCode.substring(Math.max(0, sourceCode.length - 100));
      contentKey = `${sourceCode.length}-${start}-${middle}-${end}`;
    }
    return filePath ? `${filePath}:${contentKey}` : contentKey;
  }
  static manageCacheSize() {
    if (this.lineMapCache.size <= this.maxCacheSize) {
      return;
    }
    if (this.lineMapCache.size >= this.cacheCleanupThreshold) {
      this.performCacheCleanup();
    }
  }
  static performCacheCleanup() {
    const entries = Array.from(this.lineMapCache.entries());
    entries.sort((a, b) => {
      const scoreA = a[1].accessCount * 0.7 + (Date.now() - a[1].timestamp) * -0.3;
      const scoreB = b[1].accessCount * 0.7 + (Date.now() - b[1].timestamp) * -0.3;
      return scoreA - scoreB;
    });
    const removeCount = Math.floor(this.lineMapCache.size * 0.25);
    const toRemove = entries.slice(0, removeCount);
    for (const [key] of toRemove) {
      this.lineMapCache.delete(key);
    }
  }
  static binarySearchLineIndex(lineMap, offset) {
    let left = 0;
    let right = lineMap.length - 1;
    while (left <= right) {
      const mid = Math.floor((left + right) / 2);
      const midOffset = lineMap[mid];
      const nextOffset = lineMap[mid + 1];
      if (offset >= midOffset && (nextOffset === undefined || offset < nextOffset)) {
        return mid;
      } else if (offset < midOffset) {
        right = mid - 1;
      } else {
        left = mid + 1;
      }
    }
    for (let i = 0;i < lineMap.length - 1; i++) {
      if (offset >= lineMap[i] && offset < lineMap[i + 1]) {
        return i;
      }
    }
    return Math.max(0, lineMap.length - 1);
  }
  static buildLineMap(sourceCode) {
    const lineMap = [];
    lineMap.push(0);
    if (sourceCode.length > 50000) {
      const chunkSize = 1e4;
      for (let start = 0;start < sourceCode.length; start += chunkSize) {
        const end = Math.min(start + chunkSize, sourceCode.length);
        const chunk = sourceCode.slice(start, end);
        for (let i = 0;i < chunk.length; i++) {
          if (chunk[i] === `
`) {
            lineMap.push(start + i + 1);
          }
        }
      }
    } else {
      for (let i = 0;i < sourceCode.length; i++) {
        if (sourceCode[i] === `
`) {
          lineMap.push(i + 1);
        }
      }
    }
    return lineMap;
  }
  static clearCache() {
    this.lineMapCache.clear();
    this.lineAnalysisCache.clear();
  }
  static getCacheStats() {
    let totalAccess = 0;
    let memoryEstimate = 0;
    const entries = Array.from(this.lineMapCache.values());
    for (const entry of entries) {
      totalAccess += entry.accessCount;
      memoryEstimate += entry.lineMap.length * 8;
    }
    const analysisEntries = Array.from(this.lineAnalysisCache.values());
    for (const entry of analysisEntries) {
      memoryEstimate += entry.insignificantLines.size * 8;
    }
    return {
      size: this.lineMapCache.size,
      maxSize: this.maxCacheSize,
      hitRate: totalAccess > 0 ? (totalAccess - this.lineMapCache.size) / totalAccess : 0,
      memoryEstimate,
      lineAnalysisCacheSize: this.lineAnalysisCache.size
    };
  }
  static isInsignificantLine(sourceCode, line, filePath) {
    try {
      const lineContent = this.getLineContent(sourceCode, line).trim();
      if (!lineContent) {
        return true;
      }
      const insignificantPatterns = [
        /^\/\/.*$/,
        /^\/\*.*\*\/$/,
        /^\/\*.*$/,
        /^.*\*\/$/,
        /^\*.*$/,
        /^import\s/,
        /^export\s.*from\s/,
        /^export\s*\{[^}]*\}\s*;?\s*$/,
        /^type\s+\w+\s*=\s*[^{;]+;?\s*$/,
        /^interface\s+\w+\s*\{?\s*$/,
        /^\s*\}\s*$/,
        /^\s*\{\s*$/,
        /^\s*\[\s*$/,
        /^\s*\]\s*[,;]?\s*$/,
        /^import.*from\s+['"].*['"];?\s*$/,
        /^export\s*\*\s*from\s+['"].*['"];?\s*$/,
        /^@\w+(\([^)]*\))?\s*$/,
        /^const\s+\w+\s*:\s*[^=]+;\s*$/,
        /^let\s+\w+\s*:\s*[^=]+;\s*$/,
        /^export\s+default\s+\w+;\s*$/
      ];
      return insignificantPatterns.some((pattern) => pattern.test(lineContent));
    } catch {
      return true;
    }
  }
  static findNearestMeaningfulLine(sourceCode, targetLine, maxDistance = 5, filePath) {
    if (!this.isInsignificantLine(sourceCode, targetLine, filePath)) {
      return targetLine;
    }
    const lineMap = this.getCachedLineMap(sourceCode, filePath);
    const totalLines = lineMap.length;
    for (let distance = 1;distance <= maxDistance; distance++) {
      const upperLine = targetLine - distance;
      if (upperLine >= 1 && !this.isInsignificantLine(sourceCode, upperLine, filePath)) {
        return upperLine;
      }
      const lowerLine = targetLine + distance;
      if (lowerLine <= totalLines && !this.isInsignificantLine(sourceCode, lowerLine, filePath)) {
        return lowerLine;
      }
    }
    return targetLine;
  }
  static findLastMeaningfulLine(sourceCode, fromLine, maxLookback = 10, filePath) {
    const lineMap = this.getCachedLineMap(sourceCode, filePath);
    const actualFromLine = Math.min(fromLine, lineMap.length);
    for (let line = actualFromLine;line >= Math.max(1, actualFromLine - maxLookback); line--) {
      if (!this.isInsignificantLine(sourceCode, line, filePath)) {
        return line;
      }
    }
    const extendedLookback = Math.min(maxLookback * 2, actualFromLine - 1);
    for (let line = actualFromLine - maxLookback - 1;line >= Math.max(1, actualFromLine - extendedLookback); line--) {
      if (!this.isInsignificantLine(sourceCode, line, filePath)) {
        return line;
      }
    }
    return Math.max(1, actualFromLine);
  }
  static findMostMeaningfulLineInRange(sourceCode, startLine, endLine, filePath) {
    const meaningfulLines = [];
    for (let line = startLine;line <= endLine; line++) {
      if (!this.isInsignificantLine(sourceCode, line, filePath)) {
        const score = this.calculateLineMeaningfulnessScore(sourceCode, line);
        meaningfulLines.push({ line, score });
      }
    }
    if (meaningfulLines.length === 0) {
      return startLine;
    }
    meaningfulLines.sort((a, b) => b.score - a.score);
    return meaningfulLines[0].line;
  }
  static calculateLineMeaningfulnessScore(sourceCode, line) {
    try {
      const lineContent = this.getLineContent(sourceCode, line).trim();
      let score = 1;
      const highValuePatterns = [
        { pattern: /function\s+\w+/, score: 10 },
        { pattern: /class\s+\w+/, score: 10 },
        { pattern: /if\s*\(/, score: 8 },
        { pattern: /for\s*\(/, score: 8 },
        { pattern: /while\s*\(/, score: 8 },
        { pattern: /switch\s*\(/, score: 8 },
        { pattern: /return\s+/, score: 7 },
        { pattern: /throw\s+/, score: 7 },
        { pattern: /catch\s*\(/, score: 7 },
        { pattern: /const\s+\w+\s*=/, score: 5 },
        { pattern: /let\s+\w+\s*=/, score: 5 },
        { pattern: /var\s+\w+\s*=/, score: 4 },
        { pattern: /\w+\s*\(.*\)/, score: 6 },
        { pattern: /\w+\.\w+/, score: 4 },
        { pattern: /[=!<>]=/, score: 3 },
        { pattern: /[+\-*/]=/, score: 3 }
      ];
      const modernPatterns = [
        { pattern: /async\s+function/, score: 9 },
        { pattern: /await\s+/, score: 8 },
        { pattern: /=>\s*/, score: 6 },
        { pattern: /const\s+\{.*\}\s*=/, score: 5 },
        { pattern: /\.\.\./, score: 4 },
        { pattern: /`.*\$\{.*\}.*`/, score: 4 }
      ];
      const jsxPatterns = [
        { pattern: /<\w+[^>]*>/, score: 6 },
        { pattern: /<\/\w+>/, score: 4 },
        { pattern: /\{.*\}/, score: 3 }
      ];
      const allPatterns = [...highValuePatterns, ...modernPatterns, ...jsxPatterns];
      for (const { pattern, score: patternScore } of allPatterns) {
        if (pattern.test(lineContent)) {
          score += patternScore;
        }
      }
      score += Math.min(lineContent.length / 20, 3);
      const operators = lineContent.match(/[+\-*/=<>!&|?:]/g);
      if (operators) {
        score += Math.min(operators.length * 0.5, 2);
      }
      return score;
    } catch {
      return 0;
    }
  }
  static getCachedLineAnalysis(sourceCode, filePath) {
    const cacheKey = this.generateLineAnalysisCacheKey(sourceCode, filePath);
    const cached = this.lineAnalysisCache.get(cacheKey);
    if (cached) {
      cached.accessCount++;
      cached.timestamp = Date.now();
      return cached.insignificantLines;
    }
    const insignificantLines = new Set;
    const lineMap = this.getCachedLineMap(sourceCode, filePath);
    for (let line = 1;line <= lineMap.length; line++) {
      if (this.isInsignificantLine(sourceCode, line, filePath)) {
        insignificantLines.add(line);
      }
    }
    this.manageLineAnalysisCacheSize();
    this.lineAnalysisCache.set(cacheKey, {
      insignificantLines,
      timestamp: Date.now(),
      accessCount: 1
    });
    return insignificantLines;
  }
  static generateLineAnalysisCacheKey(sourceCode, filePath) {
    const contentKey = this.generateCacheKey(sourceCode);
    return filePath ? `${filePath}:${contentKey}` : contentKey;
  }
  static manageLineAnalysisCacheSize() {
    if (this.lineAnalysisCache.size <= this.maxCacheSize) {
      return;
    }
    if (this.lineAnalysisCache.size >= this.cacheCleanupThreshold) {
      this.performLineAnalysisCacheCleanup();
    }
  }
  static performLineAnalysisCacheCleanup() {
    const entries = Array.from(this.lineAnalysisCache.entries());
    entries.sort((a, b) => {
      const scoreA = a[1].accessCount * 0.7 + (Date.now() - a[1].timestamp) * -0.3;
      const scoreB = b[1].accessCount * 0.7 + (Date.now() - b[1].timestamp) * -0.3;
      return scoreA - scoreB;
    });
    const removeCount = Math.floor(this.lineAnalysisCache.size * 0.25);
    const toRemove = entries.slice(0, removeCount);
    for (const [key] of toRemove) {
      this.lineAnalysisCache.delete(key);
    }
  }
}

// src/core/types.ts
var RuleCategory;
((RuleCategory2) => {
  RuleCategory2["CONTROL_FLOW"] = "control-flow";
  RuleCategory2["LOGICAL_OPERATOR"] = "logical-operator";
  RuleCategory2["FUNCTION_CALL"] = "function-call";
  RuleCategory2["EXCEPTION_HANDLING"] = "exception-handling";
  RuleCategory2["RECURSION"] = "recursion";
  RuleCategory2["NESTING"] = "nesting";
  RuleCategory2["JSX"] = "jsx";
  RuleCategory2["OTHER"] = "other";
})(RuleCategory ||= {});

// src/core/rule-registry.ts
function createMetadataFromRule(rule, category) {
  const inferredCategory = category || inferCategoryFromRuleId(rule.id);
  return {
    ruleId: rule.id,
    description: rule.name,
    category: inferredCategory,
    defaultIncrement: 1,
    enabled: true,
    priority: rule.priority
  };
}
function inferCategoryFromRuleId(ruleId) {
  if (ruleId.includes("if") || ruleId.includes("for") || ruleId.includes("while") || ruleId.includes("switch")) {
    return "control-flow" /* CONTROL_FLOW */;
  }
  if (ruleId.includes("logical")) {
    return "logical-operator" /* LOGICAL_OPERATOR */;
  }
  if (ruleId.includes("recursive") || ruleId.includes("recursion")) {
    return "recursion" /* RECURSION */;
  }
  if (ruleId.includes("catch") || ruleId.includes("exception")) {
    return "exception-handling" /* EXCEPTION_HANDLING */;
  }
  if (ruleId.includes("jsx")) {
    return "jsx" /* JSX */;
  }
  if (ruleId.includes("nesting")) {
    return "nesting" /* NESTING */;
  }
  return "other" /* OTHER */;
}

class RuleRegistry {
  static rules = new Map;
  static RULE_ID_PATTERN = /^[a-z][a-z0-9]*(-[a-z0-9]+)*$/;
  static register(ruleId, description, category = "other" /* OTHER */, defaultIncrement = 1) {
    if (!this.validateRuleId(ruleId)) {
      throw new Error(`Invalid rule ID format: "${ruleId}". Rule IDs must follow kebab-case convention (e.g., "if-statement", "logical-and").`);
    }
    if (this.rules.has(ruleId)) {
      throw new Error(`Rule ID "${ruleId}" is already registered. Use updateRule() to modify existing rules.`);
    }
    if (!description || description.trim().length === 0) {
      throw new Error(`Rule description cannot be empty for rule ID: "${ruleId}"`);
    }
    if (typeof defaultIncrement !== "number" || defaultIncrement < 0) {
      throw new Error(`Default increment must be a non-negative number for rule ID: "${ruleId}"`);
    }
    const ruleMetadata = {
      ruleId,
      description: description.trim(),
      category,
      defaultIncrement,
      enabled: true,
      priority: 0
    };
    this.rules.set(ruleId, ruleMetadata);
  }
  static registerFromRule(rule, category, quiet = false) {
    const metadata = createMetadataFromRule(rule, category);
    try {
      this.registerRule(metadata);
      if (!quiet) {
        console.log(`Rule '${rule.id}' registered successfully`);
      }
    } catch (error) {
      if (!quiet) {
        console.error(`Failed to register rule '${rule.id}':`, error);
      }
      throw error;
    }
  }
  static registerFromRules(rules, quiet = false) {
    for (const rule of rules) {
      try {
        this.registerFromRule(rule, undefined, true);
      } catch (error) {
        if (!quiet) {
          console.error(`Failed to register rule '${rule.id}':`, error);
        }
      }
    }
    if (!quiet) {
      console.log(`Successfully registered ${rules.length} class-based rules`);
    }
  }
  static registerRule(ruleMetadata) {
    this.register(ruleMetadata.ruleId, ruleMetadata.description, ruleMetadata.category, ruleMetadata.defaultIncrement);
    if (ruleMetadata.enabled !== undefined) {
      this.rules.get(ruleMetadata.ruleId).enabled = ruleMetadata.enabled;
    }
    if (ruleMetadata.priority !== undefined) {
      this.rules.get(ruleMetadata.ruleId).priority = ruleMetadata.priority;
    }
  }
  static registerBatch(config) {
    const { rules, overwrite = false } = config;
    for (const ruleMetadata of rules) {
      if (this.rules.has(ruleMetadata.ruleId) && !overwrite) {
        console.warn(`Skipping already registered rule: ${ruleMetadata.ruleId}`);
        continue;
      }
      if (overwrite && this.rules.has(ruleMetadata.ruleId)) {
        this.rules.delete(ruleMetadata.ruleId);
      }
      this.registerRule(ruleMetadata);
    }
  }
  static getDescription(ruleId) {
    const rule = this.rules.get(ruleId);
    return rule ? rule.description : null;
  }
  static getRule(ruleId) {
    return this.rules.get(ruleId) || null;
  }
  static getAllRules() {
    return Array.from(this.rules.values());
  }
  static getRulesByCategory(category) {
    return Array.from(this.rules.values()).filter((rule) => rule.category === category);
  }
  static hasRule(ruleId) {
    return this.rules.has(ruleId);
  }
  static getAllRuleIds() {
    return Array.from(this.rules.keys());
  }
  static updateRule(ruleId, updates) {
    const existingRule = this.rules.get(ruleId);
    if (!existingRule) {
      throw new Error(`Cannot update non-existent rule: "${ruleId}"`);
    }
    const updatedRule = {
      ...existingRule,
      ...updates
    };
    this.rules.set(ruleId, updatedRule);
  }
  static unregister(ruleId) {
    return this.rules.delete(ruleId);
  }
  static clear() {
    this.rules.clear();
  }
  static validateRuleId(ruleId) {
    return typeof ruleId === "string" && this.RULE_ID_PATTERN.test(ruleId);
  }
  static getStatistics() {
    const rules = Array.from(this.rules.values());
    const byCategory = {};
    Object.values(RuleCategory).forEach((category) => {
      byCategory[category] = 0;
    });
    let enabled = 0;
    let disabled = 0;
    rules.forEach((rule) => {
      byCategory[rule.category]++;
      if (rule.enabled !== false) {
        enabled++;
      } else {
        disabled++;
      }
    });
    return {
      total: rules.length,
      byCategory,
      enabled,
      disabled
    };
  }
  static exportConfig() {
    return {
      rules: this.getAllRules(),
      version: "1.0.0",
      description: "Exported rule configuration",
      overwrite: false
    };
  }
  static validateConfig(config) {
    const errors = [];
    if (!config.rules || !Array.isArray(config.rules)) {
      errors.push("Config must contain a rules array");
      return { isValid: false, errors };
    }
    if (!config.version || typeof config.version !== "string") {
      errors.push("Config must contain a version string");
    }
    for (let i = 0;i < config.rules.length; i++) {
      const rule = config.rules[i];
      if (!this.validateRuleId(rule.ruleId)) {
        errors.push(`Rule at index ${i} has invalid ID format: ${rule.ruleId}`);
      }
      if (!rule.description || typeof rule.description !== "string") {
        errors.push(`Rule at index ${i} (${rule.ruleId}) has invalid description`);
      }
      if (typeof rule.defaultIncrement !== "number" || rule.defaultIncrement < 0) {
        errors.push(`Rule at index ${i} (${rule.ruleId}) has invalid defaultIncrement`);
      }
    }
    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

// src/core/complexity-visitor.ts
RuleRegistry.register("if-statement", "if 语句", "control-flow" /* CONTROL_FLOW */, 1);
RuleRegistry.register("while-statement", "while 循环", "control-flow" /* CONTROL_FLOW */, 1);
RuleRegistry.register("do-while-statement", "do-while 循环", "control-flow" /* CONTROL_FLOW */, 1);
RuleRegistry.register("for-statement", "for 循环", "control-flow" /* CONTROL_FLOW */, 1);
RuleRegistry.register("switch-statement", "switch 语句", "control-flow" /* CONTROL_FLOW */, 1);
RuleRegistry.register("try-statement", "try 语句", "exception-handling" /* EXCEPTION_HANDLING */, 0);
RuleRegistry.register("catch-clause", "catch 异常处理", "exception-handling" /* EXCEPTION_HANDLING */, 1);
RuleRegistry.register("conditional-expression", "三元运算符", "control-flow" /* CONTROL_FLOW */, 1);
RuleRegistry.register("logical-operator", "逻辑运算符", "logical-operator" /* LOGICAL_OPERATOR */, 1);
RuleRegistry.register("recursive-call", "递归调用", "recursion" /* RECURSION */, 1);
RuleRegistry.register("logical-operator-mixing", "逻辑运算符混用惩罚", "logical-operator" /* LOGICAL_OPERATOR */, 1);

class ComplexityVisitor extends BaseVisitor {
  static NODE_POSITION_STRATEGIES = new Map([
    ["IfStatement", {
      nodeType: "IfStatement",
      strategy: ComplexityVisitor.createIfStatementStrategy(),
      priority: 1,
      fallbackStrategy: ComplexityVisitor.createControlFlowFallbackStrategy("if")
    }],
    ["WhileStatement", {
      nodeType: "WhileStatement",
      strategy: ComplexityVisitor.createWhileStatementStrategy(),
      priority: 1,
      fallbackStrategy: ComplexityVisitor.createControlFlowFallbackStrategy("while")
    }],
    ["ForStatement", {
      nodeType: "ForStatement",
      strategy: ComplexityVisitor.createForStatementStrategy(),
      priority: 1,
      fallbackStrategy: ComplexityVisitor.createControlFlowFallbackStrategy("for")
    }]
  ]);
  static registerPositionStrategy(entry) {
    ComplexityVisitor.NODE_POSITION_STRATEGIES.set(entry.nodeType, entry);
  }
  static getPositionStrategy(nodeType) {
    return ComplexityVisitor.NODE_POSITION_STRATEGIES.get(nodeType);
  }
  static createIfStatementStrategy() {
    return (node) => {
      const ifPosition = ComplexityVisitor.findKeywordInNode(node, "if");
      if (ifPosition !== null) {
        return ifPosition;
      }
      const nodeWithSpan = node;
      if (nodeWithSpan.span && typeof nodeWithSpan.span.start === "number") {
        return nodeWithSpan.span.start;
      }
      return null;
    };
  }
  static createWhileStatementStrategy() {
    return (node) => {
      const whilePosition = ComplexityVisitor.findKeywordInNode(node, "while");
      if (whilePosition !== null) {
        return whilePosition;
      }
      const nodeWithSpan = node;
      if (nodeWithSpan.span && typeof nodeWithSpan.span.start === "number") {
        return nodeWithSpan.span.start;
      }
      return null;
    };
  }
  static createForStatementStrategy() {
    return (node) => {
      const forPosition = ComplexityVisitor.findKeywordInNode(node, "for");
      if (forPosition !== null) {
        return forPosition;
      }
      const nodeWithSpan = node;
      if (nodeWithSpan.span && typeof nodeWithSpan.span.start === "number") {
        return nodeWithSpan.span.start;
      }
      return null;
    };
  }
  static createControlFlowFallbackStrategy(_keyword) {
    return (_node) => {
      return null;
    };
  }
  static findKeywordInNode(_node, _keyword) {
    return null;
  }
  sourceCode;
  detailCollector;
  options;
  asyncRuleEngine;
  results = [];
  totalComplexity = 0;
  nestingLevel = 0;
  currentFunctionName = "";
  currentFunctionLocation = { line: 0, column: 0 };
  processedMixingNodes = new Set;
  constructor(sourceCode = "", detailCollector, options = {}, asyncRuleEngine) {
    super();
    this.sourceCode = sourceCode;
    this.detailCollector = detailCollector;
    this.options = options;
    this.asyncRuleEngine = asyncRuleEngine;
  }
  getResults() {
    return [...this.results];
  }
  visitFunction(node) {
    try {
      this.resetForNewFunction();
      this.currentFunctionName = this.extractFunctionName(node);
      this.currentFunctionLocation = this.getNodeLocation(node);
      if (this.detailCollector) {
        this.detailCollector.startFunction(this.currentFunctionName, this.currentFunctionLocation.line, this.currentFunctionLocation.column);
      }
      const functionBody = this.getFunctionBody(node);
      if (functionBody) {
        this.visit(functionBody);
      }
      let functionDetail = null;
      if (this.detailCollector) {
        functionDetail = this.detailCollector.endFunction();
      }
      const result = {
        name: this.currentFunctionName,
        complexity: this.totalComplexity,
        line: this.currentFunctionLocation.line,
        column: this.currentFunctionLocation.column,
        filePath: "",
        details: functionDetail?.details
      };
      this.results.push(result);
    } catch (error) {
      this.handleFunctionAnalysisError(node, error);
    }
  }
  resetForNewFunction() {
    this.totalComplexity = 0;
    this.nestingLevel = 0;
    this.processedMixingNodes.clear();
    this.currentFunctionName = "";
    this.currentFunctionLocation = { line: 0, column: 0 };
    this.reset();
  }
  extractFunctionName(node) {
    if (node.type === "FunctionDeclaration" && node.identifier) {
      return node.identifier.value || node.identifier.name || "<anonymous>";
    }
    if (node.type === "MethodDefinition" && node.key) {
      return node.key.value || node.key.name || "<method>";
    }
    if (node.type === "ClassMethod" && node.key) {
      return node.key.value || node.key.name || "<method>";
    }
    if (node.type === "VariableDeclarator" && node.id) {
      return node.id.value || node.id.name || "<anonymous>";
    }
    if (node.type === "Property" && node.key) {
      return node.key.value || node.key.name || "<property>";
    }
    return "<anonymous>";
  }
  getFunctionBody(node) {
    if (node.type === "FunctionDeclaration" && node.body) {
      return node.body;
    }
    if (node.type === "ArrowFunctionExpression") {
      return node.body;
    }
    if (node.type === "FunctionExpression" && node.body) {
      return node.body;
    }
    if (node.type === "MethodDefinition" && node.value && node.value.body) {
      return node.value.body;
    }
    if (node.type === "ClassMethod" && node.function && node.function.body) {
      return node.function.body;
    }
    if (node.type === "VariableDeclarator" && node.init) {
      return this.getFunctionBody(node.init);
    }
    if (node.type === "Property" && node.value) {
      return this.getFunctionBody(node.value);
    }
    return null;
  }
  getNodeLocation(node) {
    try {
      if (this.isValidSpan(node)) {
        const position = PositionConverter.spanToPosition(this.sourceCode, node.span.start);
        if (position.line >= 1 && position.column >= 0) {
          return { line: position.line, column: position.column };
        }
      }
      if (node.loc) {
        const loc = node.loc;
        if (loc.start && typeof loc.start.line === "number" && typeof loc.start.column === "number") {
          if (loc.start.line >= 1 && loc.start.column >= 0) {
            return { line: loc.start.line, column: loc.start.column };
          }
        }
      }
      if (typeof node.line === "number" && typeof node.column === "number") {
        if (node.line >= 1 && node.column >= 0) {
          return { line: node.line, column: node.column };
        }
      }
      if (node.type === "FunctionDeclaration" || node.type === "FunctionExpression" || node.type === "ArrowFunctionExpression" || node.type === "MethodDefinition") {
        const identifier = node.key || node.id;
        if (identifier && this.isValidSpan(identifier)) {
          const position = PositionConverter.spanToPosition(this.sourceCode, identifier.span.start);
          if (position.line >= 1 && position.column >= 0) {
            return { line: position.line, column: position.column };
          }
        }
      }
    } catch (error) {
      if (this.options?.enableDebugLog) {
        console.debug("Position extraction failed:", {
          nodeType: node.type,
          hasSpan: !!node.span,
          hasLoc: !!node.loc,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }
    if (this.options?.enableDebugLog) {
      console.warn(`Unable to extract valid position for node type: ${node.type}, falling back to (1,0)`);
    }
    return { line: 1, column: 0 };
  }
  handleFunctionAnalysisError(node, error) {
    const functionName = this.extractFunctionName(node);
    const location = this.getNodeLocation(node);
    console.warn(`Error analyzing function ${functionName}:`, error.message);
    const errorResult = {
      name: functionName,
      complexity: 0,
      line: location.line,
      column: location.column,
      filePath: "",
      severity: "Critical"
    };
    this.results.push(errorResult);
    if (this.detailCollector) {
      try {
        this.detailCollector.addErrorStep({
          line: location.line,
          column: location.column,
          increment: 0,
          ruleId: "function-analysis-error",
          description: `函数分析失败: ${functionName}`,
          context: error.message
        }, `函数 ${functionName} 分析过程中发生错误: ${error.message}`);
      } catch (detailError) {
        console.error(`Failed to record function analysis error for ${functionName}:`, detailError);
      }
    }
  }
  getTotalComplexity() {
    return this.totalComplexity;
  }
  getCurrentNestingLevel() {
    return this.nestingLevel;
  }
  resetComplexity() {
    this.totalComplexity = 0;
    this.nestingLevel = 0;
    this.processedMixingNodes.clear();
    this.reset();
  }
  visit(node) {
    if (this.parentStack.length === 0 && (node.type === "Module" || node.type === "Program")) {
      return this.visitModuleAndAnalyzeFunctions(node);
    }
    this.parentStack.push(node);
    try {
      const shouldEnterNesting = this.shouldEnterNesting(node);
      this.calculateNodeComplexity(node);
      if (shouldEnterNesting) {
        this.enterNesting();
      }
      try {
        this.visitChildren(node);
      } finally {
        if (shouldEnterNesting) {
          this.exitNesting();
        }
      }
      return node;
    } catch (error) {
      this.handleVisitError(node, error);
      return node;
    } finally {
      this.parentStack.pop();
    }
  }
  visitModuleAndAnalyzeFunctions(node) {
    const functions = this.findFunctionNodes(node);
    for (const functionNode of functions) {
      this.visitFunction(functionNode);
    }
    return node;
  }
  findFunctionNodes(node) {
    const functions = [];
    const traverse = (currentNode) => {
      if (!currentNode)
        return;
      if (this.isFunctionNode(currentNode)) {
        functions.push(currentNode);
        return;
      }
      if (currentNode.body && Array.isArray(currentNode.body)) {
        for (const child of currentNode.body) {
          traverse(child);
        }
      } else if (currentNode.body && typeof currentNode.body === "object") {
        traverse(currentNode.body);
      }
      if (currentNode.declarations && Array.isArray(currentNode.declarations)) {
        for (const decl of currentNode.declarations) {
          traverse(decl);
        }
      }
      if (currentNode.init) {
        traverse(currentNode.init);
      }
      if (currentNode.consequent) {
        traverse(currentNode.consequent);
      }
      if (currentNode.alternate) {
        traverse(currentNode.alternate);
      }
    };
    traverse(node);
    return functions;
  }
  isFunctionNode(node) {
    return node && (node.type === "FunctionDeclaration" || node.type === "FunctionExpression" || node.type === "ArrowFunctionExpression" || node.type === "MethodDefinition" || node.type === "ClassMethod");
  }
  shouldEnterNesting(node) {
    switch (node.type) {
      case "IfStatement":
      case "WhileStatement":
      case "DoWhileStatement":
      case "ForStatement":
      case "ForInStatement":
      case "ForOfStatement":
      case "SwitchStatement":
      case "CatchClause":
        return true;
      default:
        return false;
    }
  }
  visitNode(node) {
    return node;
  }
  calculateNodeComplexity(node) {
    const { complexity, ruleId, description } = this.evaluateNodeWithRules(node);
    if (complexity > 0) {
      this.addComplexity(complexity, node, ruleId, description, this.getContextForRule(ruleId));
    }
  }
  evaluateNodeWithRules(node) {
    if (this.asyncRuleEngine) {
      try {
        console.debug("AsyncRuleEngine available but in sync context, using RuleRegistry fallback");
      } catch (asyncError) {
        console.debug("AsyncRuleEngine check failed, using RuleRegistry fallback");
      }
    }
    const ruleId = this.getRuleIdForNodeType(node.type, node);
    const rule = RuleRegistry.getRule(ruleId);
    if (!rule || !rule.enabled) {
      return { complexity: 0, ruleId: "unknown", description: "Unknown rule" };
    }
    if (this.shouldSkipNode(node)) {
      return { complexity: 0, ruleId, description: rule.description };
    }
    const baseComplexity = rule.defaultIncrement;
    const nestingIncrement = this.getNestingIncrement();
    const totalComplexity = baseComplexity + nestingIncrement;
    if (ruleId === "logical-operator") {
      this.checkAndApplyMixingPenaltyOnce(node);
    }
    return {
      complexity: totalComplexity,
      ruleId,
      description: rule.description
    };
  }
  getRuleIdForNodeType(nodeType, node) {
    const ruleMap = {
      IfStatement: "if-statement",
      WhileStatement: "while-statement",
      DoWhileStatement: "do-while-statement",
      ForStatement: "for-statement",
      ForInStatement: "for-statement",
      ForOfStatement: "for-statement",
      SwitchStatement: "switch-statement",
      TryStatement: "try-statement",
      CatchClause: "catch-clause",
      ConditionalExpression: "conditional-expression",
      LogicalExpression: "logical-operator",
      CallExpression: "recursive-call"
    };
    if (nodeType === "BinaryExpression") {
      if (node && this.isLogicalOperator(node)) {
        return "logical-operator";
      }
      return "unknown-rule";
    }
    return ruleMap[nodeType] || "unknown-rule";
  }
  shouldSkipNode(node) {
    if (node.type === "BinaryExpression" || node.type === "LogicalExpression") {
      const logicalNode = node;
      if (!this.isLogicalOperator(logicalNode)) {
        return true;
      }
      if (this.isDefaultValueAssignment(logicalNode)) {
        return true;
      }
    }
    if (node.type === "CallExpression") {
      return !this.isRecursiveCall(node);
    }
    return false;
  }
  getContextForRule(ruleId) {
    const baseContext = `嵌套层级: ${this.nestingLevel}`;
    switch (ruleId) {
      case "logical-operator":
        return baseContext;
      case "recursive-call":
        return "递归函数调用";
      default:
        return `基础分(1) + 嵌套分(${this.getNestingIncrement()})`;
    }
  }
  validateSpan(node) {
    try {
      const strategyResult = this.applyPositionStrategy(node);
      if (strategyResult !== null) {
        this.recordSpanValidation(node, strategyResult, "strategy-mapping", true);
        return strategyResult;
      }
      if (this.isValidSpan(node)) {
        const originalSpan = node.span.start;
        this.recordSpanValidation(node, originalSpan, "original", true);
        return originalSpan;
      }
      const correctedSpan = this.attemptParentSpanFallback(node);
      if (correctedSpan !== null) {
        this.recordSpanValidation(node, correctedSpan, "parent-fallback", true);
        return correctedSpan;
      }
      const defaultSpan = this.getDefaultSpan();
      this.recordSpanValidation(node, defaultSpan, "default-fallback", true);
      return defaultSpan;
    } catch (error) {
      const fallbackSpan = 0;
      this.recordSpanValidation(node, fallbackSpan, "error-fallback", false);
      this.recordSpanError(node, error);
      return fallbackSpan;
    }
  }
  applyPositionStrategy(node) {
    const strategyEntry = ComplexityVisitor.getPositionStrategy(node.type);
    if (!strategyEntry) {
      return null;
    }
    try {
      const primaryResult = strategyEntry.strategy(node);
      if (primaryResult !== null) {
        return primaryResult;
      }
      if (strategyEntry.fallbackStrategy) {
        const fallbackResult = strategyEntry.fallbackStrategy(node);
        if (fallbackResult !== null) {
          return fallbackResult;
        }
      }
      const instanceFallback = this.findKeywordPositionInSource(node);
      if (instanceFallback !== null) {
        return instanceFallback;
      }
    } catch (error) {
      if (this.detailCollector) {
        try {
          this.detailCollector.addErrorStep({
            line: 0,
            column: 0,
            increment: 0,
            ruleId: "strategy-error",
            description: `位置策略执行失败: ${node.type}`,
            context: error instanceof Error ? error.message : String(error)
          }, `节点 ${node.type} 的位置策略执行失败: ${error instanceof Error ? error.message : String(error)}`);
        } catch (detailError) {
          console.warn(`Failed to record strategy error for ${node.type}:`, detailError);
        }
      }
    }
    return null;
  }
  findKeywordPositionInSource(node) {
    const keywordMap = {
      IfStatement: "if",
      WhileStatement: "while",
      DoWhileStatement: "do",
      ForStatement: "for",
      ForInStatement: "for",
      ForOfStatement: "for",
      SwitchStatement: "switch",
      TryStatement: "try",
      CatchClause: "catch",
      ConditionalExpression: "?",
      FunctionDeclaration: "function",
      ArrowFunctionExpression: "=>"
    };
    const keyword = keywordMap[node.type];
    if (!keyword) {
      return null;
    }
    const searchRange = this.getSearchRangeForNode(node);
    if (!searchRange) {
      const index = this.sourceCode.indexOf(keyword);
      return index >= 0 ? index : null;
    }
    const sourceSegment = this.sourceCode.slice(searchRange.start, searchRange.end);
    const relativeIndex = sourceSegment.indexOf(keyword);
    if (relativeIndex >= 0) {
      return searchRange.start + relativeIndex;
    }
    return null;
  }
  getSearchRangeForNode(node) {
    const nodeWithSpan = node;
    if (nodeWithSpan.span && typeof nodeWithSpan.span.start === "number" && typeof nodeWithSpan.span.end === "number") {
      return {
        start: Math.max(0, nodeWithSpan.span.start - 50),
        end: Math.min(this.sourceCode.length, nodeWithSpan.span.end + 10)
      };
    }
    const parent = this.getParent();
    if (parent) {
      const parentWithSpan = parent;
      if (parentWithSpan.span && typeof parentWithSpan.span.start === "number" && typeof parentWithSpan.span.end === "number") {
        return {
          start: parentWithSpan.span.start,
          end: parentWithSpan.span.end
        };
      }
    }
    return null;
  }
  recordSpanValidation(node, span, method, success) {
    if (this.detailCollector) {
      try {
        const position = PositionConverter.spanToPosition(this.sourceCode, span);
        const level = success ? method === "original" ? "DEBUG" : "INFO" : "WARNING";
        this.detailCollector.addStepWithDiagnostic({
          line: position.line,
          column: position.column,
          increment: 0,
          ruleId: "span-validation",
          description: `Span验证: ${node.type}`,
          context: `方法: ${method}, 位置: ${position.line}:${position.column}, 状态: ${success ? "成功" : "失败"}`
        }, level, `节点 ${node.type} 的 span 验证${success ? "成功" : "失败"}，使用 ${method} 方法，位置: ${position.line}:${position.column}`);
      } catch (error) {
        console.warn(`Failed to record span validation for ${node.type}:`, error);
      }
    }
  }
  isValidSpan(node) {
    const nodeWithSpan = node;
    return nodeWithSpan.span && typeof nodeWithSpan.span.start === "number" && typeof nodeWithSpan.span.end === "number" && nodeWithSpan.span.start >= 0 && nodeWithSpan.span.end >= nodeWithSpan.span.start && nodeWithSpan.span.start < this.sourceCode.length;
  }
  attemptParentSpanFallback(node) {
    const parent = this.getParent();
    if (parent && this.isValidSpan(parent)) {
      this.recordSpanFallbackStep(node, parent, "direct-parent");
      return parent.span.start;
    }
    const grandParent = this.getGrandParent();
    if (grandParent && this.isValidSpan(grandParent)) {
      this.recordSpanFallbackStep(node, grandParent, "grandparent");
      return grandParent.span.start;
    }
    const validAncestor = this.findValidAncestorSpan();
    if (validAncestor !== null) {
      this.recordSpanFallbackStep(node, null, "ancestor-chain");
      return validAncestor;
    }
    const inferredSpan = this.inferSpanFromContext(node);
    if (inferredSpan !== null) {
      this.recordSpanFallbackStep(node, null, "type-inference");
      return inferredSpan;
    }
    return null;
  }
  findValidAncestorSpan() {
    const parentPath = this.getParentPath();
    for (let i = parentPath.length - 1;i >= 0; i--) {
      const ancestor = parentPath[i];
      if (ancestor && this.isValidSpan(ancestor)) {
        return ancestor.span.start;
      }
    }
    return null;
  }
  inferSpanFromContext(node) {
    switch (node.type) {
      case "IfStatement":
      case "WhileStatement":
      case "ForStatement":
      case "SwitchStatement": {
        return this.findControlFlowKeywordPosition(node.type);
      }
      case "FunctionDeclaration":
      case "MethodDefinition":
      case "ArrowFunctionExpression": {
        return this.findFunctionKeywordPosition(node.type);
      }
      case "BlockStatement": {
        const controlParent = this.findNearestAncestorOfType("IfStatement") || this.findNearestAncestorOfType("WhileStatement") || this.findNearestAncestorOfType("ForStatement");
        if (controlParent && this.isValidSpan(controlParent)) {
          return controlParent.span.start;
        }
        break;
      }
      default:
        break;
    }
    return null;
  }
  findControlFlowKeywordPosition(nodeType) {
    const keywordMap = {
      IfStatement: "if",
      WhileStatement: "while",
      DoWhileStatement: "do",
      ForStatement: "for",
      ForInStatement: "for",
      ForOfStatement: "for",
      SwitchStatement: "switch",
      TryStatement: "try",
      CatchClause: "catch",
      ConditionalExpression: "?"
    };
    const keyword = keywordMap[nodeType];
    if (!keyword)
      return null;
    const currentNode = this.getParent() || { type: nodeType, span: null };
    return this.findKeywordPosition(currentNode, keyword);
  }
  findFunctionKeywordPosition(nodeType) {
    const currentNode = this.getParent() || { type: nodeType, span: null };
    if (nodeType === "ArrowFunctionExpression") {
      return this.findKeywordPosition(currentNode, "=>");
    }
    return this.findKeywordPosition(currentNode, "function");
  }
  findKeywordPosition(node, keyword) {
    const searchRange = this.getSearchRange(node);
    try {
      const tokenPosition = this.findKeywordByTokenAnalysis(keyword, searchRange);
      if (tokenPosition !== null) {
        this.recordTokenSearchResult(node, keyword, tokenPosition, "token-analysis");
        return tokenPosition;
      }
      const patternPosition = this.findKeywordByPatternMatching(keyword, searchRange);
      if (patternPosition !== null) {
        this.recordTokenSearchResult(node, keyword, patternPosition, "pattern-matching");
        return patternPosition;
      }
      const indexPosition = this.findKeywordByIndexOf(keyword, searchRange);
      if (indexPosition !== null) {
        this.recordTokenSearchResult(node, keyword, indexPosition, "index-fallback");
        return indexPosition;
      }
      this.recordTokenSearchResult(node, keyword, null, "all-failed");
      return null;
    } catch (error) {
      this.recordTokenSearchError(node, keyword, error);
      return null;
    }
  }
  getSearchRange(node) {
    if (this.isValidSpan(node)) {
      const span = node.span;
      const expandedStart = Math.max(0, span.start - 50);
      const expandedEnd = Math.min(this.sourceCode.length, span.end + 10);
      return { start: expandedStart, end: expandedEnd };
    }
    const parent = this.getParent();
    if (parent && this.isValidSpan(parent)) {
      const parentSpan = parent.span;
      return { start: parentSpan.start, end: parentSpan.end };
    }
    return { start: 0, end: this.sourceCode.length };
  }
  findKeywordByTokenAnalysis(keyword, range) {
    try {
      const searchCode = this.sourceCode.slice(range.start, range.end);
      const keywordRegex = new RegExp(`\\b${this.escapeRegExp(keyword)}\\b`, "g");
      let match;
      const candidates = [];
      while ((match = keywordRegex.exec(searchCode)) !== null) {
        const absolutePosition = range.start + match.index;
        candidates.push(absolutePosition);
      }
      if (candidates.length > 0) {
        const firstCandidate = candidates[0];
        return firstCandidate !== undefined ? firstCandidate : null;
      }
      return null;
    } catch (error) {
      console.debug("Token analysis failed:", error);
      return null;
    }
  }
  findKeywordByPatternMatching(keyword, range) {
    try {
      const searchCode = this.sourceCode.slice(range.start, range.end);
      const patterns = [
        new RegExp(`\\b${this.escapeRegExp(keyword)}\\s*[\\(\\{\\s]`, "g"),
        new RegExp(`^\\s*${this.escapeRegExp(keyword)}\\b`, "gm"),
        new RegExp(`\\b${this.escapeRegExp(keyword)}\\b`, "g")
      ];
      for (const pattern of patterns) {
        pattern.lastIndex = 0;
        const match = pattern.exec(searchCode);
        if (match) {
          const keywordStart = match.index;
          const actualKeywordMatch = match[0].match(new RegExp(`\\b${this.escapeRegExp(keyword)}\\b`));
          if (actualKeywordMatch) {
            const keywordOffset = match[0].indexOf(actualKeywordMatch[0]);
            return range.start + keywordStart + keywordOffset;
          }
        }
      }
      return null;
    } catch (error) {
      console.debug("Pattern matching failed:", error);
      return null;
    }
  }
  findKeywordByIndexOf(keyword, range) {
    try {
      const searchCode = this.sourceCode.slice(range.start, range.end);
      const index = searchCode.indexOf(keyword);
      if (index >= 0) {
        return range.start + index;
      }
      return null;
    } catch (error) {
      console.debug("indexOf search failed:", error);
      return null;
    }
  }
  escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
  }
  recordTokenSearchResult(node, keyword, position, method) {
    if (this.detailCollector) {
      try {
        const resultPosition = position !== null ? PositionConverter.spanToPosition(this.sourceCode, position) : { line: 0, column: 0 };
        this.detailCollector.addStepWithDiagnostic({
          line: resultPosition.line,
          column: resultPosition.column,
          increment: 0,
          ruleId: "token-search",
          description: `Token查找: ${keyword}`,
          context: `节点: ${node.type}, 方法: ${method}, 结果: ${position !== null ? "成功" : "失败"}`
        }, position !== null ? "DEBUG" : "WARNING", `使用 ${method} 方法${position !== null ? "成功找到" : "未找到"} '${keyword}' 关键字${position !== null ? ` (位置: ${position})` : ""}`);
      } catch (error) {
        console.warn(`Failed to record token search result for ${keyword}:`, error);
      }
    }
  }
  recordTokenSearchError(node, keyword, error) {
    if (this.detailCollector) {
      try {
        this.detailCollector.addErrorStep({
          line: 0,
          column: 0,
          increment: 0,
          ruleId: "token-search-error",
          description: `Token查找失败: ${keyword}`,
          context: error.message
        }, `Token查找系统在处理 '${keyword}' 时发生错误: ${error.message}`);
      } catch (detailError) {
        console.error(`Failed to record token search error for ${keyword}:`, detailError);
      }
    }
  }
  recordSpanFallbackStep(node, fallbackNode, method) {
    if (this.detailCollector) {
      try {
        this.detailCollector.addStepWithDiagnostic({
          line: 0,
          column: 0,
          increment: 0,
          ruleId: "span-fallback",
          description: `Span回退: ${node.type}`,
          context: `方法: ${method}, 来源: ${fallbackNode?.type || "unknown"}`
        }, "INFO", `节点 ${node.type} 使用 ${method} 方法进行 span 回退${fallbackNode ? `，基于 ${fallbackNode.type} 节点` : ""}`);
      } catch (error) {
        console.warn(`Failed to record span fallback for ${node.type}:`, error);
      }
    }
  }
  getDefaultSpan() {
    const functionStart = this.findCurrentFunctionStart();
    if (functionStart !== null) {
      return functionStart;
    }
    const firstCodePosition = this.findFirstCodePosition();
    if (firstCodePosition !== null) {
      return firstCodePosition;
    }
    return 0;
  }
  findCurrentFunctionStart() {
    const functionNode = this.findNearestAncestorOfType("FunctionDeclaration") || this.findNearestAncestorOfType("MethodDefinition") || this.findNearestAncestorOfType("ArrowFunctionExpression") || this.findNearestAncestorOfType("FunctionExpression");
    if (functionNode && this.isValidSpan(functionNode)) {
      return functionNode.span.start;
    }
    return null;
  }
  findFirstCodePosition() {
    const lines = this.sourceCode.split(`
`);
    let offset = 0;
    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine.length > 0 && !trimmedLine.startsWith("//") && !trimmedLine.startsWith("/*") && !trimmedLine.startsWith("*")) {
        const firstNonSpace = line.search(/\S/);
        return firstNonSpace >= 0 ? offset + firstNonSpace : offset;
      }
      offset += line.length + 1;
    }
    return null;
  }
  recordSpanError(node, error) {
    if (this.detailCollector) {
      try {
        this.detailCollector.addErrorStep({
          line: 0,
          column: 0,
          increment: 0,
          ruleId: "span-error",
          description: `Span修正失败: ${node.type}`,
          context: error.message
        }, `无法修正节点 ${node.type} 的 span: ${error.message}`);
      } catch (detailError) {
        console.error(`Failed to record span error for ${node.type}:`, detailError);
      }
    }
  }
  handleVisitError(node, error) {
    console.warn(`Error visiting node ${node.type}:`, error.message);
    if (this.detailCollector) {
      try {
        this.detailCollector.addErrorStep({
          line: 0,
          column: 0,
          increment: 0,
          ruleId: "visit-error",
          description: `访问节点失败: ${node.type}`,
          context: error.message
        }, `节点 ${node.type} 访问过程中发生错误: ${error.message}`);
      } catch (detailError) {
        console.error(`Failed to record visit error for ${node.type}:`, detailError);
      }
    }
  }
  addComplexity(increment, node, ruleId, description, context) {
    this.totalComplexity += increment;
    if (this.detailCollector && increment > 0) {
      try {
        const validSpan = this.validateSpan(node);
        const position = PositionConverter.spanToPosition(this.sourceCode, validSpan);
        this.detailCollector.addStep({
          line: position.line,
          column: position.column,
          increment,
          ruleId,
          description,
          context: context || `嵌套层级: ${this.nestingLevel}`
        });
      } catch (error) {
        console.warn(`Failed to record complexity step for ${ruleId}:`, error);
      }
    }
  }
  enterNesting() {
    this.nestingLevel++;
  }
  exitNesting() {
    this.nestingLevel = Math.max(0, this.nestingLevel - 1);
  }
  getNestingIncrement() {
    return this.nestingLevel;
  }
  isLogicalOperator(node) {
    return node.operator === "&&" || node.operator === "||" || node.operator === "??";
  }
  isDefaultValueAssignment(node) {
    if (node.operator === "??") {
      return true;
    }
    if (node.operator === "||") {
      return this.isInDefaultValueAssignmentContext(node);
    }
    if (node.operator === "&&") {
      return this.isPartOfDefaultValuePattern(node);
    }
    return false;
  }
  isInDefaultValueAssignmentContext(node) {
    const parent = this.getParent();
    if (!parent) {
      return false;
    }
    return parent.type === "VariableDeclarator" || parent.type === "AssignmentExpression";
  }
  isPartOfDefaultValuePattern(node) {
    return this.isInDefaultValueAssignmentContext(node);
  }
  isRecursiveCall(node) {
    const callee = this.getCalleeIdentifier(node);
    if (!callee || !this.currentFunctionName) {
      return false;
    }
    return callee === this.currentFunctionName;
  }
  getCalleeIdentifier(callNode) {
    const callee = callNode.callee;
    if (!callee) {
      return null;
    }
    if (callee.type === "Identifier") {
      return callee.value || callee.name;
    }
    if (callee.type === "MemberExpression") {
      const object = callee.object;
      const property = callee.property;
      if (object?.type === "ThisExpression" && property?.type === "Identifier") {
        return property.value || property.name;
      }
    }
    return null;
  }
  checkAndApplyMixingPenaltyOnce(node) {
    const rootNode = this.findLogicalExpressionRoot(node);
    if (this.processedMixingNodes.has(rootNode)) {
      return;
    }
    if (this.detectLogicalOperatorMixing(rootNode)) {
      this.addComplexity(1, rootNode, "logical-operator-mixing", "逻辑运算符混用惩罚", "混用不同类型的逻辑运算符");
      this.processedMixingNodes.add(rootNode);
    }
  }
  detectLogicalOperatorMixing(node) {
    if (!this.options.enableMixedLogicOperatorPenalty) {
      return false;
    }
    const currentOperator = node.operator;
    if (!currentOperator || !["&&", "||"].includes(currentOperator)) {
      return false;
    }
    if (this.isDefaultValueAssignment(node)) {
      return false;
    }
    if (this.hasParenthesizedOperands(node)) {
      return false;
    }
    const hasConflictingOperators = this.hasConflictingLogicalOperators(node, currentOperator);
    const parent = this.getParent();
    const parentHasConflict = parent && this.isLogicalExpressionType(parent) && parent.operator !== currentOperator && ["&&", "||"].includes(parent.operator);
    return hasConflictingOperators || Boolean(parentHasConflict);
  }
  hasParenthesizedOperands(node) {
    if (this.isDirectlyParenthesized(node.left) || this.isDirectlyParenthesized(node.right)) {
      return true;
    }
    if (this.hasLogicalGroupingParentheses(node)) {
      return true;
    }
    return false;
  }
  isDirectlyParenthesized(node) {
    return node && node.type === "ParenthesizedExpression";
  }
  hasLogicalGroupingParentheses(node) {
    if (!node)
      return false;
    if (node.left && this.containsLogicalGrouping(node.left)) {
      return true;
    }
    if (node.right && this.containsLogicalGrouping(node.right)) {
      return true;
    }
    return false;
  }
  containsLogicalGrouping(node) {
    if (!node || typeof node !== "object")
      return false;
    if (node.type === "ParenthesizedExpression") {
      return this.containsLogicalOperators(node.expression);
    }
    if (node.left && this.containsLogicalGrouping(node.left))
      return true;
    if (node.right && this.containsLogicalGrouping(node.right))
      return true;
    return false;
  }
  containsLogicalOperators(node) {
    if (!node || typeof node !== "object")
      return false;
    if ((node.type === "LogicalExpression" || node.type === "BinaryExpression") && ["&&", "||"].includes(node.operator)) {
      return true;
    }
    const checkNode = (childNode) => {
      if (childNode && typeof childNode === "object") {
        return this.containsLogicalOperators(childNode);
      }
      return false;
    };
    for (const key in node) {
      if (key === "span" || key === "type")
        continue;
      const value = node[key];
      if (Array.isArray(value)) {
        if (value.some(checkNode))
          return true;
      } else if (checkNode(value)) {
        return true;
      }
    }
    return false;
  }
  hasConflictingLogicalOperators(node, currentOperator) {
    if (!node)
      return false;
    const checkChild = (child) => {
      if (!child)
        return false;
      if (this.isLogicalExpressionType(child)) {
        return child.operator !== currentOperator && ["&&", "||"].includes(child.operator);
      }
      if (child.type === "ParenthesisExpression" && child.expression) {
        return this.checkChildForLogicalOperator(child.expression, currentOperator);
      }
      return this.hasConflictingLogicalOperators(child, currentOperator);
    };
    return checkChild(node.left) || checkChild(node.right);
  }
  checkChildForLogicalOperator(child, currentOperator) {
    if (!child)
      return false;
    if (this.isLogicalExpressionType(child)) {
      return child.operator !== currentOperator && ["&&", "||"].includes(child.operator);
    }
    if (child.left && this.checkChildForLogicalOperator(child.left, currentOperator)) {
      return true;
    }
    if (child.right && this.checkChildForLogicalOperator(child.right, currentOperator)) {
      return true;
    }
    return false;
  }
  isLogicalExpressionType(node) {
    return node && (node.type === "LogicalExpression" || node.type === "BinaryExpression");
  }
  findLogicalExpressionRoot(node) {
    let current = node;
    let parent = this.getParent();
    while (parent && this.isLogicalExpressionType(parent) && ["&&", "||"].includes(parent.operator)) {
      current = parent;
      break;
    }
    return current;
  }
}
export {
  ComplexityVisitor
};
