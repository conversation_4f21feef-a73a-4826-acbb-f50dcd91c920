/**
 * 测试 span-debug-info 工具函数
 */

import { extractSpanSourceCode, sanitizeCodeSnippet, formatPositionInfo } from '../src/utils/span-debug-info';

// 测试 extractSpanSourceCode
const testSourceCode = `function test() {
  if (condition) {
    return true;
  }
  return false;
}`;

console.log('🧪 测试 extractSpanSourceCode:');
const snippet = extractSpanSourceCode(testSourceCode, { start: 18, end: 30 });
console.log(`提取结果: "${snippet}"`);

// 测试 sanitizeCodeSnippet
console.log('\n🧪 测试 sanitizeCodeSnippet:');
const unsafeCode = 'console.log("hello\nworld\t")\x1b[31m';
const safeCode = sanitizeCodeSnippet(unsafeCode, 30);
console.log(`转义结果: "${safeCode}"`);

// 测试 formatPositionInfo
console.log('\n🧪 测试 formatPositionInfo:');
const positionInfo = formatPositionInfo(
  { start: 100, end: 120 },
  { line: 5, column: 10 }
);
console.log(`位置信息: ${positionInfo}`);

console.log('\n✅ 核心工具函数测试完成');