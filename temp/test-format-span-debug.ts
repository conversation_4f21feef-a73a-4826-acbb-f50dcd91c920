/**
 * 测试 formatSpanDebugInfo 主函数
 */

import { formatSpanDebugInfo } from '../src/utils/span-debug-info';
import { DetailStep } from '../src/core/types';
import { writeFileSync } from 'fs';

async function testFormatSpanDebugInfo() {
  // 创建测试文件
  const testFilePath = './temp/test-code.ts';
  const testCode = `function calculateComplexity() {
  if (condition) {
    for (let i = 0; i < items.length; i++) {
      if (items[i].isValid) {
        processItem(items[i]);
      }
    }
  }
  return result;
}`;

  writeFileSync(testFilePath, testCode);

  // 创建测试 DetailStep
  const testStep: DetailStep = {
    line: 2,
    column: 2,
    increment: 1,
    cumulative: 1,
    ruleId: 'if-statement',
    nestingLevel: 1,
    span: { start: 30, end: 43 }, // "if (condition)"
    nodeType: 'IfStatement',
    shouldShowContext: true
  };

  console.log('🧪 测试 formatSpanDebugInfo:');
  
  // 测试正常情况
  console.log('\n📍 测试正常情况 (有 span 信息):');
  const debugInfo = await formatSpanDebugInfo(testStep, testFilePath, { debug: true });
  if (debugInfo) {
    console.log(debugInfo);
  } else {
    console.log('返回 null');
  }

  // 测试无 span 情况
  console.log('\n📍 测试无 span 情况:');
  const stepNoSpan = { ...testStep, span: undefined };
  const debugInfoNoSpan = await formatSpanDebugInfo(stepNoSpan, testFilePath, { debug: true });
  if (debugInfoNoSpan) {
    console.log(debugInfoNoSpan);
  } else {
    console.log('返回 null');
  }

  // 测试文件不存在情况
  console.log('\n📍 测试文件不存在情况:');
  const debugInfoNoFile = await formatSpanDebugInfo(testStep, './not-exist.ts', { debug: true });
  if (debugInfoNoFile) {
    console.log(debugInfoNoFile);
  } else {
    console.log('返回 null');
  }

  // 测试 span 超出范围情况
  console.log('\n📍 测试 span 超出范围情况:');
  const stepOutOfRange = { 
    ...testStep, 
    span: { start: 1000, end: 2000 } // 远超出文件长度
  };
  const debugInfoOutOfRange = await formatSpanDebugInfo(stepOutOfRange, testFilePath, { debug: true });
  if (debugInfoOutOfRange) {
    console.log(debugInfoOutOfRange);
  } else {
    console.log('返回 null');
  }

  console.log('\n✅ formatSpanDebugInfo 测试完成');
}

testFormatSpanDebugInfo().catch(console.error);