/**
 * Task 11: 错误恢复和回退机制完善 - 验证测试
 * 
 * 测试增强版的错误恢复机制，包括：
 * 1. 多级错误恢复策略
 * 2. 智能父节点选择
 * 3. 紧急位置生成
 * 4. 详细的错误记录和诊断信息
 */

import { ComplexityVisitor } from '../src/core/complexity-visitor';
import { DetailCollector } from '../src/core/detail-collector';
import { ASTParser } from '../src/core/parser';

async function testErrorRecoveryMechanism() {
  console.log('🔬 Task 11: 错误恢复和回退机制完善 - 验证测试');
  console.log('='.repeat(60));
  
  const parser = new ASTParser();
  
  // 测试1: 基本错误恢复
  console.log('\n📋 测试1: 基本错误恢复机制');
  const sourceCode1 = `
    function testBasicRecovery() {
      if (condition) {
        return true;
      }
    }
  `;
  
  try {
    const ast1 = await parser.parseCode(sourceCode1, 'test1.ts');
    const detailCollector1 = new DetailCollector();
    const visitor1 = new ComplexityVisitor(sourceCode1, detailCollector1);
    
    visitor1.visit(ast1);
    
    const results1 = visitor1.getResults();
    console.log(`✅ 基本恢复测试成功`);
    console.log(`   - 函数数量: ${results1.length}`);
    console.log(`   - 总复杂度: ${visitor1.getTotalComplexity()}`);
    console.log(`   - 详细步骤数: ${results1[0]?.details?.length || 0}`);
    
  } catch (error) {
    console.log(`❌ 基本恢复测试失败: ${error}`);
  }
  
  // 测试2: 复杂嵌套结构的错误恢复
  console.log('\n📋 测试2: 复杂嵌套结构错误恢复');
  const sourceCode2 = `
    function testComplexRecovery() {
      try {
        if (condition1) {
          for (let i = 0; i < 10; i++) {
            if (condition2) {
              const result = value ? processValue(value) : defaultValue;
              if (result && result.isValid) {
                return result;
              }
            }
          }
        }
      } catch (error) {
        console.error(error);
      }
    }
  `;
  
  try {
    const ast2 = await parser.parseCode(sourceCode2, 'test2.ts');
    const detailCollector2 = new DetailCollector();
    const visitor2 = new ComplexityVisitor(sourceCode2, detailCollector2);
    
    visitor2.visit(ast2);
    
    const results2 = visitor2.getResults();
    console.log(`✅ 复杂恢复测试成功`);
    console.log(`   - 函数数量: ${results2.length}`);
    console.log(`   - 总复杂度: ${visitor2.getTotalComplexity()}`);
    console.log(`   - 详细步骤数: ${results2[0]?.details?.length || 0}`);
    
    // 显示一些详细步骤
    if (results2[0]?.details && results2[0].details.length > 0) {
      console.log('   - 前几个复杂度步骤:');
      results2[0].details.slice(0, 5).forEach((step, index) => {
        console.log(`     ${index + 1}. ${step.description} (复杂度: +${step.increment})`);
      });
    }
    
  } catch (error) {
    console.log(`❌ 复杂恢复测试失败: ${error}`);
  }
  
  // 测试3: JSX/TSX 中的错误恢复
  console.log('\n📋 测试3: JSX/TSX 错误恢复');
  const sourceCode3 = `
    function Component() {
      if (!data) {
        return <div>Loading...</div>;
      }
      
      return (
        <div>
          {items.map(item => (
            item.active ? (
              <span key={item.id}>{item.name}</span>
            ) : null
          ))}
        </div>
      );
    }
  `;
  
  try {
    const ast3 = await parser.parseCode(sourceCode3, 'test3.tsx');
    const detailCollector3 = new DetailCollector();
    const visitor3 = new ComplexityVisitor(sourceCode3, detailCollector3);
    
    visitor3.visit(ast3);
    
    const results3 = visitor3.getResults();
    console.log(`✅ JSX 恢复测试成功`);
    console.log(`   - 函数数量: ${results3.length}`);
    console.log(`   - 总复杂度: ${visitor3.getTotalComplexity()}`);
    console.log(`   - 详细步骤数: ${results3[0]?.details?.length || 0}`);
    
  } catch (error) {
    console.log(`❌ JSX 恢复测试失败: ${error}`);
  }
  
  // 测试4: 测试紧急位置生成机制
  console.log('\n📋 测试4: 紧急位置生成机制');
  const sourceCodeMinified = `function test(){if(a&&(b||c)){for(let i=0;i<10;i++){if(d){return 1;}}}}`;
  
  try {
    const ast4 = await parser.parseCode(sourceCodeMinified, 'test4.ts');
    const detailCollector4 = new DetailCollector();
    const visitor4 = new ComplexityVisitor(sourceCodeMinified, detailCollector4);
    
    visitor4.visit(ast4);
    
    const results4 = visitor4.getResults();
    console.log(`✅ 紧急位置生成测试成功`);
    console.log(`   - 函数数量: ${results4.length}`);
    console.log(`   - 总复杂度: ${visitor4.getTotalComplexity()}`);
    console.log(`   - 详细步骤数: ${results4[0]?.details?.length || 0}`);
    
  } catch (error) {
    console.log(`❌ 紧急位置生成测试失败: ${error}`);
  }
  
  console.log('\n🎉 Task 11 错误恢复机制验证完成!');
  console.log('='.repeat(60));
}

// 运行测试
testErrorRecoveryMechanism().catch(console.error);