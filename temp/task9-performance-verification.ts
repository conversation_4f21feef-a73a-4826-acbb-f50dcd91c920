#!/usr/bin/env bun
/**
 * Task 9 - 专项测试和验证：性能基准测试
 * 验证 Token 查找系统的可靠性和性能
 */

import { ComplexityVisitor } from '../src/core/complexity-visitor';
import { ASTParser } from '../src/core/parser';

const largeCodeSample = `
// 大型代码样本 - 包含各种控制流和现代语法
function complexBusinessLogic(data: any[]): ProcessResult {
  const results: ProcessResult[] = [];
  
  // 多重嵌套循环和条件
  for (let i = 0; i < data.length; i++) {
    const item = data[i];
    
    if (item && item.active) {
      const processors = getProcessors(item.type);
      
      for (const processor of processors) {
        try {
          const result = processor.process(item);
          
          if (result.success) {
            results.push({
              ...result,
              timestamp: Date.now(),
              processor: processor.name
            });
          } else if (result.error) {
            console.error('Processing failed:', result.error);
            
            switch (result.error.type) {
              case 'INVALID_DATA':
                handleInvalidData(item, result.error);
                break;
              case 'NETWORK_ERROR':
                if (shouldRetry(result.error)) {
                  await retryOperation(processor, item);
                }
                break;
              default:
                logUnknownError(result.error);
            }
          }
        } catch (error) {
          handleProcessingException(error, item, processor);
        }
      }
    }
  }
  
  // 箭头函数和现代语法
  const sortedResults = results
    .filter(result => result.isValid)
    .map(result => ({
      ...result,
      score: calculateScore(result)
    }))
    .sort((a, b) => b.score - a.score);
    
  // JSX 样式代码（在 TSX 文件中）
  const renderResults = () => (
    <div className="results">
      {sortedResults.map(result => (
        <div key={result.id} className="result-item">
          {result.success ? (
            <SuccessIndicator result={result} />
          ) : (
            <ErrorIndicator error={result.error} />
          )}
        </div>
      ))}
    </div>
  );
  
  // 三元运算符嵌套
  const status = sortedResults.length > 0 
    ? sortedResults.every(r => r.success) 
      ? 'ALL_SUCCESS' 
      : 'PARTIAL_SUCCESS'
    : 'NO_RESULTS';
    
  return {
    results: sortedResults,
    status,
    metadata: {
      processedCount: data.length,
      successCount: sortedResults.filter(r => r.success).length,
      timestamp: Date.now()
    }
  };
}

// 异步箭头函数
const asyncProcessors = [
  async (item: any) => await validateItem(item),
  async (item: any) => await transformItem(item),
  async (item: any) => await saveItem(item)
];

// 复杂的控制流
while (hasMoreData()) {
  const batch = getNextBatch();
  
  if (batch.length === 0) {
    break;
  }
  
  for await (const item of batch) {
    try {
      const validation = await validateAsync(item);
      
      if (!validation.isValid) {
        continue;
      }
      
      const processed = await processItem(item);
      
      if (processed && processed.shouldSave) {
        await saveProcessedItem(processed);
      }
    } catch (error) {
      if (error instanceof ValidationError) {
        logValidationError(error);
      } else if (error instanceof ProcessingError) {
        logProcessingError(error);
      } else {
        throw error; // 未知错误重新抛出
      }
    }
  }
}
`.repeat(20); // 重复 20 次以创建大型代码

async function performanceTest() {
  console.log('🚀 开始 Task 9 性能基准测试...');
  console.log(`📊 代码大小: ${Math.round(largeCodeSample.length / 1024)}KB`);
  
  const startTime = Date.now();
  
  try {
    // 解析大型代码
    const parseStart = Date.now();
    const parser = new ASTParser();
    const ast = await parser.parseCode(largeCodeSample, 'test-performance.tsx');
    const parseTime = Date.now() - parseStart;
    console.log(`✅ 代码解析耗时: ${parseTime}ms`);
    
    // 创建访问者并进行复杂度分析
    const analysisStart = Date.now();
    const visitor = new ComplexityVisitor({}, largeCodeSample);
    visitor.visit(ast);
    const analysisTime = Date.now() - analysisStart;
    
    const totalTime = Date.now() - startTime;
    
    console.log('\n📈 性能测试结果:');
    console.log(`   • 解析耗时: ${parseTime}ms`);
    console.log(`   • 分析耗时: ${analysisTime}ms`);
    console.log(`   • 总耗时: ${totalTime}ms`);
    console.log(`   • 总复杂度: ${visitor.complexity}`);
    
    // 验证性能要求
    const performanceOk = totalTime < 5000; // 5秒内完成
    console.log(`\n${performanceOk ? '✅' : '❌'} 性能要求验证: ${performanceOk ? '通过' : '失败'}`);
    console.log(`   要求: < 5000ms, 实际: ${totalTime}ms`);
    
    return performanceOk;
    
  } catch (error) {
    console.error('❌ 性能测试失败:', error);
    return false;
  }
}

async function tokenSearchReliabilityTest() {
  console.log('\n🔍 Token 查找系统可靠性测试...');
  
  const testCases = [
    { code: 'if (condition) { return true; }', keyword: 'if', expected: true },
    { code: 'for (let i = 0; i < 10; i++) { }', keyword: 'for', expected: true },
    { code: 'while (running) { process(); }', keyword: 'while', expected: true },
    { code: 'switch (type) { case "A": break; }', keyword: 'switch', expected: true },
    { code: 'const x = (a, b) => a + b;', keyword: '=>', expected: true },
    { code: '<div>{content}</div>', keyword: '<', expected: true },
    { code: 'function test() { }', keyword: 'function', expected: true },
    { code: 'try { risky(); } catch (e) { }', keyword: 'catch', expected: true }
  ];
  
  let passed = 0;
  let total = testCases.length;
  
  for (const testCase of testCases) {
    try {
      const parser = new ASTParser();
      const ast = await parser.parseCode(testCase.code, 'test.tsx');
      const visitor = new ComplexityVisitor({}, testCase.code);
      visitor.visit(ast);
      
      // Token 查找能力通过复杂度分析间接验证
      // 如果能正确分析复杂度，说明位置定位工作正常
      const hasComplexity = visitor.complexity > 0;
      
      if (hasComplexity === testCase.expected) {
        passed++;
        console.log(`   ✅ ${testCase.keyword}: 通过`);
      } else {
        console.log(`   ❌ ${testCase.keyword}: 失败`);
      }
    } catch (error) {
      console.log(`   ❌ ${testCase.keyword}: 错误 - ${error.message}`);
    }
  }
  
  const reliability = (passed / total) * 100;
  console.log(`\n📊 Token 查找可靠性: ${passed}/${total} (${reliability.toFixed(1)}%)`);
  
  return reliability >= 95; // 要求 95% 以上可靠性
}

async function strategyCompletenessTest() {
  console.log('\n🧩 策略映射表完整性测试...');
  
  // 检查策略映射表覆盖的节点类型
  const strategies = ComplexityVisitor['NODE_POSITION_STRATEGIES'];
  const registeredTypes = Array.from(strategies.keys());
  
  console.log(`📋 已注册的策略数量: ${registeredTypes.length}`);
  console.log('   已注册的节点类型:');
  
  registeredTypes.forEach(type => {
    const strategy = strategies.get(type);
    console.log(`   • ${type} (优先级: ${strategy?.priority})`);
  });
  
  // 验证关键节点类型是否都有策略
  const requiredTypes = [
    'IfStatement', 'WhileStatement', 'ForStatement', 'DoWhileStatement',
    'SwitchStatement', 'TryStatement', 'CatchClause', 'ConditionalExpression',
    'ArrowFunctionExpression', 'FunctionExpression', 'FunctionDeclaration',
    'JSXElement', 'JSXFragment', 'JSXExpressionContainer',
    'TypeAnnotation', 'TSTypeAnnotation'
  ];
  
  let coverage = 0;
  for (const type of requiredTypes) {
    if (strategies.has(type)) {
      coverage++;
    } else {
      console.log(`   ⚠️  缺少策略: ${type}`);
    }
  }
  
  const completeness = (coverage / requiredTypes.length) * 100;
  console.log(`\n📊 策略完整性: ${coverage}/${requiredTypes.length} (${completeness.toFixed(1)}%)`);
  
  return completeness >= 90; // 要求 90% 以上完整性
}

// 主测试函数
async function main() {
  console.log('🎯 Task 9: 专项测试和验证');
  console.log('=' .repeat(50));
  
  const results = {
    performance: await performanceTest(),
    reliability: await tokenSearchReliabilityTest(),
    completeness: await strategyCompletenessTest()
  };
  
  console.log('\n🏁 测试总结:');
  console.log('=' .repeat(50));
  console.log(`性能测试: ${results.performance ? '✅ 通过' : '❌ 失败'}`);
  console.log(`可靠性测试: ${results.reliability ? '✅ 通过' : '❌ 失败'}`);
  console.log(`完整性测试: ${results.completeness ? '✅ 通过' : '❌ 失败'}`);
  
  const allPassed = results.performance && results.reliability && results.completeness;
  console.log(`\n${allPassed ? '🎉' : '💥'} Task 9 整体结果: ${allPassed ? '全部通过' : '存在问题'}`);
  
  if (allPassed) {
    console.log('\n✨ SWC Span 智能回退机制第二阶段核心功能已完成！');
    console.log('   • Token 查找系统运行稳定');
    console.log('   • 箭头函数和 JSX 定位精确');
    console.log('   • 策略映射表功能完善');
    console.log('   • 性能指标符合要求');
  }
  
  return allPassed;
}

// 运行测试
if (import.meta.main) {
  main().then(success => {
    process.exit(success ? 0 : 1);
  });
}