import { BaseFormatter } from './base';
import type { AnalysisResult, DetailStep, Position } from '../core/types';
import { DiagnosticMarker } from '../core/types';
import type { CLIOptions } from '../config/types';
import { writeFile } from 'fs/promises';
import { getCodeFrameGenerator, type CodeFrameResult } from '../utils/code-frame-generator';

/**
 * JSON Schema 版本和元数据
 */
interface JsonSchemaMetadata {
  schemaVersion: string;
  generatedAt: string;
  format: string;
  detailsEnabled: boolean;
  contextEnabled: boolean;
  contextAllEnabled: boolean;
  errorRecoveryEnabled: boolean;
}

/**
 * 错误恢复信息
 */
interface ErrorRecoveryInfo {
  /** 恢复尝试次数 */
  recoveryAttempts: number;
  /** 恢复策略 */
  recoveryStrategy: string;
  /** 是否成功恢复 */
  success: boolean;
  /** 错误信息 */
  error?: string;
  /** 恢复耗时 */
  recoveryDuration?: number;
}

/**
 * 增强的详细步骤，包含上下文信息和错误恢复信息
 */
interface ContextEnhancedDetailStep extends DetailStep {
  /** 位置信息 */
  position?: Position;
  /** 代码上下文框架 */
  codeFrame?: string;
  /** 上下文是否可用 */
  contextAvailable: boolean;
  /** 上下文生成错误信息 */
  contextError?: string;
  /** 错误恢复信息 */
  errorRecovery?: ErrorRecoveryInfo;
}

/**
 * 增强的 JSON 输出结果，包含元数据和诊断信息
 */
interface EnhancedJsonResult extends AnalysisResult {
  metadata: JsonSchemaMetadata;
  diagnostics?: {
    totalWarnings: number;
    totalErrors: number;
    totalUnknown: number;
    hasIssues: boolean;
    errorRecoveryStats?: {
      totalRecoveryAttempts: number;
      successfulRecoveries: number;
      failedRecoveries: number;
      mostCommonStrategy: string;
    };
  };
}

export class JsonFormatter extends BaseFormatter {
  private codeFrameGenerator = getCodeFrameGenerator();
  
  public override async format(result: AnalysisResult, showDetails?: boolean, options?: CLIOptions): Promise<string> {
    // 首先应用文件复杂度过滤（异步）
    const filteredResult = await this.applyFileComplexityFilter(result, options);
    
    // 为JSON输出添加severity信息、详细模式支持、诊断信息、上下文信息和错误恢复信息
    const enrichedResult = this.enrichWithDiagnostics(filteredResult, showDetails, options);
    return JSON.stringify(enrichedResult, null, 2);
  }
  
  public override async writeToFile(result: AnalysisResult, outputPath: string, options?: CLIOptions): Promise<void> {
    // 首先应用文件复杂度过滤（异步）
    const filteredResult = await this.applyFileComplexityFilter(result, options);
    
    const enrichedResult = await this.enrichWithDiagnosticsAsync(filteredResult, true, options); // 文件输出时总是显示详细信息
    const jsonContent = JSON.stringify(enrichedResult, null, 2);
    
    try {
      await writeFile(outputPath, jsonContent, 'utf-8');
    } catch (error) {
      throw new Error(`无法写入JSON文件 ${outputPath}: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
  
  /**
   * 增强结果数据，添加诊断信息、元数据、Schema 版本控制和上下文信息（同步版本）
   */
  private enrichWithDiagnostics(result: AnalysisResult, showDetails?: boolean, options?: CLIOptions): EnhancedJsonResult {
    // 首先应用现有的severity增强
    const severityEnhanced = this.enrichWithSeverity(result, showDetails, options);
    
    // 收集诊断统计信息
    const diagnostics = this.collectDiagnostics(severityEnhanced, showDetails);
    
    // 获取过滤统计信息
    const filterStatistics = this.getFilterStatistics(result);
    
    // 创建元数据，包含错误恢复信息和质量门禁信息
    const metadata: JsonSchemaMetadata = {
      schemaVersion: '2.1.0', // 升级版本以支持错误恢复
      generatedAt: new Date().toISOString(),
      format: 'cognitive-complexity-json',
      detailsEnabled: showDetails || false,
      contextEnabled: options?.showContext || false,
      contextAllEnabled: options?.showAllContext || false,
      errorRecoveryEnabled: true
    };
    
    // 如果有质量门禁信息，添加到 metadata 中
    const qualityGateInfo = (result as any).qualityGateInfo;
    if (qualityGateInfo) {
      (metadata as any).qualityGate = qualityGateInfo;
    }
    
    // 返回增强的结果
    const enhancedResult: EnhancedJsonResult = {
      ...severityEnhanced,
      metadata
    };
    
    // 添加过滤统计信息到结果中（如果存在）
    if (filterStatistics) {
      (enhancedResult as any).filterStatistics = filterStatistics;
    }
    
    // 只有当存在诊断信息时才添加diagnostics字段
    if (diagnostics.hasIssues || diagnostics.errorRecoveryStats) {
      enhancedResult.diagnostics = diagnostics;
    }
    
    return enhancedResult;
  }

  /**
   * 增强结果数据，添加诊断信息、元数据、Schema 版本控制和上下文信息（异步版本，用于文件输出）
   */
  private async enrichWithDiagnosticsAsync(result: AnalysisResult, showDetails?: boolean, options?: CLIOptions): Promise<EnhancedJsonResult> {
    // 首先应用现有的severity增强（异步版本）
    const severityEnhanced = await this.enrichWithSeverityAsync(result, showDetails, options);
    
    // 收集诊断统计信息
    const diagnostics = this.collectDiagnostics(severityEnhanced, showDetails);
    
    // 获取过滤统计信息
    const filterStatistics = this.getFilterStatistics(result);
    
    // 创建元数据，包含错误恢复信息和质量门禁信息
    const metadata: JsonSchemaMetadata = {
      schemaVersion: '2.1.0', // 升级版本以支持错误恢复
      generatedAt: new Date().toISOString(),
      format: 'cognitive-complexity-json',
      detailsEnabled: showDetails || false,
      contextEnabled: options?.showContext || false,
      contextAllEnabled: options?.showAllContext || false,
      errorRecoveryEnabled: true
    };
    
    // 如果有质量门禁信息，添加到 metadata 中
    const qualityGateInfo = (result as any).qualityGateInfo;
    if (qualityGateInfo) {
      (metadata as any).qualityGate = qualityGateInfo;
    }
    
    // 返回增强的结果
    const enhancedResult: EnhancedJsonResult = {
      ...severityEnhanced,
      metadata
    };
    
    // 添加过滤统计信息到结果中（如果存在）
    if (filterStatistics) {
      (enhancedResult as any).filterStatistics = filterStatistics;
    }
    
    // 只有当存在诊断信息时才添加diagnostics字段
    if (diagnostics.hasIssues || diagnostics.errorRecoveryStats) {
      enhancedResult.diagnostics = diagnostics;
    }
    
    return enhancedResult;
  }
  
  /**
   * 收集诊断统计信息，包括错误恢复统计
   */
  private collectDiagnostics(result: AnalysisResult, showDetails?: boolean) {
    let totalWarnings = 0;
    let totalErrors = 0;
    let totalUnknown = 0;
    
    // 错误恢复统计
    let totalRecoveryAttempts = 0;
    let successfulRecoveries = 0;
    let failedRecoveries = 0;
    const strategyCount = new Map<string, number>();
    
    if (showDetails) {
      for (const fileResult of result.results) {
        for (const func of fileResult.functions) {
          if (func.details) {
            for (const step of func.details) {
              if (step.diagnosticMarker) {
                switch (step.diagnosticMarker) {
                  case DiagnosticMarker.WARNING:
                    totalWarnings++;
                    break;
                  case DiagnosticMarker.ERROR:
                    totalErrors++;
                    break;
                  case DiagnosticMarker.UNKNOWN:
                    totalUnknown++;
                    break;
                }
              }
              
              // 检查规则ID是否为未知规则
              if (step.ruleId === 'unknown-rule') {
                totalUnknown++;
              }
              
              // 检查负增量（豁免场景）
              if (step.increment < 0) {
                totalWarnings++;
              }
              
              // 统计错误恢复信息
              const enhancedStep = step as ContextEnhancedDetailStep;
              if (enhancedStep.errorRecovery) {
                totalRecoveryAttempts += enhancedStep.errorRecovery.recoveryAttempts;
                if (enhancedStep.errorRecovery.success) {
                  successfulRecoveries++;
                } else {
                  failedRecoveries++;
                }
                
                // 统计策略使用频率
                const strategy = enhancedStep.errorRecovery.recoveryStrategy;
                strategyCount.set(strategy, (strategyCount.get(strategy) || 0) + 1);
              }
            }
          }
        }
      }
    }
    
    // 找出最常用的恢复策略
    let mostCommonStrategy = 'none';
    let maxCount = 0;
    for (const [strategy, count] of strategyCount.entries()) {
      if (count > maxCount) {
        maxCount = count;
        mostCommonStrategy = strategy;
      }
    }
    
    const result_diagnostics = {
      totalWarnings,
      totalErrors,
      totalUnknown,
      hasIssues: totalWarnings > 0 || totalErrors > 0 || totalUnknown > 0
    } as any;
    
    // 只有当有错误恢复活动时才添加恢复统计
    if (totalRecoveryAttempts > 0) {
      result_diagnostics.errorRecoveryStats = {
        totalRecoveryAttempts,
        successfulRecoveries,
        failedRecoveries,
        mostCommonStrategy
      };
    }
    
    return result_diagnostics;
  }
  
  private enrichWithSeverity(result: AnalysisResult, showDetails?: boolean, options?: CLIOptions): AnalysisResult {
    const enrichedResults = result.results.map(fileResult => ({
      ...fileResult,
      functions: fileResult.functions.map(func => {
        const enrichedFunc = {
          ...func,
          severity: this.getSeverityLevel(func.complexity)
        };
        
        // 如果不是详细模式，移除details字段以减少输出大小
        if (!showDetails && enrichedFunc.details) {
          delete enrichedFunc.details;
        }
        
        // 如果启用了上下文显示且有详细信息，则增强详细步骤（同步版本）
        if (showDetails && enrichedFunc.details && (options?.showContext || options?.showAllContext)) {
          enrichedFunc.details = this.enrichDetailsWithContext(
            enrichedFunc.details, 
            fileResult.filePath,
            options
          );
        }
        
        return enrichedFunc;
      })
    }));
    
    return {
      ...result,
      results: enrichedResults
    };
  }

  /**
   * 异步版本的severity增强，支持实际的代码框架生成
   */
  private async enrichWithSeverityAsync(result: AnalysisResult, showDetails?: boolean, options?: CLIOptions): Promise<AnalysisResult> {
    const enrichedResults = await Promise.all(result.results.map(async fileResult => ({
      ...fileResult,
      functions: await Promise.all(fileResult.functions.map(async func => {
        const enrichedFunc = {
          ...func,
          severity: this.getSeverityLevel(func.complexity)
        };
        
        // 如果不是详细模式，移除details字段以减少输出大小
        if (!showDetails && enrichedFunc.details) {
          delete enrichedFunc.details;
        }
        
        // 如果启用了上下文显示且有详细信息，则增强详细步骤（异步版本）
        if (showDetails && enrichedFunc.details && (options?.showContext || options?.showAllContext)) {
          enrichedFunc.details = await this.enrichDetailsWithContextAsync(
            enrichedFunc.details, 
            fileResult.filePath,
            options
          );
        }
        
        return enrichedFunc;
      }))
    })));
    
    return {
      ...result,
      results: enrichedResults
    };
  }
  
  /**
   * 为详细步骤增强上下文信息（同步版本，不进行实际的代码框架生成）
   */
  private enrichDetailsWithContext(
    details: DetailStep[], 
    filePath: string, 
    options?: CLIOptions
  ): ContextEnhancedDetailStep[] {
    return details.map(step => {
      const enhanced: ContextEnhancedDetailStep = {
        ...step,
        contextAvailable: false
      };
      
      // 检查是否应该显示上下文
      const shouldShow = options?.showAllContext || 
                        (options?.showContext && (step.shouldShowContext || step.increment > 1));
      
      if (shouldShow) {
        // 添加位置信息
        if (step.span) {
          enhanced.position = {
            line: step.line,
            column: step.column
          };
        }
        
        // 对于同步版本，只是标记为可用而不实际生成
        if (step.context) {
          enhanced.codeFrame = step.context;
          enhanced.contextAvailable = true;
        } else if (step.span || (step.line && step.column)) {
          enhanced.contextAvailable = true;
          enhanced.contextError = 'Context generation requires async operation';
        }
        
        // 包含上下文排序信息
        if (step.contextRank !== undefined) {
          enhanced.contextRank = step.contextRank;
        }
      }
      
      return enhanced;
    });
  }

  /**
   * 为详细步骤增强上下文信息（异步版本，实际生成代码框架并包含错误恢复信息）
   */
  private async enrichDetailsWithContextAsync(
    details: DetailStep[], 
    filePath: string, 
    options?: CLIOptions
  ): Promise<ContextEnhancedDetailStep[]> {
    return Promise.all(details.map(async step => {
      const enhanced: ContextEnhancedDetailStep = {
        ...step,
        contextAvailable: false
      };
      
      // 检查是否应该显示上下文
      const shouldShow = options?.showAllContext || 
                        (options?.showContext && (step.shouldShowContext || step.increment > 1));
      
      if (shouldShow) {
        // 添加位置信息
        if (step.span) {
          enhanced.position = {
            line: step.line,
            column: step.column
          };
        }
        
        // 尝试生成代码框架（带错误恢复）
        try {
          let frameResult: CodeFrameResult;
          const contextLines = options?.contextLines ?? 2;
          
          if (step.span) {
            frameResult = await this.codeFrameGenerator.generateFrameFromSpan(
              filePath,
              step.span,
              {
                highlightCode: false, // JSON输出不需要颜色
                linesAbove: contextLines,
                linesBelow: contextLines
              },
              step.nodeType
            );
          } else {
            frameResult = await this.codeFrameGenerator.generateFrame(
              filePath,
              step.line,
              step.column,
              {
                highlightCode: false, // JSON输出不需要颜色
                linesAbove: contextLines,
                linesBelow: contextLines
              }
            );
          }
          
          // 设置代码框架和状态
          if (frameResult.success) {
            enhanced.codeFrame = frameResult.frame;
            enhanced.contextAvailable = true;
          } else {
            enhanced.contextAvailable = false;
            enhanced.contextError = frameResult.error;
          }
          
          // 添加错误恢复信息
          if (frameResult.recoveryAttempts && frameResult.recoveryAttempts > 0) {
            enhanced.errorRecovery = {
              recoveryAttempts: frameResult.recoveryAttempts,
              recoveryStrategy: frameResult.recoveryStrategy || 'unknown',
              success: frameResult.success,
              error: frameResult.error
            };
          }
          
        } catch (error) {
          enhanced.contextAvailable = false;
          enhanced.contextError = error instanceof Error ? error.message : String(error);
          
          // 记录错误恢复信息
          enhanced.errorRecovery = {
            recoveryAttempts: 1,
            recoveryStrategy: 'exception-fallback',
            success: false,
            error: enhanced.contextError
          };
        }
        
        // 如果有现有的上下文，优先使用
        if (step.context && !enhanced.codeFrame) {
          enhanced.codeFrame = step.context;
          enhanced.contextAvailable = true;
        }
        
        // 包含上下文排序信息
        if (step.contextRank !== undefined) {
          enhanced.contextRank = step.contextRank;
        }
      }
      
      return enhanced;
    }));
  }
}