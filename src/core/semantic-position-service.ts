import type { Node } from '@swc/core';
import type { 
  SemanticContext, 
  PositionStrategy, 
  SemanticPositioningOptions,
  SEMANTIC_POSITION_STRATEGIES
} from './semantic-types';
import { CodeSemanticAnalyzer } from './code-semantic-analyzer';
import { PositionConverter } from '../utils/position-converter';

/**
 * 语义感知位置服务 - 提供智能的代码位置定位能力
 * 
 * 核心功能：
 * - 基于语义理解的精确位置定位
 * - 多级策略回退机制
 * - 节点类型特定的定位策略
 * - 智能跳过无意义的结构性代码
 */
export class SemanticPositionService {
  private readonly sourceCode: string;
  private readonly codeAnalyzer: CodeSemanticAnalyzer;
  private readonly options: SemanticPositioningOptions;
  private readonly lines: string[];

  constructor(
    sourceCode: string,
    codeAnalyzer: CodeSemanticAnalyzer,
    options: SemanticPositioningOptions = {}
  ) {
    this.sourceCode = sourceCode;
    this.codeAnalyzer = codeAnalyzer;
    this.options = {
      enableJSXSemantics: true,
      enableFunctionSemantics: true,
      enableSmartLineFiltering: true,
      maxFallbackLevels: 3,
      debugMode: false,
      ...options,
    };
    this.lines = sourceCode.split('\n');
  }

  /**
   * 找到节点的语义感知位置
   * @param node AST 节点
   * @param fallbackSpan 回退 span 位置
   * @returns 语义位置
   */
  findSemanticPosition(node: Node, fallbackSpan?: number): number {
    try {
      // 分析节点语义上下文
      const semanticContext = this.analyzeNodeSemantics(node);
      
      // 获取节点特定策略
      const strategy = this.getNodeSpecificStrategy(node.type);
      
      if (strategy) {
        // 尝试使用主要策略
        const primaryPosition = this.applyPositionStrategy(node, strategy.strategy, semanticContext);
        if (primaryPosition !== null) {
          return this.validateAndRefinePosition(primaryPosition, semanticContext);
        }
        
        // 尝试回退策略
        const fallbackPosition = this.applyPositionStrategy(node, strategy.fallback, semanticContext);
        if (fallbackPosition !== null) {
          return this.validateAndRefinePosition(fallbackPosition, semanticContext);
        }
      }
      
      // 使用通用语义定位
      const genericPosition = this.findGenericSemanticPosition(node, semanticContext);
      if (genericPosition !== null) {
        return this.validateAndRefinePosition(genericPosition, semanticContext);
      }
      
      // 最后使用回退 span
      if (fallbackSpan !== undefined) {
        return this.validateAndRefinePosition(fallbackSpan, semanticContext);
      }
      
      // 紧急定位
      return this.findEmergencyPosition(node);
      
    } catch (error) {
      if (this.options.debugMode) {
        console.warn('SemanticPositionService error:', error);
      }
      return fallbackSpan || this.findEmergencyPosition(node);
    }
  }

  /**
   * 寻找有意义的代码行
   * @param sourceCode 源代码
   * @param position 位置信息
   * @returns 有意义的位置
   */
  findMeaningfulCodeLine(sourceCode: string, position: { line: number; column: number }): { line: number; column: number } {
    const targetLine = position.line - 1; // 转换为 0 基准索引
    
    if (!this.options.enableSmartLineFiltering) {
      return position;
    }

    // 检查当前行是否有意义
    if (targetLine >= 0 && targetLine < this.lines.length) {
      const currentLine = this.lines[targetLine];
      const semanticInfo = this.codeAnalyzer.analyzeLineSemantics(currentLine || '', targetLine);
      
      if (semanticInfo.isLogicallySignificant) {
        return position;
      }
    }

    // 向上查找有意义的行
    const upwardLine = this.codeAnalyzer.findNearestLogicalCode(targetLine, 'up', 3);
    if (upwardLine !== null) {
      return { line: upwardLine + 1, column: 1 }; // 转换回 1 基准索引
    }

    // 向下查找有意义的行
    const downwardLine = this.codeAnalyzer.findNearestLogicalCode(targetLine, 'down', 2);
    if (downwardLine !== null) {
      return { line: downwardLine + 1, column: 1 };
    }

    // 返回原位置
    return position;
  }

  /**
   * 检查行是否有意义
   * @param lineContent 行内容
   * @param nodeType 节点类型
   * @returns 是否有意义
   */
  isSignificantLine(lineContent: string, nodeType?: string): boolean {
    const semanticInfo = this.codeAnalyzer.analyzeLineSemantics(lineContent);
    
    // 基础有意义性检查
    if (!semanticInfo.isLogicallySignificant) {
      return false;
    }

    // 根据节点类型进行特殊判断
    if (nodeType) {
      return this.isSignificantForNodeType(lineContent, nodeType);
    }

    return true;
  }

  /**
   * 分析节点语义上下文
   * @param node AST 节点
   * @returns 语义上下文
   */
  private analyzeNodeSemantics(node: Node): SemanticContext {
    const nodeType = node.type;
    
    // 确定代码模式
    let codePattern: SemanticContext['codePattern'] = 'expression';
    if (nodeType.includes('JSX')) {
      codePattern = 'jsx';
    } else if (nodeType.includes('Function') || nodeType.includes('Arrow')) {
      codePattern = 'function';
    } else if (nodeType.includes('If') || nodeType.includes('While') || nodeType.includes('For')) {
      codePattern = 'control-flow';
    } else if (nodeType.includes('Declaration')) {
      codePattern = 'declaration';
    }

    // 确定逻辑重要性
    let logicalImportance: SemanticContext['logicalImportance'] = 'secondary';
    if (['IfStatement', 'ConditionalExpression', 'LogicalExpression'].includes(nodeType)) {
      logicalImportance = 'primary';
    } else if (nodeType.includes('JSX') && nodeType !== 'JSXElement') {
      logicalImportance = 'primary';
    } else if (['{', '}', '(', ')'].some(char => nodeType.includes(char))) {
      logicalImportance = 'structural';
    }

    return {
      nodeType,
      codePattern,
      logicalImportance,
      surroundingElements: {
        hasJSX: this.sourceCode.includes('<'),
        hasTypeScript: this.sourceCode.includes(':') || this.sourceCode.includes('interface'),
        hasArrowFunctions: this.sourceCode.includes('=>'),
        nestingLevel: this.estimateNestingLevel(node),
      },
    };
  }

  /**
   * 获取节点特定策略
   * @param nodeType 节点类型
   * @returns 位置策略
   */
  private getNodeSpecificStrategy(nodeType: string): PositionStrategy | null {
    // 导入策略映射
    const strategies = require('./semantic-types').SEMANTIC_POSITION_STRATEGIES as typeof SEMANTIC_POSITION_STRATEGIES;
    return strategies[nodeType] || null;
  }

  /**
   * 应用位置定位策略
   * @param node AST 节点
   * @param strategyName 策略名称
   * @param context 语义上下文
   * @returns 位置或 null
   */
  private applyPositionStrategy(node: Node, strategyName: string, context: SemanticContext): number | null {
    switch (strategyName) {
      // JSX 相关策略
      case 'find-opening-tag-start':
        return this.findJSXOpeningTagStart(node);
      case 'find-jsx-content-start':
        return this.findJSXContentStart(node);
      case 'find-expression-content':
        return this.findJSXExpressionContent(node);
      case 'find-brace-content':
        return this.findBraceContent(node);

      // 函数相关策略
      case 'find-arrow-or-params':
        return this.findArrowOrParams(node);
      case 'find-function-keyword':
        return this.findFunctionKeyword(node);
      case 'find-params-start':
        return this.findParametersStart(node);
      case 'find-function-start':
        return this.findFunctionStart(node);

      // 控制流策略
      case 'find-condition-start':
        return this.findConditionStart(node);
      case 'find-if-keyword':
        return this.findKeyword(node, 'if');
      case 'find-while-keyword':
        return this.findKeyword(node, 'while');
      case 'find-for-keyword':
        return this.findKeyword(node, 'for');
      case 'find-question-mark':
        return this.findOperator(node, '?');

      // 逻辑操作符策略
      case 'find-logical-operator':
        return this.findLogicalOperator(node);
      case 'find-left-operand':
        return this.findLeftOperand(node);

      default:
        return null;
    }
  }

  /**
   * 验证和精化位置
   * @param position 原始位置
   * @param _context 语义上下文
   * @returns 精化后的位置
   */
  private validateAndRefinePosition(position: number, _context: SemanticContext): number {
    try {
      // 转换为行列位置
      const lineColumn = PositionConverter.spanToPosition(this.sourceCode, position);
      
      // 应用智能行筛选
      const refinedLineColumn = this.findMeaningfulCodeLine(this.sourceCode, lineColumn);
      
      // 转换回字符位置
      return PositionConverter.lineColumnToOffset(this.sourceCode, refinedLineColumn.line, refinedLineColumn.column);
    } catch (error) {
      // 验证失败，返回原位置
      return position;
    }
  }

  /**
   * 通用语义定位
   * @param node AST 节点
   * @param context 语义上下文
   * @returns 位置或 null
   */
  private findGenericSemanticPosition(node: Node, context: SemanticContext): number | null {
    if ('span' in node && node.span) {
      return (node.span as any).start;
    }
    return null;
  }

  /**
   * 紧急位置定位
   * @param node AST 节点
   * @returns 紧急位置
   */
  private findEmergencyPosition(node: Node): number {
    // 尝试从节点中获取任何可用的位置信息
    if ('span' in node && node.span) {
      return (node.span as any).start || 0;
    }
    
    // 返回代码开始位置
    return 0;
  }

  /**
   * 估算嵌套层级
   * @param node AST 节点
   * @returns 嵌套层级
   */
  private estimateNestingLevel(node: Node): number {
    // 简单实现：基于 span 位置估算嵌套深度
    if ('span' in node && node.span) {
      const position = (node.span as any).start;
      const lineColumn = PositionConverter.spanToPosition(this.sourceCode, position);
      const line = this.lines[lineColumn.line - 1];
      
      if (line) {
        // 计算前导空白字符数量作为嵌套层级的近似值
        const leadingWhitespace = line.match(/^\s*/)?.[0].length || 0;
        return Math.floor(leadingWhitespace / 2); // 假设每层缩进2个空格
      }
    }
    
    return 0;
  }

  /**
   * 判断行对特定节点类型是否有意义
   * @param lineContent 行内容
   * @param nodeType 节点类型
   * @returns 是否有意义
   */
  private isSignificantForNodeType(lineContent: string, nodeType: string): boolean {
    switch (nodeType) {
      case 'JSXElement':
      case 'JSXExpressionContainer':
        return this.codeAnalyzer.isLogicallySignificant(lineContent, { codePattern: 'jsx' } as any);
      
      case 'ArrowFunctionExpression':
      case 'FunctionExpression':
      case 'FunctionDeclaration':
        return this.codeAnalyzer.isLogicallySignificant(lineContent, { codePattern: 'function' } as any);
      
      case 'IfStatement':
      case 'WhileStatement':
      case 'ForStatement':
        return this.codeAnalyzer.isLogicallySignificant(lineContent, { codePattern: 'control-flow' } as any);
      
      default:
        return this.codeAnalyzer.isLogicallySignificant(lineContent);
    }
  }

  // 以下是具体的策略方法实现 - 简化版本，具体实现可以在后续迭代中完善

  private findJSXOpeningTagStart(node: Node): number | null {
    if ('span' in node && node.span) {
      return (node.span as any).start;
    }
    return null;
  }

  private findJSXContentStart(node: Node): number | null {
    return this.findJSXOpeningTagStart(node);
  }

  private findJSXExpressionContent(node: Node): number | null {
    if ('span' in node && node.span) {
      return (node.span as any).start;
    }
    return null;
  }

  private findBraceContent(node: Node): number | null {
    return this.findJSXExpressionContent(node);
  }

  private findArrowOrParams(node: Node): number | null {
    if ('span' in node && node.span) {
      return (node.span as any).start;
    }
    return null;
  }

  private findFunctionKeyword(node: Node): number | null {
    return this.findKeyword(node, 'function');
  }

  private findParametersStart(node: Node): number | null {
    if ('span' in node && node.span) {
      return (node.span as any).start;
    }
    return null;
  }

  private findFunctionStart(node: Node): number | null {
    return this.findParametersStart(node);
  }

  private findConditionStart(node: Node): number | null {
    if ('span' in node && node.span) {
      return (node.span as any).start;
    }
    return null;
  }

  private findKeyword(node: Node, keyword: string): number | null {
    if ('span' in node && node.span) {
      const start = (node.span as any).start;
      const lineColumn = PositionConverter.spanToPosition(this.sourceCode, start);
      const line = this.lines[lineColumn.line - 1];
      
      if (line) {
        const keywordIndex = line.indexOf(keyword);
        if (keywordIndex !== -1) {
          return PositionConverter.lineColumnToOffset(this.sourceCode, lineColumn.line, keywordIndex + 1);
        }
      }
    }
    return null;
  }

  private findOperator(node: Node, operator: string): number | null {
    return this.findKeyword(node, operator);
  }

  private findLogicalOperator(node: Node): number | null {
    // 查找 && 或 || 操作符
    const operators = ['&&', '||'];
    for (const op of operators) {
      const position = this.findKeyword(node, op);
      if (position !== null) {
        return position;
      }
    }
    return null;
  }

  private findLeftOperand(node: Node): number | null {
    if ('span' in node && node.span) {
      return (node.span as any).start;
    }
    return null;
  }
}