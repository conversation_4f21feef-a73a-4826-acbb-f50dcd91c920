import { DiagnosticMarker } from './types';

/**
 * 只读DetailStep接口
 * 外部消费者只能访问只读属性，确保类型安全
 */
export interface ReadonlyDetailStep {
  /** 代码行号 */
  readonly line: number;
  /** 代码列号 */
  readonly column: number;
  /** 本步骤复杂度增量 */
  readonly increment: number;
  /** 累计复杂度 */
  readonly cumulative: number;
  /** 规则标识符 (kebab-case 格式) */
  readonly ruleId: string;
  /** 人类可读的规则描述 */
  readonly description: string;
  /** 嵌套层级 */
  readonly nestingLevel: number;
  /** 可选的上下文信息 */
  readonly context: string;
  /** 诊断标记 */
  readonly diagnosticMarker?: DiagnosticMarker;
  /** 诊断消息 */
  readonly diagnosticMessage?: string;
  /** SWC span信息 (用于代码框架生成) */
  readonly span?: { start: number; end: number };
  /** AST节点类型 (用于SWC offset修正) */
  readonly nodeType?: string;
}

/**
 * 内部DetailStep接口
 * 对象池内部使用，允许修改属性进行重置
 */
export interface InternalDetailStep {
  /** 代码行号 */
  line: number;
  /** 代码列号 */
  column: number;
  /** 本步骤复杂度增量 */
  increment: number;
  /** 累计复杂度 */
  cumulative: number;
  /** 规则标识符 (kebab-case 格式) */
  ruleId: string;
  /** 人类可读的规则描述 */
  description: string;
  /** 嵌套层级 */
  nestingLevel: number;
  /** 可选的上下文信息 */
  context: string;
  /** 诊断标记 */
  diagnosticMarker?: DiagnosticMarker;
  /** 诊断消息 */
  diagnosticMessage?: string;
  /** SWC span信息 (用于代码框架生成) */
  span?: { start: number; end: number };
  /** AST节点类型 (用于SWC offset修正) */
  nodeType?: string;
}

/**
 * DetailStep工厂接口
 * 使用工厂模式创建和重置DetailStep对象
 */
export interface DetailStepFactory {
  /**
   * 创建新的DetailStep对象
   * @returns 新创建的DetailStep对象
   */
  create(): ReadonlyDetailStep;

  /**
   * 重置DetailStep对象的所有属性为默认值
   * @param step 要重置的DetailStep对象
   */
  reset(step: InternalDetailStep): void;

  /**
   * 验证DetailStep对象的有效性
   * @param step 要验证的DetailStep对象
   * @returns 如果对象有效则返回true
   */
  validate(step: ReadonlyDetailStep): boolean;
}

/**
 * 默认DetailStep工厂实现
 * 提供标准的创建、重置和验证逻辑
 */
export class DefaultDetailStepFactory implements DetailStepFactory {
  create(): ReadonlyDetailStep {
    const step: InternalDetailStep = {
      line: 0,
      column: 0,
      increment: 0,
      cumulative: 0,
      ruleId: '',
      description: '',
      nestingLevel: 0,
      context: '',
      diagnosticMarker: undefined,
      diagnosticMessage: undefined,
      span: undefined,
      nodeType: undefined
    };
    
    return step as ReadonlyDetailStep;
  }

  reset(step: InternalDetailStep): void {
    step.line = 0;
    step.column = 0;
    step.increment = 0;
    step.cumulative = 0;
    step.ruleId = '';
    step.description = '';
    step.nestingLevel = 0;
    step.context = '';
    step.diagnosticMarker = undefined;
    step.diagnosticMessage = undefined;
    step.span = undefined;
    step.nodeType = undefined;
  }

  validate(step: ReadonlyDetailStep): boolean {
    return (
      typeof step.line === 'number' &&
      step.line >= 0 &&
      typeof step.column === 'number' &&
      step.column >= 0 &&
      typeof step.increment === 'number' &&
      typeof step.cumulative === 'number' &&
      typeof step.ruleId === 'string' &&
      typeof step.description === 'string' &&
      typeof step.nestingLevel === 'number' &&
      step.nestingLevel >= 0 &&
      typeof step.context === 'string'
    );
  }
}

/**
 * 泛型对象池接口
 * 提供对象的重用机制以优化内存分配
 */
export interface ObjectPool<T> {
  /**
   * 从池中获取一个对象
   * @returns 池中的对象或新创建的对象
   */
  acquire(): T;

  /**
   * 将对象释放回池中
   * @param obj 要释放的对象
   */
  release(obj: T): void;

  /**
   * 获取池的当前大小
   * @returns 池中对象的数量
   */
  size(): number;

  /**
   * 清空对象池
   */
  clear(): void;

  /**
   * 获取对象池统计信息
   * @returns 包含池大小、创建数量等统计信息的对象
   */
  getStats(): ObjectPoolStats;
}

/**
 * 对象池统计信息
 */
export interface ObjectPoolStats {
  /** 池中当前对象数量 */
  poolSize: number;
  /** 池的最大容量 */
  maxSize: number;
  /** 总共创建的对象数量 */
  totalCreated: number;
  /** 总共重用的对象数量 */
  totalReused: number;
  /** 重用率百分比 */
  reuseRate: string;
}

/**
 * 工厂模式对象池接口
 * 结合工厂模式和对象池模式，提供类型安全的对象管理
 */
export interface FactoryObjectPool<TReadonly, TInternal> extends ObjectPool<TReadonly> {
  /**
   * 使用工厂创建对象
   * @returns 类型安全的只读对象
   */
  acquire(): TReadonly;

  /**
   * 使用工厂重置并释放对象
   * @param obj 要释放的对象
   */
  release(obj: TReadonly): void;

  /**
   * 获取工厂实例
   * @returns 关联的工厂实例
   */
  getFactory(): DetailStepFactory;

  /**
   * 预热对象池
   * 预先创建一定数量的对象以减少运行时分配
   * @param count 要预创建的对象数量
   */
  warmUp(count: number): void;

  /**
   * 验证池中对象的完整性
   * @returns 如果所有对象都有效则返回true
   */
  validatePool(): boolean;
}

/**
 * 对象池配置选项
 */
export interface ObjectPoolOptions {
  /** 池的最大容量 */
  maxSize?: number;
  /** 是否启用对象验证 */
  enableValidation?: boolean;
  /** 预热对象数量 */
  warmUpSize?: number;
  /** 工厂实现 */
  factory?: DetailStepFactory;
}

/**
 * 对象池错误类型
 */
export class ObjectPoolError extends Error {
  constructor(
    message: string,
    public readonly poolType: string,
    public readonly operation: string,
    public readonly context?: Record<string, unknown>
  ) {
    super(`ObjectPool Error [${poolType}:${operation}]: ${message}`);
    this.name = 'ObjectPoolError';
  }
}

/**
 * 只读函数上下文接口
 * 外部消费者只能访问只读属性
 */
export interface ReadonlyFunctionContext {
  readonly name: string;
  readonly line: number;
  readonly column: number;
  readonly complexity: number;
  readonly steps: readonly ReadonlyDetailStep[];
  readonly nestingLevel: number;
}

/**
 * 内部函数上下文接口
 * 内部实现可以修改属性
 */
export interface InternalFunctionContext {
  name: string;
  line: number;
  column: number;
  complexity: number;
  steps: InternalDetailStep[];
  nestingLevel: number;
}

/**
 * 函数上下文工厂接口
 */
export interface FunctionContextFactory {
  /**
   * 创建新的函数上下文对象
   */
  create(): ReadonlyFunctionContext;

  /**
   * 重置函数上下文对象
   */
  reset(context: InternalFunctionContext): void;

  /**
   * 验证函数上下文对象的有效性
   */
  validate(context: ReadonlyFunctionContext): boolean;
}