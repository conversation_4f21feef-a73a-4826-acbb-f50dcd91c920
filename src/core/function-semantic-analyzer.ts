import type { FunctionSemanticInfo, FunctionType } from './semantic-types';
import { CodeSemanticAnalyzer } from './code-semantic-analyzer';
import { PositionConverter } from '../utils/position-converter';

/**
 * 函数语义分析器 - 专门处理函数语法的语义分析和位置定位
 * 
 * 核心功能：
 * - 函数类型识别和分类
 * - 函数逻辑起始点精确定位
 * - 参数列表和函数体位置分析
 * - 箭头函数特殊处理
 */
export class FunctionSemanticAnalyzer {
  private readonly lines: string[];

  constructor(_sourceCode: string, _codeAnalyzer: CodeSemanticAnalyzer) {
    this.lines = _sourceCode.split('\n');
  }

  /**
   * 分析函数语义信息
   * @param node 函数节点
   * @returns 函数语义信息
   */
  analyzeFunctionSemantics(node: any): FunctionSemanticInfo {
    // 识别函数类型
    const functionType = this.identifyFunctionType(node);
    
    // 查找参数列表位置
    const parametersPosition = this.locateParameterList(node);
    
    // 查找函数体位置
    const bodyPosition = this.locateFunctionBody(node);
    
    // 查找逻辑起始位置
    const logicalStartPosition = this.findLogicalStartPosition(node, functionType);
    
    // 检查函数修饰符
    const isAsync = this.isAsyncFunction(node);
    const isGenerator = this.isGeneratorFunction(node);

    return {
      functionType,
      parametersPosition: parametersPosition || undefined,
      bodyPosition: bodyPosition || undefined,
      logicalStartPosition: logicalStartPosition || undefined,
      isAsync,
      isGenerator,
    };
  }

  /**
   * 查找箭头函数的逻辑起始点
   * @param node 箭头函数表达式节点
   * @returns 逻辑起始位置或null
   */
  findArrowFunctionLogicalStart(node: any): number | null {
    if (node.type !== 'ArrowFunctionExpression') {
      return null;
    }

    // 策略1: 如果有参数，定位到第一个参数
    if (node.params && node.params.length > 0) {
      const paramPosition = this.findParameterListStart(node.params);
      if (paramPosition !== null) {
        return paramPosition;
      }
    }

    // 策略2: 定位到箭头符号
    const arrowPosition = this.findArrowOperator(node);
    if (arrowPosition !== null) {
      return arrowPosition;
    }

    // 策略3: 如果是单行箭头函数，定位到函数体
    if (this.isSingleLineArrowFunction(node)) {
      return this.findFunctionBodyStart(node.body);
    }

    return null;
  }

  /**
   * 查找参数列表位置
   * @param node 函数节点
   * @returns 参数列表位置或null
   */
  locateParameterList(node: any): number | null {
    if (!node.params || !Array.isArray(node.params) || node.params.length === 0) {
      return null;
    }

    // 获取第一个参数的位置
    const firstParam = node.params[0];
    return this.getNodePosition(firstParam);
  }

  /**
   * 查找函数体位置
   * @param node 函数节点
   * @returns 函数体位置或null
   */
  locateFunctionBody(node: any): number | null {
    if (!node.body) {
      return null;
    }

    return this.getNodePosition(node.body);
  }

  /**
   * 识别函数类型
   * @param node 函数节点
   * @returns 函数类型
   */
  private identifyFunctionType(node: any): FunctionType {
    switch (node.type) {
      case 'ArrowFunctionExpression':
        return 'arrow-function';
      case 'FunctionExpression':
        return 'function-expression';
      case 'FunctionDeclaration':
        return 'function-declaration';
      case 'MethodDefinition':
        return 'method-definition';
      default:
        // 尝试从上下文推断
        return this.inferFunctionTypeFromContext(node);
    }
  }

  /**
   * 查找逻辑起始位置
   * @param node 函数节点
   * @param functionType 函数类型
   * @returns 逻辑起始位置或null
   */
  private findLogicalStartPosition(node: any, functionType: FunctionType): number | null {
    switch (functionType) {
      case 'arrow-function':
        return this.findArrowFunctionLogicalStart(node);
      case 'function-expression':
      case 'function-declaration':
        return this.findRegularFunctionLogicalStart(node);
      case 'method-definition':
        return this.findMethodLogicalStart(node);
      case 'constructor':
        return this.findConstructorLogicalStart(node);
      default:
        return this.getNodePosition(node);
    }
  }

  /**
   * 查找参数列表开始位置
   * @param params 参数数组
   * @returns 参数列表开始位置或null
   */
  private findParameterListStart(params: any[]): number | null {
    if (!params || params.length === 0) {
      return null;
    }

    const firstParam = params[0];
    return this.getNodePosition(firstParam);
  }

  /**
   * 查找箭头操作符位置
   * @param node 箭头函数节点
   * @returns 箭头位置或null
   */
  private findArrowOperator(node: any): number | null {
    const position = this.getNodePosition(node);
    if (position === null) {
      return null;
    }

    try {
      const lineColumn = PositionConverter.spanToPosition('', position);
      const lineIndex = lineColumn.line - 1;
      
      if (lineIndex >= 0 && lineIndex < this.lines.length) {
        const line = this.lines[lineIndex];
        
        if (!line) return position;
        
        // 查找 => 操作符
        const arrowMatch = line.match(/=>/);
        if (arrowMatch && arrowMatch.index !== undefined) {
          return PositionConverter.lineColumnToOffset(
            '',
            lineColumn.line,
            arrowMatch.index + 1
          );
        }
      }
    } catch (_error) {
      // 忽略转换错误
    }

    return null;
  }

  /**
   * 检查是否为单行箭头函数
   * @param node 箭头函数节点
   * @returns 是否为单行
   */
  private isSingleLineArrowFunction(node: any): boolean {
    if (!node.body) {
      return false;
    }

    // 如果函数体不是块语句，通常是单行箭头函数
    return node.body.type !== 'BlockStatement';
  }

  /**
   * 查找函数体开始位置
   * @param body 函数体节点
   * @returns 函数体开始位置或null
   */
  private findFunctionBodyStart(body: any): number | null {
    if (!body) {
      return null;
    }

    // 对于表达式体，直接返回表达式位置
    if (body.type !== 'BlockStatement') {
      return this.getNodePosition(body);
    }

    // 对于块语句，查找第一个有意义的语句
    if (body.body && Array.isArray(body.body) && body.body.length > 0) {
      const firstStatement = body.body[0];
      return this.getNodePosition(firstStatement);
    }

    return this.getNodePosition(body);
  }

  /**
   * 检查是否为异步函数
   * @param node 函数节点
   * @returns 是否为异步函数
   */
  private isAsyncFunction(node: any): boolean {
    return node.async === true;
  }

  /**
   * 检查是否为生成器函数
   * @param node 函数节点
   * @returns 是否为生成器函数
   */
  private isGeneratorFunction(node: any): boolean {
    return node.generator === true;
  }

  /**
   * 从上下文推断函数类型
   * @param node 函数节点
   * @returns 推断的函数类型
   */
  private inferFunctionTypeFromContext(node: any): FunctionType {
    // 检查是否在类定义中
    if (this.isInClassContext(node)) {
      if (this.isConstructorFunction(node)) {
        return 'constructor';
      }
      return 'method-definition';
    }

    // 检查是否为函数声明
    if (this.hasIdentifier(node)) {
      return 'function-declaration';
    }

    // 默认为函数表达式
    return 'function-expression';
  }

  /**
   * 查找普通函数的逻辑起始位置
   * @param node 函数节点
   * @returns 逻辑起始位置或null
   */
  private findRegularFunctionLogicalStart(node: any): number | null {
    // 策略1: 查找 function 关键字
    const functionKeywordPosition = this.findFunctionKeyword(node);
    if (functionKeywordPosition !== null) {
      return functionKeywordPosition;
    }

    // 策略2: 查找函数名或参数列表
    if (node.id) {
      return this.getNodePosition(node.id);
    }

    // 策略3: 查找参数列表
    return this.locateParameterList(node);
  }

  /**
   * 查找方法的逻辑起始位置
   * @param node 方法节点
   * @returns 逻辑起始位置或null
   */
  private findMethodLogicalStart(node: any): number | null {
    // 查找方法名
    if (node.key) {
      return this.getNodePosition(node.key);
    }

    return this.findRegularFunctionLogicalStart(node.value || node);
  }

  /**
   * 查找构造函数的逻辑起始位置
   * @param node 构造函数节点
   * @returns 逻辑起始位置或null
   */
  private findConstructorLogicalStart(node: any): number | null {
    // 查找 constructor 关键字
    const constructorPosition = this.findConstructorKeyword(node);
    if (constructorPosition !== null) {
      return constructorPosition;
    }

    return this.findMethodLogicalStart(node);
  }

  /**
   * 查找 function 关键字位置
   * @param node 函数节点
   * @returns function关键字位置或null
   */
  private findFunctionKeyword(node: any): number | null {
    return this.findKeywordInNode(node, 'function');
  }

  /**
   * 查找 constructor 关键字位置
   * @param node 构造函数节点
   * @returns constructor关键字位置或null
   */
  private findConstructorKeyword(node: any): number | null {
    return this.findKeywordInNode(node, 'constructor');
  }

  /**
   * 在节点对应的源代码中查找关键字
   * @param node 节点
   * @param keyword 关键字
   * @returns 关键字位置或null
   */
  private findKeywordInNode(node: any, keyword: string): number | null {
    const position = this.getNodePosition(node);
    if (position === null) {
      return null;
    }

    try {
      const lineColumn = PositionConverter.spanToPosition('', position);
      const lineIndex = lineColumn.line - 1;
      
      if (lineIndex >= 0 && lineIndex < this.lines.length) {
        const line = this.lines[lineIndex];
        
        if (!line) return null;
        
        // 查找关键字
        const keywordRegex = new RegExp(`\\b${keyword}\\b`);
        const keywordMatch = line.match(keywordRegex);
        if (keywordMatch && keywordMatch.index !== undefined) {
          return PositionConverter.lineColumnToOffset(
            '',
            lineColumn.line,
            keywordMatch.index + 1
          );
        }
      }
    } catch (_error) {
      // 忽略转换错误
    }

    return null;
  }

  /**
   * 检查是否在类上下文中
   * @param node 节点
   * @returns 是否在类中
   */
  private isInClassContext(node: any): boolean {
    // 简化实现：检查节点类型或属性
    return node.type === 'MethodDefinition' || 
           (node.kind && ['method', 'constructor', 'get', 'set'].includes(node.kind));
  }

  /**
   * 检查是否为构造函数
   * @param node 节点
   * @returns 是否为构造函数
   */
  private isConstructorFunction(node: any): boolean {
    return node.kind === 'constructor' || 
           (node.key && node.key.name === 'constructor');
  }

  /**
   * 检查节点是否有标识符
   * @param node 节点
   * @returns 是否有标识符
   */
  private hasIdentifier(node: any): boolean {
    return node.id && node.id.name;
  }

  /**
   * 获取节点位置
   * @param node 节点
   * @returns 位置或null
   */
  private getNodePosition(node: any): number | null {
    if (node && 'span' in node && node.span) {
      return node.span.start || null;
    }
    return null;
  }
}