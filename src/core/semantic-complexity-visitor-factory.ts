import type { AsyncRuleEngine } from '../engine/types';
import { DetailCollector } from './detail-collector';
import type { CalculationOptions } from './types';
import { SemanticComplexityVisitor } from './complexity-visitor-refactored';
import { CodeSemanticAnalyzer } from './code-semantic-analyzer';
import { SemanticPositionService } from './semantic-position-service';
import { IntelligentFallbackEngine } from './intelligent-fallback-engine';
import { JSXSemanticParser } from './jsx-semantic-parser';
import { FunctionSemanticAnalyzer } from './function-semantic-analyzer';
import type { SemanticPositioningOptions } from './semantic-types';

/**
 * 语义复杂度访问器工厂 - 提供便捷的创建方法
 * 
 * 功能：
 * - 简化 SemanticComplexityVisitor 的创建过程
 * - 提供预配置的语义服务组合
 * - 支持轻量级和完整功能两种模式
 * - 统一管理依赖注入
 */
export class SemanticComplexityVisitorFactory {
  
  /**
   * 创建轻量级语义复杂度访问器
   * 适用于快速分析和单次使用场景
   * 
   * @param sourceCode 源代码
   * @param options 计算选项
   * @returns 语义复杂度访问器实例
   */
  static createLightweight(
    sourceCode: string,
    options: CalculationOptions = {}
  ): SemanticComplexityVisitor {
    const semanticServices = this.createDefaultSemanticServices(sourceCode);
    
    return new SemanticComplexityVisitor(
      sourceCode,
      undefined, // 无详情收集器
      options,
      semanticServices
    );
  }

  /**
   * 创建完整功能的语义复杂度访问器
   * 适用于详细分析和生产环境使用
   * 
   * @param sourceCode 源代码
   * @param detailCollector 详情收集器
   * @param options 计算选项
   * @param asyncRuleEngine 异步规则引擎
   * @returns 语义复杂度访问器实例
   */
  static createComplete(
    sourceCode: string,
    detailCollector?: DetailCollector,
    options: CalculationOptions = {},
    asyncRuleEngine?: AsyncRuleEngine
  ): SemanticComplexityVisitor {
    const semanticServices = this.createDefaultSemanticServices(sourceCode);
    
    return new SemanticComplexityVisitor(
      sourceCode,
      detailCollector,
      options,
      semanticServices,
      asyncRuleEngine
    );
  }

  /**
   * 创建自定义配置的语义复杂度访问器
   * 支持精细化的语义选项控制
   * 
   * @param sourceCode 源代码
   * @param semanticOptions 语义选项
   * @param calculationOptions 计算选项
   * @param detailCollector 详情收集器
   * @param asyncRuleEngine 异步规则引擎
   * @returns 语义复杂度访问器实例
   */
  static createCustom(
    sourceCode: string,
    semanticOptions: SemanticPositioningOptions,
    calculationOptions: CalculationOptions = {},
    detailCollector?: DetailCollector,
    asyncRuleEngine?: AsyncRuleEngine
  ): SemanticComplexityVisitor {
    const semanticServices = this.createCustomSemanticServices(sourceCode, semanticOptions);
    
    return new SemanticComplexityVisitor(
      sourceCode,
      detailCollector,
      calculationOptions,
      semanticServices,
      asyncRuleEngine
    );
  }

  /**
   * 创建JSX优化的语义复杂度访问器
   * 针对React/JSX代码的特殊优化
   * 
   * @param sourceCode 源代码
   * @param options 计算选项
   * @param detailCollector 详情收集器
   * @returns 语义复杂度访问器实例
   */
  static createForJSX(
    sourceCode: string,
    options: CalculationOptions = {},
    detailCollector?: DetailCollector
  ): SemanticComplexityVisitor {
    const semanticOptions: SemanticPositioningOptions = {
      enableJSXSemantics: true,
      enableFunctionSemantics: true,
      enableSmartLineFiltering: true,
      maxFallbackLevels: 5,
      debugMode: false,
    };

    return this.createCustom(sourceCode, semanticOptions, options, detailCollector);
  }

  /**
   * 创建TypeScript优化的语义复杂度访问器
   * 针对TypeScript代码的特殊处理
   * 
   * @param sourceCode 源代码
   * @param options 计算选项
   * @param detailCollector 详情收集器
   * @returns 语义复杂度访问器实例
   */
  static createForTypeScript(
    sourceCode: string,
    options: CalculationOptions = {},
    detailCollector?: DetailCollector
  ): SemanticComplexityVisitor {
    const semanticOptions: SemanticPositioningOptions = {
      enableJSXSemantics: false,
      enableFunctionSemantics: true,
      enableSmartLineFiltering: true,
      maxFallbackLevels: 3,
      debugMode: false,
    };

    return this.createCustom(sourceCode, semanticOptions, options, detailCollector);
  }

  /**
   * 创建调试模式的语义复杂度访问器
   * 启用所有调试功能，用于开发和问题排查
   * 
   * @param sourceCode 源代码
   * @param options 计算选项
   * @param detailCollector 详情收集器
   * @returns 语义复杂度访问器实例
   */
  static createDebugMode(
    sourceCode: string,
    options: CalculationOptions = {},
    detailCollector?: DetailCollector
  ): SemanticComplexityVisitor {
    const semanticOptions: SemanticPositioningOptions = {
      enableJSXSemantics: true,
      enableFunctionSemantics: true,
      enableSmartLineFiltering: true,
      maxFallbackLevels: 10,
      debugMode: true,
    };

    return this.createCustom(sourceCode, semanticOptions, options, detailCollector);
  }

  /**
   * 创建默认语义服务
   * @param sourceCode 源代码
   * @returns 语义服务集合
   */
  private static createDefaultSemanticServices(sourceCode: string) {
    const options: SemanticPositioningOptions = {
      enableJSXSemantics: true,
      enableFunctionSemantics: true,
      enableSmartLineFiltering: true,
      maxFallbackLevels: 5,
      debugMode: false,
    };

    return this.createCustomSemanticServices(sourceCode, options);
  }

  /**
   * 创建自定义语义服务
   * @param sourceCode 源代码
   * @param options 语义选项
   * @returns 语义服务集合
   */
  private static createCustomSemanticServices(
    sourceCode: string, 
    options: SemanticPositioningOptions
  ) {
    // 创建基础分析器
    const codeAnalyzer = new CodeSemanticAnalyzer(sourceCode);
    
    // 创建专门分析器
    const jsxParser = new JSXSemanticParser(sourceCode, codeAnalyzer);
    const functionAnalyzer = new FunctionSemanticAnalyzer(sourceCode, codeAnalyzer);
    
    // 创建位置服务
    const semanticPosition = new SemanticPositionService(sourceCode, codeAnalyzer, options);
    
    // 创建智能回退引擎
    const fallbackEngine = new IntelligentFallbackEngine(semanticPosition, codeAnalyzer, sourceCode);

    return {
      semanticPosition,
      fallbackEngine,
      jsxParser,
      functionAnalyzer,
      codeAnalyzer,
    };
  }
}

/**
 * 便捷创建函数 - 兼容原有API
 */

/**
 * 创建语义复杂度访问器 - 默认配置
 * @param sourceCode 源代码
 * @param options 选项
 * @returns 访问器实例
 */
export function createSemanticComplexityVisitor(
  sourceCode: string,
  options: CalculationOptions = {}
): SemanticComplexityVisitor {
  return SemanticComplexityVisitorFactory.createLightweight(sourceCode, options);
}

/**
 * 创建带详情收集的语义复杂度访问器
 * @param sourceCode 源代码
 * @param detailCollector 详情收集器
 * @param options 选项
 * @returns 访问器实例
 */
export function createSemanticComplexityVisitorWithDetails(
  sourceCode: string,
  detailCollector: DetailCollector,
  options: CalculationOptions = {}
): SemanticComplexityVisitor {
  return SemanticComplexityVisitorFactory.createComplete(sourceCode, detailCollector, options);
}