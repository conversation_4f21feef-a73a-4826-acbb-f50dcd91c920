/**
 * Token查找器
 * 
 * 实现基于SWC Token流的精确关键字定位系统。
 * 支持三级降级策略：Token分析 → 模式匹配 → indexOf查找。
 */

import type { Node } from '@swc/core';
import type { TokenSearchRange } from '../types';
import { SearchStrategies } from './search-strategies';

/**
 * Token查找器类
 * 
 * 提供精确的关键字和符号定位功能。
 * 使用多种策略确保在各种情况下都能找到正确的位置。
 */
export class TokenSearcher {
  /**
   * 源代码内容
   */
  private readonly sourceCode: string;

  /**
   * 搜索策略实例
   */
  private readonly searchStrategies: SearchStrategies;

  /**
   * 构造函数
   * 
   * @param sourceCode 源代码内容
   */
  constructor(sourceCode: string) {
    this.sourceCode = sourceCode;
    this.searchStrategies = new SearchStrategies(sourceCode);
  }

  /**
   * SWC Token 查找系统 - 精确定位关键字位置
   * 
   * 实现三级降级策略：
   * 1. SWC AST Token 查找（最精确）
   * 2. 字符串模式匹配（备用）
   * 3. indexOf 查找（兜底）
   * 
   * @param node AST 节点
   * @param keyword 要查找的关键字
   * @returns 关键字的字节偏移位置，如果未找到则返回 null
   */
  public findKeywordPosition(node: any, keyword: string): number | null {
    // 获取节点的搜索范围（如果有有效span则限制搜索范围）
    const searchRange = this.getSearchRange(node);
    
    try {
      // 策略1: SWC AST Token 查找
      const tokenPosition = this.searchStrategies.findKeywordByTokenAnalysis(keyword, searchRange);
      if (tokenPosition !== null) {
        this.recordTokenSearchResult(node, keyword, tokenPosition, 'token-analysis');
        return tokenPosition;
      }

      // 策略2: 智能字符串模式匹配
      const patternPosition = this.searchStrategies.findKeywordByPatternMatching(keyword, searchRange);
      if (patternPosition !== null) {
        this.recordTokenSearchResult(node, keyword, patternPosition, 'pattern-matching');
        return patternPosition;
      }

      // 策略3: 简单 indexOf 查找（兜底策略）
      const indexPosition = this.searchStrategies.findKeywordByIndexOf(keyword, searchRange);
      if (indexPosition !== null) {
        this.recordTokenSearchResult(node, keyword, indexPosition, 'index-fallback');
        return indexPosition;
      }

      // 所有策略都失败
      this.recordTokenSearchResult(node, keyword, null, 'all-failed');
      return null;

    } catch (error) {
      this.recordTokenSearchError(node, keyword, error as Error);
      return null;
    }
  }

  /**
   * 获取节点的搜索范围
   * 
   * @param node AST 节点
   * @returns 搜索范围 { start, end }
   */
  private getSearchRange(node: any): { start: number; end: number } {
    // 如果节点有有效的 span，使用它来限制搜索范围
    if (this.isValidSpan(node)) {
      const span = node.span;
      // 扩展搜索范围以包含可能的前置空白和关键字
      const expandedStart = Math.max(0, span.start - 50);
      const expandedEnd = Math.min(this.sourceCode.length, span.end + 10);
      return { start: expandedStart, end: expandedEnd };
    }

    // 最后回退到整个源代码
    return { start: 0, end: this.sourceCode.length };
  }

  /**
   * 检查 span 是否有效
   * 
   * @param node 要检查的节点
   * @returns 是否有效
   */
  private isValidSpan(node: any): boolean {
    return node.span && 
           typeof node.span.start === 'number' && 
           typeof node.span.end === 'number' &&
           node.span.start >= 0 && 
           node.span.end >= node.span.start &&
           node.span.start < this.sourceCode.length;
  }

  /**
   * 在指定范围内查找关键字
   * 
   * @param keyword 要查找的关键字
   * @param range 搜索范围
   * @returns 关键字位置，如果未找到则返回 null
   */
  public findKeywordInRange(keyword: string, range: TokenSearchRange): number | null {
    return this.searchStrategies.findKeywordByPatternMatching(keyword, range);
  }

  /**
   * 查找箭头函数中的箭头操作符 '=>' 位置
   * 
   * @param node 箭头函数节点
   * @returns 箭头操作符位置
   */
  public findArrowOperatorPosition(node: any): number | null {
    if (!node || node.type !== 'ArrowFunctionExpression') {
      return null;
    }

    // 确定搜索范围
    const searchRange = this.getArrowFunctionSearchRange(node);
    if (!searchRange) {
      return null;
    }

    // 在范围内搜索箭头符号
    const searchText = this.sourceCode.slice(searchRange.start, searchRange.end);
    
    // 使用正则表达式查找箭头符号，避免误匹配
    const arrowRegex = /\s*=>\s*/g;
    let match;
    const candidates: number[] = [];
    
    while ((match = arrowRegex.exec(searchText)) !== null) {
      const absolutePosition = searchRange.start + match.index + match[0].indexOf('=>');
      candidates.push(absolutePosition);
    }
    
    // 返回第一个匹配的箭头符号位置
    return candidates.length > 0 ? candidates[0] : null;
  }

  /**
   * 确定箭头函数的搜索范围
   * 基于参数列表和函数体的位置
   * 
   * @param node 箭头函数节点
   * @returns 搜索范围
   */
  private getArrowFunctionSearchRange(node: any): { start: number; end: number } | null {
    let searchStart = 0;
    let searchEnd = this.sourceCode.length;

    // 确定搜索起始位置
    if (node.params && node.params.length > 0) {
      // 从最后一个参数结束后开始
      const lastParam = node.params[node.params.length - 1];
      if (lastParam && lastParam.span && typeof lastParam.span.end === 'number') {
        searchStart = lastParam.span.end;
      }
    } else if (node.span && typeof node.span.start === 'number') {
      // 无参数情况，从函数开始
      searchStart = node.span.start;
    }

    // 确定搜索结束位置
    if (node.body && node.body.span && typeof node.body.span.start === 'number') {
      searchEnd = node.body.span.start;
    } else if (node.span && typeof node.span.end === 'number') {
      searchEnd = node.span.end;
    }

    // 验证搜索范围的有效性
    if (searchStart >= searchEnd || searchStart < 0 || searchEnd > this.sourceCode.length) {
      return null;
    }

    return { start: searchStart, end: searchEnd };
  }

  /**
   * 查找三元运算符中的 '?' 位置
   * 
   * @param node 条件表达式节点
   * @returns '?' 符号位置
   */
  public findTernaryOperatorPosition(node: any): number | null {
    if (!node || node.type !== 'ConditionalExpression') {
      return null;
    }

    // 确定搜索范围
    const searchRange = this.getTernarySearchRange(node);
    if (!searchRange) {
      return null;
    }

    // 在范围内搜索 '?' 符号
    const searchText = this.sourceCode.slice(searchRange.start, searchRange.end);
    
    // 使用正则表达式查找 '?' 符号，避免误匹配
    const questionRegex = /\s*\?\s*/g;
    const match = questionRegex.exec(searchText);
    
    if (match) {
      const questionIndex = match.index + match[0].indexOf('?');
      return searchRange.start + questionIndex;
    }
    
    return null;
  }

  /**
   * 确定三元运算符的搜索范围
   * 基于测试条件和真值表达式的位置
   * 
   * @param node 条件表达式节点
   * @returns 搜索范围
   */
  private getTernarySearchRange(node: any): { start: number; end: number } | null {
    let searchStart = 0;
    let searchEnd = this.sourceCode.length;

    // 确定搜索起始位置：从测试条件结束后开始
    if (node.test && node.test.span && typeof node.test.span.end === 'number') {
      searchStart = node.test.span.end;
    } else if (node.span && typeof node.span.start === 'number') {
      searchStart = node.span.start;
    }

    // 确定搜索结束位置：到真值表达式开始前结束
    if (node.consequent && node.consequent.span && typeof node.consequent.span.start === 'number') {
      searchEnd = node.consequent.span.start;
    } else if (node.span && typeof node.span.end === 'number') {
      searchEnd = node.span.end;
    }

    // 验证搜索范围的有效性
    if (searchStart >= searchEnd || searchStart < 0 || searchEnd > this.sourceCode.length) {
      return null;
    }

    return { start: searchStart, end: searchEnd };
  }

  /**
   * 记录Token搜索结果（简化版本）
   * 
   * @param node 节点
   * @param keyword 关键字
   * @param position 找到的位置
   * @param method 使用的方法
   */
  private recordTokenSearchResult(node: any, keyword: string, position: number | null, method: string): void {
    // 简化实现，实际会与DetailCollector集成
    const status = position !== null ? 'success' : 'failed';
    console.debug(`Token search ${status}: '${keyword}' in ${node.type} using ${method}${position !== null ? ` (position: ${position})` : ''}`);
  }

  /**
   * 记录Token搜索错误（简化版本）
   * 
   * @param node 节点
   * @param keyword 关键字
   * @param error 错误信息
   */
  private recordTokenSearchError(node: any, keyword: string, error: Error): void {
    // 简化实现，实际会与DetailCollector集成
    console.warn(`Token search error for '${keyword}' in ${node.type}:`, error.message);
  }
}
