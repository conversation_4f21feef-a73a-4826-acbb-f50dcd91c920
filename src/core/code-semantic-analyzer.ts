import type {
  SemanticContext,
  LineSemanticInfo,
  LineContentType,
  LogicalElement,
  SyntaxHint,
} from './semantic-types';

/**
 * 代码语义分析器 - 提供基础的代码语义理解能力
 * 
 * 核心功能：
 * - 分析代码行的语义特征
 * - 识别逻辑上有意义的代码内容
 * - 分类代码元素并评估其重要性
 * - 提供语法模式识别和提示
 */
export class CodeSemanticAnalyzer {
  private readonly sourceCode: string;
  private readonly lines: string[];

  constructor(sourceCode: string) {
    this.sourceCode = sourceCode;
    this.lines = sourceCode.split('\n');
  }

  /**
   * 分析行语义信息
   * @param lineContent 行内容
   * @param lineNumber 行号（0开始）
   * @returns 行语义信息
   */
  analyzeLineSemantics(lineContent: string, lineNumber?: number): LineSemanticInfo {
    const trimmed = lineContent.trim();
    
    // 基础内容类型分类
    const contentType = this.classifyLineContent(trimmed);
    
    // 逻辑重要性判断
    const isLogicallySignificant = this.isLogicallySignificant(trimmed, contentType);
    
    // 提取逻辑元素
    const logicalElements = isLogicallySignificant 
      ? this.extractLogicalElements(trimmed)
      : [];
    
    // 语法提示
    const syntaxHints = this.generateSyntaxHints(trimmed, lineNumber);

    return {
      isLogicallySignificant,
      contentType,
      logicalElements,
      syntaxHints,
    };
  }

  /**
   * 判断内容是否逻辑上有意义
   * @param content 内容
   * @param context 语义上下文
   * @returns 是否有意义
   */
  isLogicallySignificant(content: string, context?: SemanticContext | LineContentType): boolean {
    // 空行或纯空白
    if (!content || /^\s*$/.test(content)) {
      return false;
    }

    // 纯注释行
    if (/^\s*\/\//.test(content) || /^\s*\/\*/.test(content)) {
      return false;
    }

    // 只有结构性符号的行（括号、分号等）
    if (/^\s*[{}();,]\s*$/.test(content)) {
      return false;
    }

    // 只有导入/导出结构的行
    if (/^\s*(import|export)\s*[{}]*\s*$/.test(content)) {
      return false;
    }

    // 根据上下文进行特殊判断
    if (typeof context === 'object' && context?.codePattern) {
      return this.isSignificantForPattern(content, context.codePattern);
    }

    return true;
  }

  /**
   * 寻找最近的逻辑代码行
   * @param position 当前位置（行号）
   * @param direction 查找方向
   * @param maxDistance 最大查找距离
   * @returns 找到的位置，null表示未找到
   */
  findNearestLogicalCode(
    position: number, 
    direction: 'up' | 'down', 
    maxDistance: number = 5
  ): number | null {
    const step = direction === 'up' ? -1 : 1;
    const startLine = position + step;
    
    for (let i = 0; i < maxDistance; i++) {
      const lineIndex = startLine + (i * step);
      
      if (lineIndex < 0 || lineIndex >= this.lines.length) {
        break;
      }
      
      const line = this.lines[lineIndex];
      if (line && this.isLogicallySignificant(line)) {
        return lineIndex;
      }
    }
    
    return null;
  }

  /**
   * 获取行内容的语义重要性分数
   * @param lineContent 行内容
   * @returns 重要性分数 (0-1)
   */
  getSemanticImportanceScore(lineContent: string): number {
    const trimmed = lineContent.trim();
    
    if (!this.isLogicallySignificant(trimmed)) {
      return 0;
    }

    let score = 0.5; // 基础分数

    // 关键字加分
    const keywords = ['if', 'else', 'for', 'while', 'switch', 'case', 'function', 'const', 'let', 'var'];
    for (const keyword of keywords) {
      if (new RegExp(`\\b${keyword}\\b`).test(trimmed)) {
        score += 0.1;
      }
    }

    // 复杂操作符加分
    if (/&&|\|\||[?:]/.test(trimmed)) {
      score += 0.15;
    }

    // JSX 结构加分
    if (/<[A-Z]/.test(trimmed) || /{.*}/.test(trimmed)) {
      score += 0.1;
    }

    // 函数调用加分
    if (/\w+\s*\(/.test(trimmed)) {
      score += 0.1;
    }

    return Math.min(score, 1);
  }

  /**
   * 分类行内容类型
   * @param content 内容
   * @returns 内容类型
   */
  private classifyLineContent(content: string): LineContentType {
    if (!content) {
      return 'whitespace-only';
    }

    const hasCode = /[a-zA-Z0-9_$]/.test(content);
    const hasComment = /\/\/|\/\*|\*\//.test(content);
    const isStructuralOnly = /^[\s{}();,]*$/.test(content);

    if (isStructuralOnly) {
      return 'structural-only';
    }

    if (hasCode && hasComment) {
      return 'code-with-comment';
    }

    if (hasComment && !hasCode) {
      return 'pure-comment';
    }

    if (hasCode) {
      return 'pure-code';
    }

    return 'mixed-content';
  }

  /**
   * 提取逻辑元素
   * @param content 内容
   * @returns 逻辑元素数组
   */
  private extractLogicalElements(content: string): LogicalElement[] {
    const elements: LogicalElement[] = [];
    let position = 0;

    // 关键字模式
    const keywordPatterns = [
      { pattern: /\b(if|else|for|while|switch|case|function|return|throw|try|catch)\b/g, type: 'keyword' as const, importance: 'critical' as const },
      { pattern: /\b(const|let|var|class|interface|type|enum)\b/g, type: 'keyword' as const, importance: 'important' as const },
    ];

    // 操作符模式
    const operatorPatterns = [
      { pattern: /&&|\|\|/g, type: 'operator' as const, importance: 'critical' as const },
      { pattern: /[?:]/g, type: 'operator' as const, importance: 'critical' as const },
      { pattern: /[+\-*/%=<>!]/g, type: 'operator' as const, importance: 'minor' as const },
    ];

    // 标识符模式
    const identifierPattern = { pattern: /\b[a-zA-Z_$][a-zA-Z0-9_$]*\b/g, type: 'identifier' as const, importance: 'important' as const };

    // 字面量模式
    const literalPatterns = [
      { pattern: /"[^"]*"|'[^']*'|`[^`]*`/g, type: 'literal' as const, importance: 'minor' as const },
      { pattern: /\b\d+(\.\d+)?\b/g, type: 'literal' as const, importance: 'minor' as const },
    ];

    // 提取所有模式
    const allPatterns = [...keywordPatterns, ...operatorPatterns, identifierPattern, ...literalPatterns];

    for (const { pattern, type, importance } of allPatterns) {
      let match;
      pattern.lastIndex = 0; // 重置正则表达式状态
      
      while ((match = pattern.exec(content)) !== null) {
        elements.push({
          type,
          value: match[0],
          importance,
          position: {
            start: match.index,
            end: match.index + match[0].length,
          },
        });
      }
    }

    // 按位置排序
    elements.sort((a, b) => a.position.start - b.position.start);

    return elements;
  }

  /**
   * 生成语法提示
   * @param content 内容
   * @param _lineNumber 行号
   * @returns 语法提示数组
   */
  private generateSyntaxHints(content: string, _lineNumber?: number): SyntaxHint[] {
    const hints: SyntaxHint[] = [];

    // JSX 模式检测
    if (/<[A-Z]/.test(content) || /<\//.test(content)) {
      hints.push({
        type: 'jsx-pattern',
        confidence: 0.9,
        metadata: { hasJSX: true },
      });
    }

    // 箭头函数检测
    if (/=\s*>/.test(content) || /=>\s*{/.test(content)) {
      hints.push({
        type: 'arrow-function',
        confidence: 0.85,
        metadata: { hasArrowFunction: true },
      });
    }

    // TypeScript 类型注解检测
    if (/:\s*[a-zA-Z_$][a-zA-Z0-9_$<>[\]|&]*/.test(content)) {
      hints.push({
        type: 'type-annotation',
        confidence: 0.8,
        metadata: { hasTypeScript: true },
      });
    }

    // 解构赋值检测
    if (/const\s*{|let\s*{|var\s*{/.test(content) || /\[.*\]\s*=/.test(content)) {
      hints.push({
        type: 'destructuring',
        confidence: 0.75,
        metadata: { hasDestructuring: true },
      });
    }

    return hints;
  }

  /**
   * 判断内容对特定模式是否有意义
   * @param content 内容
   * @param pattern 代码模式
   * @returns 是否有意义
   */
  private isSignificantForPattern(content: string, pattern: string): boolean {
    switch (pattern) {
      case 'jsx':
        return this.hasJSXLogicalContent(content);
      case 'function':
        return this.hasFunctionLogicalContent(content);
      case 'control-flow':
        return this.hasControlFlowContent(content);
      default:
        return true;
    }
  }

  /**
   * 检查是否包含 JSX 逻辑内容
   * @param content 内容
   * @returns 是否包含
   */
  private hasJSXLogicalContent(content: string): boolean {
    // JSX 表达式内容 {expression}
    if (/{[^}]*[a-zA-Z0-9_$()[\].+\-*/%<>=!&|?:][^}]*}/.test(content)) {
      return true;
    }
    
    // 排除纯结构性 JSX
    if (/^<\/[A-Z]/.test(content.trim()) || /^<[A-Z][^>]*\/>\s*$/.test(content.trim())) {
      return false;
    }
    
    return true;
  }

  /**
   * 检查是否包含函数逻辑内容
   * @param content 内容
   * @returns 是否包含
   */
  private hasFunctionLogicalContent(content: string): boolean {
    // 函数参数或返回语句
    if (/\breturn\b/.test(content) || /\bthrow\b/.test(content)) {
      return true;
    }
    
    // 排除纯声明行
    if (/^(function|const|let|var)\s+[a-zA-Z_$][\w$]*\s*[=:]?\s*$/.test(content.trim())) {
      return false;
    }
    
    return true;
  }

  /**
   * 检查是否包含控制流内容
   * @param content 内容
   * @returns 是否包含
   */
  private hasControlFlowContent(content: string): boolean {
    // 包含条件表达式或控制关键字
    return /\b(if|else|for|while|switch|case|break|continue)\b/.test(content);
  }
}