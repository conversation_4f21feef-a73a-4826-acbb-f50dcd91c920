import type { Node } from '@swc/core';
import type { JSXSemanticInfo } from './semantic-types';
import { CodeSemanticAnalyzer } from './code-semantic-analyzer';
import { PositionConverter } from '../utils/position-converter';

/**
 * JSX语义解析器 - 专门处理JSX语法的语义分析和位置定位
 * 
 * 核心功能：
 * - JSX元素结构分析
 * - JSX表达式内容精确定位
 * - 区分JSX结构和逻辑内容
 * - JSX复杂度评估
 */
export class JSXSemanticParser {
  private readonly lines: string[];

  constructor(_sourceCode: string, _codeAnalyzer: CodeSemanticAnalyzer) {
    this.lines = _sourceCode.split('\n');
  }

  /**
   * 解析JSX元素的语义信息
   * @param node JSX元素节点
   * @returns JSX语义信息
   */
  parseJSXElement(node: any): JSXSemanticInfo {
    const nodeType = node.type;
    
    // 确定JSX元素类型
    let elementType: JSXSemanticInfo['elementType'] = 'opening-tag';
    if (nodeType === 'JSXClosingElement') {
      elementType = 'closing-tag';
    } else if (this.isSelfClosingElement(node)) {
      elementType = 'self-closing';
    } else if (nodeType === 'JSXExpressionContainer') {
      elementType = 'expression';
    }

    // 检查是否包含逻辑内容
    const hasLogicalContent = this.hasLogicalContent(node);
    
    // 查找表达式位置
    const expressionPosition = this.findExpressionPosition(node);
    
    // 计算逻辑复杂度贡献
    const logicalComplexity = this.calculateLogicalComplexity(node);

    return {
      elementType,
      hasLogicalContent,
      expressionPosition,
      logicalComplexity,
    };
  }

  /**
   * 查找JSX表达式容器中的实际内容位置
   * @param node JSX表达式容器节点
   * @returns 内容位置或null
   */
  findJSXExpressionContent(node: any): number | null {
    if (node.type !== 'JSXExpressionContainer') {
      return null;
    }

    const expression = node.expression;
    if (!expression) {
      return null;
    }

    // 优先定位到实际的逻辑代码，而非JSX结构
    if (this.isConditionalLogic(expression)) {
      return this.findConditionalStart(expression);
    }

    if (this.isFunctionCall(expression)) {
      return this.findFunctionCallStart(expression);
    }

    if (this.isLogicalExpression(expression)) {
      return this.findLogicalExpressionStart(expression);
    }

    // 回退到表达式开始
    return this.getNodePosition(expression);
  }

  /**
   * 定位JSX标签位置
   * @param node JSX元素节点
   * @returns 标签位置或null
   */
  locateJSXTagPosition(node: any): number | null {
    if (!this.isJSXElement(node)) {
      return null;
    }

    // 查找开始标签位置
    const tagPosition = this.findJSXOpeningTag(node);
    if (tagPosition !== null) {
      return tagPosition;
    }

    // 回退到节点开始位置
    return this.getNodePosition(node);
  }

  /**
   * 区分JSX结构和逻辑内容
   * @param node 节点
   * @returns 内容类型
   */
  distinguishJSXFromLogic(node: Node): 'jsx-structure' | 'logic-content' {
    const nodeType = node.type;

    // JSX表达式容器通常包含逻辑
    if (nodeType === 'JSXExpressionContainer') {
      return 'logic-content';
    }

    // JSX元素本身是结构
    if (nodeType === 'JSXElement' || nodeType === 'JSXFragment') {
      return 'jsx-structure';
    }

    // 检查节点内容
    if (this.containsLogicalOperations(node)) {
      return 'logic-content';
    }

    return 'jsx-structure';
  }

  /**
   * 查找JSX开始标签在源代码中的位置
   * @param node JSX节点
   * @returns 位置或null
   */
  findJSXOpeningTag(node: any): number | null {
    const position = this.getNodePosition(node);
    if (position === null) {
      return null;
    }

    try {
      const lineColumn = PositionConverter.spanToPosition('', position); // sourceCode为空，简化实现
      const lineIndex = lineColumn.line - 1;
      
      if (lineIndex >= 0 && lineIndex < this.lines.length) {
        const line = this.lines[lineIndex];
        
        if (!line) return null;
        
        // 查找 < 符号开始的标签
        const tagMatch = line.match(/<[A-Za-z][A-Za-z0-9]*|<>/);
        if (tagMatch && tagMatch.index !== undefined) {
          return PositionConverter.lineColumnToOffset('', lineColumn.line, tagMatch.index + 1);
        }
      }
    } catch (_error) {
      // 忽略转换错误
    }

    return null;
  }

  /**
   * 检查是否为自闭合元素
   * @param node JSX节点
   * @returns 是否自闭合
   */
  private isSelfClosingElement(node: any): boolean {
    // 检查节点是否有 selfClosing 属性
    if (node.selfClosing === true) {
      return true;
    }

    // 检查源代码中是否以 /> 结尾
    const position = this.getNodePosition(node);
    if (position !== null) {
      try {
        const lineColumn = PositionConverter.spanToPosition('', position);
        const lineIndex = lineColumn.line - 1;
        
        if (lineIndex >= 0 && lineIndex < this.lines.length) {
          const line = this.lines[lineIndex];
          return line ? /\/>/.test(line) : false;
        }
      } catch (_error) {
        // 忽略错误
      }
    }

    return false;
  }

  /**
   * 检查节点是否包含逻辑内容
   * @param node 节点
   * @returns 是否包含逻辑内容
   */
  private hasLogicalContent(node: any): boolean {
    // JSX表达式容器通常包含逻辑
    if (node.type === 'JSXExpressionContainer') {
      return true;
    }

    // 检查子节点
    if (node.children && Array.isArray(node.children)) {
      return node.children.some((child: any) => this.hasLogicalContent(child));
    }

    // 检查属性中的表达式
    if (node.attributes && Array.isArray(node.attributes)) {
      return node.attributes.some((attr: any) => 
        attr.type === 'JSXExpressionContainer' || 
        (attr.value && attr.value.type === 'JSXExpressionContainer')
      );
    }

    return false;
  }

  /**
   * 查找表达式位置
   * @param node 节点
   * @returns 表达式位置或undefined
   */
  private findExpressionPosition(node: any): number | undefined {
    if (node.type === 'JSXExpressionContainer') {
      return this.findJSXExpressionContent(node) || undefined;
    }

    // 查找子节点中的表达式
    if (node.children && Array.isArray(node.children)) {
      for (const child of node.children) {
        const position = this.findExpressionPosition(child);
        if (position !== undefined) {
          return position;
        }
      }
    }

    return undefined;
  }

  /**
   * 计算逻辑复杂度贡献
   * @param node 节点
   * @returns 复杂度分数
   */
  private calculateLogicalComplexity(node: any): number {
    let complexity = 0;

    // JSX表达式容器中的复杂逻辑
    if (node.type === 'JSXExpressionContainer') {
      complexity += this.calculateExpressionComplexity(node.expression);
    }

    // 递归计算子节点复杂度
    if (node.children && Array.isArray(node.children)) {
      for (const child of node.children) {
        complexity += this.calculateLogicalComplexity(child);
      }
    }

    // 计算属性中的复杂度
    if (node.attributes && Array.isArray(node.attributes)) {
      for (const attr of node.attributes) {
        if (attr.value && attr.value.type === 'JSXExpressionContainer') {
          complexity += this.calculateExpressionComplexity(attr.value.expression);
        }
      }
    }

    return complexity;
  }

  /**
   * 计算表达式复杂度
   * @param expression 表达式节点
   * @returns 复杂度分数
   */
  private calculateExpressionComplexity(expression: any): number {
    if (!expression) {
      return 0;
    }

    let complexity = 0;

    switch (expression.type) {
      case 'ConditionalExpression':
        complexity += 1; // 三元操作符
        break;
      
      case 'LogicalExpression':
        complexity += 1; // 逻辑操作符
        break;
      
      case 'BinaryExpression':
        if (['&&', '||'].includes(expression.operator)) {
          complexity += 1; // 逻辑二元操作符
        }
        break;
      
      case 'CallExpression':
        // 简单的函数调用（如翻译函数）不增加复杂度
        // 只有复杂的函数调用才增加复杂度
        if (this.isComplexFunctionCall(expression)) {
          complexity += 0.5;
        }
        break;
      
      // 简单的标识符、字面量等不增加复杂度
      case 'Identifier':
      case 'StringLiteral':
      case 'NumericLiteral':
      case 'BooleanLiteral':
      case 'NullLiteral':
        return 0;
      
      // 简单的属性访问也不增加复杂度
      case 'MemberExpression':
        if (!this.isComplexMemberExpression(expression)) {
          return 0;
        }
        complexity += 0.5;
        break;
    }

    // 递归计算子表达式
    if (expression.left) {
      complexity += this.calculateExpressionComplexity(expression.left);
    }
    if (expression.right) {
      complexity += this.calculateExpressionComplexity(expression.right);
    }
    if (expression.test) {
      complexity += this.calculateExpressionComplexity(expression.test);
    }
    if (expression.consequent) {
      complexity += this.calculateExpressionComplexity(expression.consequent);
    }
    if (expression.alternate) {
      complexity += this.calculateExpressionComplexity(expression.alternate);
    }

    return complexity;
  }

  /**
   * 检查是否为复杂函数调用
   * @param expression 函数调用表达式
   * @returns 是否为复杂函数调用
   */
  private isComplexFunctionCall(expression: any): boolean {
    if (!expression || expression.type !== 'CallExpression') {
      return false;
    }

    // 简单的翻译函数调用不算复杂
    if (this.isTranslationFunction(expression)) {
      return false;
    }

    // 简单的状态setter调用不算复杂
    if (this.isSimpleStateSetterCall(expression)) {
      return false;
    }

    // 有多个参数且参数复杂的函数调用算复杂
    const args = expression.arguments || [];
    if (args.length > 2) {
      return true;
    }

    // 参数中包含复杂表达式的函数调用算复杂
    for (const arg of args) {
      if (this.isComplexArgument(arg)) {
        return true;
      }
    }

    return false;
  }

  /**
   * 检查是否为复杂成员表达式
   * @param expression 成员表达式
   * @returns 是否为复杂成员表达式
   */
  private isComplexMemberExpression(expression: any): boolean {
    if (!expression || expression.type !== 'MemberExpression') {
      return false;
    }

    // 计算嵌套深度
    let depth = 0;
    let current = expression;
    while (current && current.type === 'MemberExpression') {
      depth++;
      current = current.object;
    }

    // 超过2层嵌套算复杂
    return depth > 2;
  }

  /**
   * 检查是否为翻译函数
   * @param expression 函数调用表达式
   * @returns 是否为翻译函数
   */
  private isTranslationFunction(expression: any): boolean {
    if (!expression.callee) {
      return false;
    }

    const callee = expression.callee;
    
    // 检查直接调用：t()
    if (callee.type === 'Identifier') {
      const name = callee.name || callee.value;
      return ['t', 'i18n', 'translate', '_'].includes(name);
    }

    // 检查成员调用：i18n.t()
    if (callee.type === 'MemberExpression' && callee.property) {
      const propName = callee.property.name || callee.property.value;
      return ['t', 'translate'].includes(propName);
    }

    return false;
  }

  /**
   * 检查是否为简单的状态setter调用
   * @param expression 函数调用表达式
   * @returns 是否为简单的状态setter调用
   */
  private isSimpleStateSetterCall(expression: any): boolean {
    if (!expression.callee || expression.callee.type !== 'Identifier') {
      return false;
    }

    const name = expression.callee.name || expression.callee.value;
    
    // React hooks的setter模式: set[Something]
    return typeof name === 'string' && /^set[A-Z]/.test(name);
  }

  /**
   * 检查是否为复杂参数
   * @param arg 参数节点
   * @returns 是否为复杂参数
   */
  private isComplexArgument(arg: any): boolean {
    if (!arg) {
      return false;
    }

    switch (arg.type) {
      case 'ConditionalExpression':
      case 'LogicalExpression':
      case 'BinaryExpression':
        return true;
      
      case 'CallExpression':
        return true; // 函数调用作为参数算复杂
      
      case 'ObjectExpression':
        return (arg.properties?.length || 0) > 3; // 超过3个属性的对象算复杂
      
      case 'ArrayExpression':
        return (arg.elements?.length || 0) > 5; // 超过5个元素的数组算复杂
      
      default:
        return false;
    }
  }

  /**
   * 检查是否为条件逻辑
   * @param expression 表达式节点
   * @returns 是否为条件逻辑
   */
  private isConditionalLogic(expression: any): boolean {
    return expression && expression.type === 'ConditionalExpression';
  }

  /**
   * 检查是否为函数调用
   * @param expression 表达式节点
   * @returns 是否为函数调用
   */
  private isFunctionCall(expression: any): boolean {
    return expression && expression.type === 'CallExpression';
  }

  /**
   * 检查是否为逻辑表达式
   * @param expression 表达式节点
   * @returns 是否为逻辑表达式
   */
  private isLogicalExpression(expression: any): boolean {
    return expression && expression.type === 'LogicalExpression';
  }

  /**
   * 检查是否为JSX元素
   * @param node 节点
   * @returns 是否为JSX元素
   */
  private isJSXElement(node: any): boolean {
    return node && (node.type === 'JSXElement' || node.type === 'JSXFragment');
  }

  /**
   * 检查节点是否包含逻辑操作
   * @param node 节点
   * @returns 是否包含逻辑操作
   */
  private containsLogicalOperations(node: any): boolean {
    if (!node) {
      return false;
    }

    const logicalTypes = [
      'ConditionalExpression',
      'LogicalExpression',
      'IfStatement',
      'CallExpression'
    ];

    return logicalTypes.includes(node.type);
  }

  /**
   * 获取节点位置
   * @param node 节点
   * @returns 位置或null
   */
  private getNodePosition(node: any): number | null {
    if (node && 'span' in node && node.span) {
      return node.span.start || null;
    }
    return null;
  }

  /**
   * 查找条件表达式开始位置
   * @param expression 条件表达式节点
   * @returns 位置或null
   */
  private findConditionalStart(expression: any): number | null {
    return this.getNodePosition(expression.test || expression);
  }

  /**
   * 查找函数调用开始位置
   * @param expression 函数调用表达式节点
   * @returns 位置或null
   */
  private findFunctionCallStart(expression: any): number | null {
    return this.getNodePosition(expression.callee || expression);
  }

  /**
   * 查找逻辑表达式开始位置
   * @param expression 逻辑表达式节点
   * @returns 位置或null
   */
  private findLogicalExpressionStart(expression: any): number | null {
    return this.getNodePosition(expression.left || expression);
  }
}