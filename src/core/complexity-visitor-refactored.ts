import type { Node } from '@swc/core';
import type { AsyncRuleEngine } from '../engine/types';
import { BaseVisitor } from './base-visitor';
import { DetailCollector } from './detail-collector';
import { RuleRegistry } from './rule-registry';
import type { CalculationOptions, FunctionResult } from './types';
import { DiagnosticMarker, RuleCategory } from './types';
import { PositionConverter } from '../utils/position-converter';

// 语义感知服务
import { SemanticPositionService } from './semantic-position-service';
import { IntelligentFallbackEngine } from './intelligent-fallback-engine';
import { JSXSemanticParser } from './jsx-semantic-parser';
import { FunctionSemanticAnalyzer } from './function-semantic-analyzer';
import { CodeSemanticAnalyzer } from './code-semantic-analyzer';
import type { SemanticPositioningOptions } from './semantic-types';

/**
 * 语义感知服务依赖注入容器
 */
interface SemanticServices {
  semanticPosition: SemanticPositionService;
  fallbackEngine: IntelligentFallbackEngine;
  jsxParser: JSXSemanticParser;
  functionAnalyzer: FunctionSemanticAnalyzer;
  codeAnalyzer: CodeSemanticAnalyzer;
}

/**
 * 重构后的 ComplexityVisitor - 语义感知版本
 * 
 * 核心改进：
 * - 模块化架构：职责分离到专门的语义服务
 * - 语义感知定位：精确定位到逻辑代码而非结构代码
 * - 智能回退机制：多级回退策略确保定位可靠性
 * - 可测试性：每个语义服务可独立测试
 * - 可扩展性：新增语义规则或节点类型支持更容易
 */
export class SemanticComplexityVisitor extends BaseVisitor {
  private readonly sourceCode: string;
  private readonly detailCollector?: DetailCollector;
  private readonly options: CalculationOptions;
  private readonly asyncRuleEngine?: AsyncRuleEngine;
  private readonly functionResults: Map<string, FunctionResult> = new Map();
  
  // 语义感知服务
  private readonly semanticServices: SemanticServices;
  
  // 状态管理
  private complexity = 0;
  private nestingLevel = 0;
  private currentFunction: string | null = null;
  private functionStack: Array<{ functionName: string | null; complexity: number }> = [];

  constructor(
    sourceCode: string,
    detailCollector?: DetailCollector,
    options: CalculationOptions = {},
    semanticServices?: SemanticServices,
    asyncRuleEngine?: AsyncRuleEngine
  ) {
    super();
    
    this.sourceCode = sourceCode;
    this.detailCollector = detailCollector;
    this.options = options;
    this.asyncRuleEngine = asyncRuleEngine;
    
    // 初始化语义服务
    if (semanticServices) {
      this.semanticServices = semanticServices;
    } else {
      // 创建默认语义服务
      this.semanticServices = this.createDefaultSemanticServices();
    }
  }

  /**
   * 获取函数分析结果
   * @returns 函数结果映射
   */
  getFunctionResults(): Map<string, FunctionResult> {
    return this.functionResults;
  }

  /**
   * 重写visit方法以正确处理函数边界管理和嵌套层级
   * @param node 要访问的节点
   * @returns 访问后的节点
   */
  public override visit<T extends Node>(node: T): T {
    // 检查是否是函数节点
    const isFunctionNode = this.isFunctionNode(node);
    
    if (isFunctionNode) {
      // 函数节点需要特殊处理：先设置函数上下文，再访问子节点
      return this.visitFunctionNode(node);
    } else {
      // 非函数节点：检查是否是嵌套节点，管理嵌套层级
      const isNesting = this.isNestingNode(node);
      const previousNestingLevel = this.nestingLevel;
      
      // 推入父节点栈
      this.parentStack.push(node);
      
      try {
        // 如果是嵌套节点，先增加嵌套层级
        if (isNesting) {
          this.nestingLevel++;
        }
        
        // 调用节点特定访问方法
        this.visitNode(node);
        
        // 访问子节点
        this.visitChildren(node);
        
        return node;
      } finally {
        // 恢复嵌套层级
        if (isNesting) {
          this.nestingLevel = previousNestingLevel;
        }
        // 确保栈清理
        this.parentStack.pop();
      }
    }
  }

  /**
   * 专门处理函数节点的访问
   * @param node 函数节点
   * @returns 访问后的节点
   */
  private visitFunctionNode<T extends Node>(node: T): T {
    // 推入父节点栈
    this.parentStack.push(node);
    
    try {
      // 获取函数信息和位置
      const functionInfo = this.semanticServices.functionAnalyzer.analyzeFunctionSemantics(node);
      const position = functionInfo.logicalStartPosition || this.getSemanticPosition(node);
      
      // 开始函数处理：设置函数上下文
      this.startFunction(node, position, this.extractFunctionName(node) || 'anonymous');
      
      // 注意：不调用 visitNode，因为函数节点不需要增加复杂度
      // 直接访问函数体的子节点（此时函数上下文已正确设置）
      this.visitChildren(node);
      
      // 完成函数处理：记录函数结果
      this.finishFunction(node);
      
      return node;
    } finally {
      // 确保栈清理
      this.parentStack.pop();
    }
  }

  /**
   * 检查节点是否是函数节点
   * @param node 节点
   * @returns 是否是函数节点
   */
  private isFunctionNode(node: any): boolean {
    if (!node || !node.type) return false;
    
    const functionTypes = [
      'FunctionDeclaration',
      'FunctionExpression', 
      'ArrowFunctionExpression',
      'MethodDefinition',
      'ClassMethod',        // SWC中的类方法
      'MethodProperty'      // SWC中的对象方法简写
    ];
    
    return functionTypes.includes(node.type);
  }

  /**
   * 获取当前复杂度
   * @returns 复杂度值
   */
  getComplexity(): number {
    return this.complexity;
  }

  /**
   * 获取总复杂度（所有函数的复杂度之和）
   * @returns 总复杂度值
   */
  getTotalComplexity(): number {
    let total = 0;
    this.functionResults.forEach(result => {
      total += result.complexity;
    });
    return total;
  }

  /**
   * 访问特定节点的实现
   * @param node 要访问的节点
   * @returns 处理后的节点
   */
  protected visitNode(node: Node): Node {
    // 根据节点类型分发到具体的访问方法
    switch (node.type) {
      case 'IfStatement':
        this.visitIfStatement(node);
        break;
      case 'WhileStatement':
        this.visitWhileStatement(node);
        break;
      case 'ForStatement':
        this.visitForStatement(node);
        break;
      case 'ConditionalExpression':
        this.visitConditionalExpression(node);
        break;
      case 'LogicalExpression':
        this.visitLogicalExpression(node);
        break;
      case 'CatchClause':
        this.visitCatchClause(node);
        break;
      case 'JSXElement':
        this.visitJSXElement(node);
        break;
      case 'JSXExpressionContainer':
        this.visitJSXExpressionContainer(node);
        break;
      case 'ArrowFunctionExpression':
        this.visitArrowFunctionExpression(node);
        break;
      case 'FunctionExpression':
        this.visitFunctionExpression(node);
        break;
      case 'FunctionDeclaration':
        this.visitFunctionDeclaration(node);
        break;
      case 'ClassMethod':
        this.visitClassMethod(node);
        break;
      case 'MethodProperty':
        this.visitMethodProperty(node);
        break;
    }
    
    return node;
  }

  /**
   * 访问子节点
   * @param node 节点
   */
  protected override visitChildren(node: any): void {
    // 简化实现：遍历节点属性
    for (const [key, value] of Object.entries(node)) {
      if (key === 'span' || key === 'type') continue;
      
      if (Array.isArray(value)) {
        for (const child of value) {
          if (child && typeof child === 'object' && child.type) {
            this.visit(child as Node);
          }
        }
      } else if (value && typeof value === 'object') {
        // 检查是否有type属性（标准AST节点）
        if ((value as any).type) {
          this.visit(value as Node);
        }
        // 特殊处理：ClassMethod的function属性包含函数体
        else if (key === 'function' && node.type === 'ClassMethod' && value.body) {
          this.visit(value.body as Node);
        }
      }
    }
  }

  /**
   * 访问各种节点类型 - 使用语义感知定位
   */
  
  visitIfStatement(node: any): void {
    this.processComplexityNode(node, 'if-statement', '条件语句');
  }

  visitWhileStatement(node: any): void {
    this.processComplexityNode(node, 'while-statement', 'while 循环');
  }

  visitForStatement(node: any): void {
    this.processComplexityNode(node, 'for-statement', 'for 循环');
  }

  visitConditionalExpression(node: any): void {
    this.processComplexityNode(node, 'conditional-expression', '三元操作符');
  }

  visitLogicalExpression(node: any): void {
    this.processComplexityNode(node, 'logical-operator', '逻辑操作符');
  }

  visitCatchClause(node: any): void {
    this.processComplexityNode(node, 'catch-clause', 'catch 异常处理');
  }

  visitJSXElement(node: any): void {
    // JSX 元素使用专门的语义解析
    const jsxInfo = this.semanticServices.jsxParser.parseJSXElement(node);
    
    if (jsxInfo.hasLogicalContent && jsxInfo.logicalComplexity > 0) {
      const position = this.getSemanticPosition(node);
      this.addComplexity(jsxInfo.logicalComplexity, position, 'JSX 逻辑复杂度');
    }
  }

  visitJSXExpressionContainer(node: any): void {
    // JSX 表达式容器 - 只有包含逻辑复杂度的表达式才计算复杂度
    const jsxInfo = this.semanticServices.jsxParser.parseJSXElement(node);
    
    // 只有当表达式包含真正的逻辑复杂度时才加分
    if (jsxInfo.logicalComplexity > 0) {
      const expressionPosition = this.semanticServices.jsxParser.findJSXExpressionContent(node);
      const position = expressionPosition || this.getSemanticPosition(node);
      this.addComplexity(jsxInfo.logicalComplexity, position, 'JSX 逻辑复杂度');
    }
    
    // 简单的表达式如 {t('...')} 或 {variable} 不增加复杂度
  }

  visitArrowFunctionExpression(node: any): void {
    // 箭头函数的访问将通过 visitFunctionNode 处理
    // 这里只需要处理非函数特有的逻辑
  }

  visitFunctionExpression(node: any): void {
    // 函数表达式的访问将通过 visitFunctionNode 处理
    // 这里只需要处理非函数特有的逻辑
  }

  visitFunctionDeclaration(node: any): void {
    // 函数声明的访问将通过 visitFunctionNode 处理
    // 这里只需要处理非函数特有的逻辑
  }

  visitClassMethod(node: any): void {
    // 类方法的访问将通过 visitFunctionNode 处理
    // 这里只需要处理非函数特有的逻辑
  }

  visitMethodProperty(node: any): void {
    // 对象方法的访问将通过 visitFunctionNode 处理
    // 这里只需要处理非函数特有的逻辑
  }

  /**
   * 开始函数处理 - 设置函数上下文
   * @param node 函数节点
   * @param position 位置
   * @param functionName 函数名
   */
  private startFunction(node: any, position: number, functionName: string): void {
    const lineInfo = this.calculateLineNumber(position);
    
    // 启动详细信息收集器的函数跟踪
    if (this.detailCollector && functionName) {
      this.detailCollector.startFunction(functionName, lineInfo.line, lineInfo.column);
    }
    
    // 保存当前状态（支持嵌套函数）
    const previousFunction = this.currentFunction;
    const previousComplexity = this.complexity;
    
    // 重置函数级状态
    this.currentFunction = functionName;
    this.complexity = 0;
    this.nestingLevel = 0;
    
    // 保存之前的状态以便恢复（嵌套函数支持）
    this.functionStack = this.functionStack || [];
    this.functionStack.push({
      functionName: previousFunction,
      complexity: previousComplexity,
    });
  }

  /**
   * 完成函数处理 - 在所有子节点访问完成后调用
   * @param node 函数节点
   */
  private finishFunction(node: any): void {
    const functionName = this.currentFunction;
    
    if (!functionName) {
      console.warn('finishFunction called but no currentFunction set');
      return;
    }
    
    // 结束详细信息收集并获取详情
    let functionDetails: any = undefined;
    if (this.detailCollector) {
      try {
        functionDetails = this.detailCollector.endFunction();
      } catch (error) {
        // 如果结束函数失败，记录错误但继续
        console.warn(`Failed to end function tracking for ${functionName}:`, error);
      }
    }
    
    // 计算函数位置
    const functionInfo = this.semanticServices.functionAnalyzer.analyzeFunctionSemantics(node);
    const position = functionInfo.logicalStartPosition || this.getSemanticPosition(node);
    const lineInfo = this.calculateLineNumber(position);
    
    // 记录函数结果
    this.functionResults.set(functionName, {
      name: functionName,
      complexity: this.complexity,
      line: lineInfo.line,
      column: lineInfo.column,
      filePath: '', // 将在外部设置
      details: functionDetails?.details || [], // 修正：使用details而不是steps
    });
    
    // 恢复之前的状态（支持嵌套函数）
    const previousState = this.functionStack.pop();
    if (previousState) {
      this.currentFunction = previousState.functionName;
      this.complexity = previousState.complexity;
    } else {
      // 没有更多嵌套函数，回到顶层状态
      this.currentFunction = null;
      this.complexity += this.functionResults.get(functionName)?.complexity || 0;
    }
  }

  /**
   * 处理复杂度节点 - 核心语义感知处理（简化版本）
   * @param node AST 节点
   * @param ruleId 规则ID
   * @param description 描述
   */
  private processComplexityNode(node: any, ruleId: string, description: string): void {
    // 获取语义感知位置
    const position = this.getSemanticPosition(node);
    
    // 获取规则复杂度
    const baseComplexity = this.getRuleComplexity(ruleId);
    
    // 计算嵌套加成
    const nestingIncrement = this.getNestingIncrement();
    const totalComplexity = baseComplexity + nestingIncrement;
    
    // 获取 span 信息
    const span = node.span ? { start: node.span.start, end: node.span.end } : undefined;
    
    // 记录复杂度，传递节点类型和span信息用于智能位置修正
    this.addComplexity(totalComplexity, position, description, node.type, span);
    
    // 注意：不在这里处理子节点访问或嵌套层级管理
    // 这些都由统一的 visit 方法处理
  }

  /**
   * 获取语义感知位置 - 核心定位逻辑
   * @param node AST 节点
   * @returns 语义位置
   */
  private getSemanticPosition(node: any): number {
    // 尝试从 span 获取回退位置
    const fallbackSpan = node.span?.start;
    
    // 使用语义位置服务进行智能定位
    const semanticPosition = this.semanticServices.semanticPosition.findSemanticPosition(
      node, 
      fallbackSpan
    );
    
    // 如果语义定位失败，使用智能回退引擎
    if (semanticPosition === fallbackSpan && fallbackSpan !== undefined) {
      const fallbackResult = this.semanticServices.fallbackEngine.performIntelligentFallback(node);
      return fallbackResult.position || fallbackSpan;
    }
    
    return semanticPosition;
  }

  /**
   * 添加复杂度
   * @param complexity 复杂度值
   * @param position 位置
   * @param description 描述
   * @param nodeType 节点类型（用于智能位置修正）
   * @param span SWC span 信息（可选）
   */
  private addComplexity(complexity: number, position: number, description: string, nodeType?: string, span?: { start: number; end: number }): void {
    this.complexity += complexity;
    
    // 改进：即使没有活跃函数，也记录详细信息（用于调试）
    if (this.detailCollector) {
      const lineInfo = this.calculateLineNumber(position, nodeType);
      
      // 如果没有当前函数，创建一个临时的全局上下文
      if (!this.currentFunction) {
        // 尝试启动一个全局函数上下文
        const globalFunctionName = '<global>';
        this.detailCollector.startFunction(globalFunctionName, lineInfo.line, lineInfo.column);
        this.currentFunction = globalFunctionName;
      }
      
      this.detailCollector.addStep({
        line: lineInfo.line,
        column: lineInfo.column,
        increment: complexity,
        ruleId: description,
        description,
        context: this.buildContext(description),
        nodeType: nodeType,
        span: span,
      });
    }
    
    // 调试信息：记录复杂度归属
    if (this.options.enableDebugLog) {
      console.log(`[DEBUG] Added complexity ${complexity} to function "${this.currentFunction || '<none>'}" at line ${this.calculateLineNumber(position, nodeType).line}: ${description}`);
    }
  }

  /**
   * 根据字符位置计算行号和列号（改进版本）
   * 使用PositionConverter的标准转换逻辑，确保与其他组件一致
   * @param position 字符位置
   * @param nodeType 节点类型（用于智能位置修正）
   * @returns 行号和列号信息
   */
  private calculateLineNumber(position: number, nodeType?: string): { line: number; column: number } {
    if (position <= 0) {
      return { line: 1, column: 1 };
    }

    if (position >= this.sourceCode.length) {
      // 位置超出源代码范围，使用最后一行
      const lines = this.sourceCode.split('\n');
      const lastLine = lines[lines.length - 1];
      return { line: lines.length, column: (lastLine?.length || 0) + 1 };
    }

    // 使用智能位置转换，包含回退机制和节点类型修正
    try {
      return PositionConverter.spanToPosition(this.sourceCode, position, undefined, undefined, nodeType);
    } catch (error) {
      // 后备方案：使用基础转换
      try {
        return PositionConverter.fastSpanToPosition(this.sourceCode, position);
      } catch (fallbackError) {
        // 最终后备：逐字符计算
        let line = 1;
        let column = 1;
        
        for (let i = 0; i < position && i < this.sourceCode.length; i++) {
          if (this.sourceCode[i] === '\n') {
            line++;
            column = 1;
          } else {
            column++;
          }
        }
        
        return { line, column };
      }
    }
  }

  /**
   * 获取规则复杂度
   * @param ruleId 规则ID
   * @returns 复杂度值
   */
  private getRuleComplexity(ruleId: string): number {
    if (this.asyncRuleEngine) {
      // 异步规则引擎处理（简化）
      return 1;
    }
    
    // 从规则注册表获取
    const rule = RuleRegistry.getRule(ruleId);
    return rule ? 1 : 1; // 简化为固定值
  }

  /**
   * 获取嵌套增量
   * @returns 嵌套增量
   */
  private getNestingIncrement(): number {
    return this.nestingLevel;
  }

  /**
   * 检查是否为嵌套节点
   * @param node AST 节点
   * @returns 是否嵌套
   */
  private isNestingNode(node: any): boolean {
    const nestingTypes = [
      'IfStatement',
      'WhileStatement',
      'ForStatement',
      'TryStatement',
      'SwitchStatement'
    ];
    
    return nestingTypes.includes(node.type);
  }

  /**
   * 在嵌套上下文中执行
   * @param callback 回调函数
   */
  private withNesting(callback: () => void): void {
    this.nestingLevel++;
    try {
      callback();
    } finally {
      this.nestingLevel--;
    }
  }

  /**
   * 提取函数名 - 改进版本
   * @param node 函数节点
   * @returns 函数名
   */
  private extractFunctionName(node: any): string {
    // 处理命名函数声明 - SWC使用 identifier.value
    if (node.identifier?.value) {
      return node.identifier.value;
    }
    
    // 处理命名函数声明 - 备用检查
    if (node.id?.name) {
      return node.id.name;
    }
    
    if (node.id?.value) {
      return node.id.value;
    }
    
    // 处理类方法和对象方法
    if ((node.type === 'ClassMethod' || node.type === 'MethodProperty') && node.key) {
      if (node.key.name) {
        return node.key.name;
      }
      if (node.key.value) {
        return node.key.value;
      }
      if (node.key.type === 'Identifier') {
        return node.key.name || node.key.value || 'method';
      }
    }
    
    // 处理传统方法定义
    if (node.type === 'MethodDefinition' && node.key) {
      if (node.key.name) {
        return node.key.name;
      }
      if (node.key.value) {
        return node.key.value;
      }
      if (node.key.type === 'Identifier') {
        return node.key.name || node.key.value || 'method';
      }
    }
    
    // 处理赋值给变量的函数表达式
    // 检查父节点是否是变量声明或赋值表达式
    const parent = this.getParent();
    if (parent) {
      // const/let/var functionName = function() {}
      if (parent.type === 'VariableDeclarator' && parent.id) {
        if (parent.id.name) {
          return parent.id.name;
        }
        if (parent.id.value) {
          return parent.id.value;
        }
      }
      
      // obj.methodName = function() {}
      if (parent.type === 'AssignmentExpression' && parent.left) {
        if (parent.left.type === 'MemberExpression' && parent.left.property) {
          // SWC中属性名可能在name或value中
          return parent.left.property.name || parent.left.property.value || 'method';
        }
        if (parent.left.type === 'Identifier') {
          return parent.left.name || parent.left.value || 'assignment';
        }
      }
      
      // 对象字面量中的方法 { methodName: function() {} }
      if (parent.type === 'KeyValueProperty' && parent.key) {
        if (parent.key.type === 'Identifier') {
          return parent.key.name || parent.key.value || 'property';
        }
        // 如果key是字符串字面量
        if (parent.key.type === 'StringLiteral') {
          return parent.key.value;
        }
      }
      
      // 对象方法简写 { methodName() {} } - 兼容旧API
      if (parent.type === 'Property' && parent.key) {
        if (parent.key.name) {
          return parent.key.name;
        }
        if (parent.key.value) {
          return parent.key.value;
        }
      }
    }
    
    // 特殊处理箭头函数
    if (node.type === 'ArrowFunctionExpression') {
      const lineInfo = this.calculateLineNumber(this.getSemanticPosition(node));
      return `arrow_${lineInfo.line}_${lineInfo.column}`;
    }
    
    // 匿名函数 - 使用更友好的命名
    const lineInfo = this.calculateLineNumber(this.getSemanticPosition(node));
    return `anonymous_${lineInfo.line}_${lineInfo.column}`;
  }

  /**
   * 构建上下文信息
   * @param description 描述
   * @returns 上下文
   */
  private buildContext(description: string): string {
    const nestingInfo = this.nestingLevel > 0 ? ` (嵌套层级: ${this.nestingLevel})` : '';
    return `${description}${nestingInfo}`;
  }

  /**
   * 创建默认语义服务
   * @returns 语义服务
   */
  private createDefaultSemanticServices(): SemanticServices {
    const options: SemanticPositioningOptions = {
      enableJSXSemantics: true,
      enableFunctionSemantics: true,
      enableSmartLineFiltering: true,
    };
    
    const codeAnalyzer = new CodeSemanticAnalyzer(this.sourceCode);
    const jsxParser = new JSXSemanticParser(this.sourceCode, codeAnalyzer);
    const functionAnalyzer = new FunctionSemanticAnalyzer(this.sourceCode, codeAnalyzer);
    const semanticPosition = new SemanticPositionService(this.sourceCode, codeAnalyzer, options);
    const fallbackEngine = new IntelligentFallbackEngine(semanticPosition, codeAnalyzer, this.sourceCode);
    
    return {
      semanticPosition,
      fallbackEngine,
      jsxParser,
      functionAnalyzer,
      codeAnalyzer,
    };
  }
}