import type { Node } from '@swc/core';
import type { 
  FallbackResult, 
  EmergencyContext
} from './semantic-types';
import { SemanticPositionService } from './semantic-position-service';
import { CodeSemanticAnalyzer } from './code-semantic-analyzer';
import { PositionConverter } from '../utils/position-converter';

/**
 * 智能回退引擎 - 提供多级智能回退策略
 * 
 * 核心功能：
 * - 多级回退策略（语义 → 结构 → 上下文 → 紧急）
 * - 基于语义理解的智能定位
 * - 自适应回退机制
 * - 错误恢复和诊断
 */
export class IntelligentFallbackEngine {
  private readonly sourceCode: string;
  private readonly lines: string[];

  constructor(
    _semanticPosition: SemanticPositionService,
    _codeAnalyzer: CodeSemanticAnalyzer,
    sourceCode: string
  ) {
    this.sourceCode = sourceCode;
    this.lines = sourceCode.split('\n');
  }

  /**
   * 执行智能回退定位
   * @param node AST 节点
   * @param previousAttempts 之前尝试的策略
   * @returns 回退结果
   */
  performIntelligentFallback(node: Node, previousAttempts: string[] = []): FallbackResult {
    const context: EmergencyContext = {
      node,
      sourceCode: this.sourceCode,
      attemptedStrategies: previousAttempts,
    };

    // 第一级：语义回退
    if (!previousAttempts.includes('semantic')) {
      const semanticResult = this.attemptSemanticFallback(node, context);
      if (semanticResult.position !== null) {
        return semanticResult;
      }
    }

    // 第二级：结构回退
    if (!previousAttempts.includes('structural')) {
      const structuralResult = this.attemptStructuralFallback(node, context);
      if (structuralResult.position !== null) {
        return structuralResult;
      }
    }

    // 第三级：上下文回退
    if (!previousAttempts.includes('contextual')) {
      const contextualResult = this.attemptContextualFallback(node, context);
      if (contextualResult.position !== null) {
        return contextualResult;
      }
    }

    // 第四级：紧急回退
    const emergencyPosition = this.generateEmergencyPosition(context);
    return {
      position: emergencyPosition,
      strategy: 'emergency',
      confidence: 0.1,
      reason: 'All fallback strategies failed, using emergency position',
    };
  }

  /**
   * 尝试语义回退
   * @param node AST 节点
   * @param context 紧急上下文
   * @returns 回退结果
   */
  private attemptSemanticFallback(node: Node, context: EmergencyContext): FallbackResult {
    try {
      // 基于节点类型的语义分析
      const semanticPosition = this.findSemanticMeaningfulPosition(node);
      if (semanticPosition !== null) {
        return {
          position: semanticPosition,
          strategy: 'semantic',
          confidence: 0.8,
          reason: 'Found semantic meaningful position based on node type',
        };
      }

      // 基于周围代码的语义分析
      const contextualPosition = this.findContextualSemanticPosition(node);
      if (contextualPosition !== null) {
        return {
          position: contextualPosition,
          strategy: 'semantic',
          confidence: 0.7,
          reason: 'Found position based on surrounding semantic context',
        };
      }

    } catch (error) {
      context.error = error instanceof Error ? error.message : String(error);
    }

    return {
      position: null,
      strategy: 'semantic',
      confidence: 0,
      reason: 'Semantic fallback failed',
    };
  }

  /**
   * 尝试结构回退
   * @param node AST 节点
   * @param context 紧急上下文
   * @returns 回退结果
   */
  private attemptStructuralFallback(node: Node, context: EmergencyContext): FallbackResult {
    try {
      // 基于 AST 结构的位置推断
      const structuralPosition = this.findStructuralPosition(node);
      if (structuralPosition !== null) {
        return {
          position: structuralPosition,
          strategy: 'structural',
          confidence: 0.6,
          reason: 'Found position based on AST structural analysis',
        };
      }

      // 基于父子节点关系的推断
      const hierarchicalPosition = this.findHierarchicalPosition(node);
      if (hierarchicalPosition !== null) {
        return {
          position: hierarchicalPosition,
          strategy: 'structural',
          confidence: 0.5,
          reason: 'Found position based on node hierarchy',
        };
      }

    } catch (error) {
      context.error = error instanceof Error ? error.message : String(error);
    }

    return {
      position: null,
      strategy: 'structural',
      confidence: 0,
      reason: 'Structural fallback failed',
    };
  }

  /**
   * 尝试上下文回退
   * @param node AST 节点
   * @param context 紧急上下文
   * @returns 回退结果
   */
  private attemptContextualFallback(node: Node, context: EmergencyContext): FallbackResult {
    try {
      // 基于代码上下文的推断
      const contextPosition = this.findContextualPosition(node);
      if (contextPosition !== null) {
        return {
          position: contextPosition,
          strategy: 'contextual',
          confidence: 0.4,
          reason: 'Found position based on code context analysis',
        };
      }

      // 基于同类节点的模式推断
      const patternPosition = this.findPatternBasedPosition(node);
      if (patternPosition !== null) {
        return {
          position: patternPosition,
          strategy: 'contextual',
          confidence: 0.3,
          reason: 'Found position based on similar node patterns',
        };
      }

    } catch (error) {
      context.error = error instanceof Error ? error.message : String(error);
    }

    return {
      position: null,
      strategy: 'contextual',
      confidence: 0,
      reason: 'Contextual fallback failed',
    };
  }

  /**
   * 生成紧急位置
   * @param context 紧急上下文
   * @returns 紧急位置
   */
  private generateEmergencyPosition(context: EmergencyContext): number {
    const node = context.node;

    // 尝试从节点获取任何可用的位置信息
    if ('span' in node && node.span) {
      const span = node.span as any;
      if (typeof span.start === 'number') {
        return Math.max(0, span.start);
      }
    }

    // 尝试从节点的其他属性获取位置
    const nodeAsAny = node as any;
    if (nodeAsAny.start && typeof nodeAsAny.start === 'number') {
      return Math.max(0, nodeAsAny.start);
    }

    if (nodeAsAny.range && Array.isArray(nodeAsAny.range) && nodeAsAny.range.length > 0) {
      return Math.max(0, nodeAsAny.range[0]);
    }

    // 最后的紧急策略：返回代码开始位置
    return 0;
  }

  /**
   * 查找语义上有意义的位置
   * @param node AST 节点
   * @returns 位置或 null
   */
  private findSemanticMeaningfulPosition(node: Node): number | null {
    const nodeType = node.type;

    // 基于节点类型的特殊处理
    switch (nodeType) {
      case 'JSXElement':
        return this.findJSXElementMeaningfulPosition(node);
      case 'JSXExpressionContainer':
        return this.findJSXExpressionMeaningfulPosition(node);
      case 'ArrowFunctionExpression':
        return this.findArrowFunctionMeaningfulPosition(node);
      case 'ConditionalExpression':
        return this.findConditionalExpressionMeaningfulPosition(node);
      case 'LogicalExpression':
        return this.findLogicalExpressionMeaningfulPosition(node);
      default:
        return this.findGenericMeaningfulPosition(node);
    }
  }

  /**
   * 查找上下文语义位置
   * @param node AST 节点
   * @returns 位置或 null
   */
  private findContextualSemanticPosition(node: Node): number | null {
    if (!('span' in node) || !node.span) {
      return null;
    }

    const span = node.span as any;
    const startPosition = span.start;
    
    try {
      const lineColumn = PositionConverter.spanToPosition(this.sourceCode, startPosition);
      const lineIndex = lineColumn.line - 1;
      
      if (lineIndex >= 0 && lineIndex < this.lines.length) {
        const line = this.lines[lineIndex];
        
        if (!line) return null;
        
        // 查找行中第一个有意义的代码位置
        const meaningfulMatch = line.match(/[a-zA-Z_$]/);
        if (meaningfulMatch && meaningfulMatch.index !== undefined) {
          return PositionConverter.lineColumnToOffset(
            this.sourceCode, 
            lineColumn.line, 
            meaningfulMatch.index + 1
          );
        }
      }
    } catch (_error) {
      // 忽略转换错误
    }

    return null;
  }

  /**
   * 查找结构位置
   * @param node AST 节点
   * @returns 位置或 null
   */
  private findStructuralPosition(node: Node): number | null {
    // 尝试从 span 获取位置
    if ('span' in node && node.span) {
      const span = node.span as any;
      if (typeof span.start === 'number') {
        return span.start;
      }
    }

    return null;
  }

  /**
   * 查找层次结构位置
   * @param node AST 节点
   * @returns 位置或 null
   */
  private findHierarchicalPosition(node: Node): number | null {
    // 这里可以实现基于父子节点关系的位置推断
    // 简化实现：返回结构位置
    return this.findStructuralPosition(node);
  }

  /**
   * 查找上下文位置
   * @param node AST 节点
   * @returns 位置或 null
   */
  private findContextualPosition(node: Node): number | null {
    // 基于代码上下文的推断
    return this.findContextualSemanticPosition(node);
  }

  /**
   * 查找基于模式的位置
   * @param node AST 节点
   * @returns 位置或 null
   */
  private findPatternBasedPosition(node: Node): number | null {
    // 基于同类节点模式的推断
    // 简化实现：使用结构位置
    return this.findStructuralPosition(node);
  }

  // 以下是具体节点类型的语义位置查找方法

  private findJSXElementMeaningfulPosition(node: Node): number | null {
    return this.findStructuralPosition(node);
  }

  private findJSXExpressionMeaningfulPosition(node: Node): number | null {
    return this.findStructuralPosition(node);
  }

  private findArrowFunctionMeaningfulPosition(node: Node): number | null {
    return this.findStructuralPosition(node);
  }

  private findConditionalExpressionMeaningfulPosition(node: Node): number | null {
    return this.findStructuralPosition(node);
  }

  private findLogicalExpressionMeaningfulPosition(node: Node): number | null {
    return this.findStructuralPosition(node);
  }

  private findGenericMeaningfulPosition(node: Node): number | null {
    return this.findStructuralPosition(node);
  }
}