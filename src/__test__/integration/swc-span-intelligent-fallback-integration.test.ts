import { describe, test, expect, beforeEach } from 'vitest';
import { ComplexityVisitor } from '../../core/complexity-visitor';
import { DetailCollector } from '../../core/detail-collector';
import { ASTParser } from '../../core/parser';
import { PositionConverter } from '../../utils/position-converter';
import type { Module } from '@swc/core';
import type { CalculationOptions } from '../../core/types';

/**
 * SWC Span 智能回退机制全面集成测试
 * 
 * Task 13: 全面集成测试 - 测试完整的位置转换流程（AST → 最终位置）
 * 
 * 测试覆盖：
 * 1. 完整的位置转换流程（AST → 最终位置）
 * 2. 多种节点类型的混合场景
 * 3. 大文件和复杂语法的综合情况  
 * 4. 与现有 ComplexityVisitor API 的兼容性
 * 
 * 验证核心功能：
 * - L0 层语义感知位置转换
 * - L1 层策略映射系统
 * - Token 查找系统的三级降级策略
 * - 箭头函数和 JSX 的精确定位
 * - 错误恢复和多级回退机制
 */
describe('SWC Span 智能回退机制 - 全面集成测试', () => {
  let parser: ASTParser;
  let options: CalculationOptions;

  beforeEach(() => {
    parser = new ASTParser();
    options = { includeDetails: true };
  });

  // =============================================================================
  // 1. 完整位置转换流程测试（AST → 最终位置）
  // =============================================================================
  
  describe('完整位置转换流程', () => {
    test('应该完整处理 AST 解析到最终位置输出的全流程', async () => {
      const sourceCode = `function complexFunction(x, y) {
  // 控制流语句
  if (x > 0) {
    while (y < 10) {
      for (let i = 0; i < 5; i++) {
        if (i % 2 === 0) {
          console.log('even');
        }
      }
      y++;
    }
  }
  
  // 逻辑运算符
  return x > 0 && y < 20 || x < 0;
}`;

      // 1. AST 解析阶段
      const ast = await parser.parseCode(sourceCode, 'test.ts');
      expect(ast).toBeDefined();
      expect(ast.type).toBe('Module');

      // 2. 访问者遍历和复杂度计算阶段
      const detailCollector = new DetailCollector();
      const visitor = new ComplexityVisitor(sourceCode, detailCollector, options);
      visitor.visit(ast as Module);
      const results = visitor.getResults();

      // 验证结果结构
      expect(results).toHaveLength(1);
      const func = results[0];
      expect(func.name).toBe('complexFunction');
      expect(func.complexity).toBeGreaterThan(6); // 复杂控制流

      // 3. 位置信息验证 - 从 span 到最终位置的转换
      expect(func.line).toBe(1);
      expect(func.column).toBeGreaterThanOrEqual(0);
      
      // 验证 details 中的位置转换
      if (func.details) {
        const validDetails = func.details.filter(d => d.line > 0);
        expect(validDetails.length).toBeGreaterThan(0);
        
        validDetails.forEach(detail => {
          // 验证有效位置信息
          expect(detail.line).toBeGreaterThan(0);
          expect(detail.column).toBeGreaterThanOrEqual(0);
          
          // 验证位置在合理范围内
          const lines = sourceCode.split('\n');
          expect(detail.line).toBeLessThanOrEqual(lines.length);
          
          // 验证该行确实存在对应的代码内容
          const lineContent = lines[detail.line - 1];
          expect(lineContent).toBeDefined();
          expect(lineContent.trim()).not.toBe('');
        });
      }
    });

    test('应该处理空行和注释对位置转换的影响', async () => {
      const sourceCode = `// 文件头注释

import React from 'react';

// 函数前注释  
function testFunction() {
  // 空行之前
  
  // 空行之后
  if (true) {
    return 'test';
  }
  
  // 文件末尾
}`;

      const ast = await parser.parseCode(sourceCode, 'test-with-comments.ts');
      const detailCollector = new DetailCollector();
      const visitor = new ComplexityVisitor(sourceCode, detailCollector, options);
      visitor.visit(ast as Module);
      const results = visitor.getResults();

      expect(results).toHaveLength(1);
      const func = results[0];
      
      // 验证函数能被正确识别
      expect(func.line).toBeGreaterThan(0); // 函数定义行
      expect(func.name).toBe('testFunction');
      
      if (func.details) {
        const validDetails = func.details.filter(d => d.line > 0);
        const ifDetail = validDetails.find(d => d.ruleId === 'if-statement');
        expect(ifDetail).toBeDefined();
        expect(ifDetail!.line).toBeGreaterThanOrEqual(func.line); // if 语句应该在函数定义行或之后
      }
    });
  });

  // =============================================================================
  // 2. 多种节点类型混合场景测试
  // =============================================================================
  
  describe('多种节点类型混合场景', () => {
    test('应该正确处理控制流 + 逻辑运算符 + JSX 的混合场景', async () => {
      const sourceCode = `import React from 'react';

export function ComplexComponent({ user, data }) {
  // 控制流与逻辑运算符结合
  if (user && user.isActive || data?.length > 0) {
    // 三元运算符嵌套在 JSX 中
    return (
      <div>
        <h1>{user?.name || 'Guest'}</h1>
        {data?.map(item => (
          <div key={item.id}>
            {item.status === 'active' ? 
              <span className="active">Active</span> : 
              <span className="inactive">Inactive</span>
            }
          </div>
        ))}
        <button onClick={() => {
          // 箭头函数中的控制流
          if (user.isAdmin && data.length > 10) {
            console.log('Admin with large dataset');
          }
        }}>
          Process Data
        </button>
      </div>
    );
  }
  
  // while 循环与递归调用
  let attempts = 0;
  while (attempts < 3 && !processData(data)) {
    attempts++;
    return ComplexComponent({ user, data: data.slice(1) }); // 递归调用
  }
  
  return null;
}

function processData(data) {
  return data && data.length > 0;
}`;

      const ast = await parser.parseCode(sourceCode, 'complex-mixed.tsx');
      const detailCollector = new DetailCollector();
      const visitor = new ComplexityVisitor(sourceCode, detailCollector, options);
      visitor.visit(ast as Module);
      const results = visitor.getResults();

      expect(results).toHaveLength(1); // 检测到主要函数
      
      // 验证检测到的函数
      const mainFunc = results[0];
      expect(mainFunc).toBeDefined();
      expect(mainFunc.complexity).toBeGreaterThan(0); // 确实有复杂逻辑

      // 验证details中包含各种复杂度类型的位置信息
      if (mainFunc.details) {
        const validDetails = mainFunc.details.filter(d => d.line > 0);
        expect(validDetails.length).toBeGreaterThan(0); // 至少有复杂度点
        
        // 应该包含复杂度类型
        const detailTypes = validDetails.map(d => d.ruleId);
        expect(detailTypes.length).toBeGreaterThan(0);
        // 可能包含 if-statement 或 logical-operator 等
        const hasComplexityRules = detailTypes.some(type => 
          ['if-statement', 'logical-operator', 'while-statement', 'for-statement'].includes(type)
        );
        expect(hasComplexityRules).toBe(true);

        // 验证所有位置都在合理范围内
        validDetails.forEach(detail => {
          expect(detail.line).toBeGreaterThan(0);
          expect(detail.line).toBeLessThanOrEqual(sourceCode.split('\n').length);
        });
      }
    });

    test('应该正确处理嵌套函数和箭头函数的混合定位', async () => {
      const sourceCode = `function outerFunction() {
  const regularArrow = (x) => {
    return x > 0 ? true : false;
  };
  
  const asyncArrow = async (data) => {
    if (data && data.length > 0) {
      return data.filter(item => 
        item.active && item.score > 5
      );
    }
  };
  
  function innerFunction() {
    const nestedArrow = () => {
      for (let i = 0; i < 10; i++) {
        if (i % 2 === 0) {
          console.log(i);
        }
      }
    };
    return nestedArrow;
  }
  
  return { regularArrow, asyncArrow, innerFunction };
}`;

      const ast = await parser.parseCode(sourceCode, 'nested-functions.ts');
      const detailCollector = new DetailCollector();
      const visitor = new ComplexityVisitor(sourceCode, detailCollector, options);
      visitor.visit(ast as Module);
      const results = visitor.getResults();

      // 应该识别出多个函数
      expect(results.length).toBeGreaterThanOrEqual(1);

      // 验证每个函数的位置信息
      results.forEach(func => {
        expect(func.line).toBeGreaterThan(0);
        expect(func.column).toBeGreaterThanOrEqual(0);
        
        // 验证函数名不为空
        expect(func.name).toBeTruthy();
        
        // 验证复杂度合理
        expect(func.complexity).toBeGreaterThanOrEqual(0);
      });

      // 特别验证箭头函数的定位
      const arrowFunctions = results.filter(r => 
        r.name.includes('Arrow') || r.name.includes('arrow') || 
        r.name.includes('function')
      );
      
      arrowFunctions.forEach(arrowFunc => {
        if (arrowFunc.details) {
          arrowFunc.details.forEach(detail => {
            // 箭头函数的位置应该指向合理的代码行
            const lineContent = sourceCode.split('\n')[detail.line - 1];
            expect(lineContent).toBeDefined();
            expect(lineContent.trim()).not.toBe('');
          });
        }
      });
    });
  });

  // =============================================================================
  // 3. 大文件和复杂语法综合测试
  // =============================================================================
  
  describe('大文件和复杂语法综合测试', () => {
    test('应该高效处理包含多种现代语法的大型文件', async () => {
      // 生成一个包含多种语法结构的大型代码示例
      const generateLargeComplexCode = () => {
        const imports = `import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { debounce, throttle } from 'lodash';
import type { User, DataItem, FilterOptions } from './types';`;

        const interfaces = `interface ComponentProps {
  users: User[];
  data: DataItem[];
  onFilter: (options: FilterOptions) => void;
  onSelect: (item: DataItem) => void;
}

type FilterFunction<T> = (item: T) => boolean;
type SortFunction<T> = (a: T, b: T) => number;`;

        const mainComponent = `export default function LargeComplexComponent({
  users,
  data,
  onFilter,
  onSelect
}: ComponentProps) {
  const [filters, setFilters] = useState<FilterOptions>({});
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [selectedItems, setSelectedItems] = useState<DataItem[]>([]);

  // 复杂的过滤逻辑
  const filteredData = useMemo(() => {
    if (!data || data.length === 0) {
      return [];
    }

    return data.filter(item => {
      // 多层嵌套的过滤条件
      if (filters.category && item.category !== filters.category) {
        return false;
      }

      if (filters.status && item.status !== filters.status) {
        return false;
      }

      if (filters.score) {
        if (filters.score.min && item.score < filters.score.min) {
          return false;
        }
        if (filters.score.max && item.score > filters.score.max) {
          return false;  
        }
      }

      // 复杂的用户权限检查
      const user = users.find(u => u.id === item.userId);
      if (user) {
        if (filters.userType && user.type !== filters.userType) {
          return false;
        }

        if (filters.requiresPermission) {
          const hasPermission = user.permissions?.includes(filters.requiresPermission);
          if (!hasPermission) {
            return false;
          }
        }
      }

      return true;
    });
  }, [data, filters, users]);

  // 复杂的排序逻辑
  const sortedData = useMemo(() => {
    const sorted = [...filteredData].sort((a, b) => {
      let comparison = 0;

      // 多重排序条件
      if (filters.sortBy === 'name') {
        comparison = a.name.localeCompare(b.name);
      } else if (filters.sortBy === 'score') {
        comparison = a.score - b.score;
      } else if (filters.sortBy === 'date') {
        comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
      }

      return sortOrder === 'asc' ? comparison : -comparison;
    });

    return sorted;
  }, [filteredData, filters.sortBy, sortOrder]);

  // 复杂的事件处理器
  const handleFilterChange = useCallback((newFilters: Partial<FilterOptions>) => {
    setFilters(prev => {
      const updated = { ...prev, ...newFilters };
      
      // 复杂的过滤器验证逻辑
      if (updated.score) {
        if (updated.score.min && updated.score.max) {
          if (updated.score.min > updated.score.max) {
            updated.score.min = updated.score.max;
          }
        }
      }

      onFilter(updated);
      return updated;
    });
  }, [onFilter]);

  const handleItemSelect = useCallback((item: DataItem) => {
    setSelectedItems(prev => {
      const isSelected = prev.some(i => i.id === item.id);
      
      if (isSelected) {
        return prev.filter(i => i.id !== item.id);
      } else {
        // 复杂的选择逻辑
        if (prev.length >= 10) {
          return [...prev.slice(1), item];
        } else {
          return [...prev, item];
        }
      }
    });
    
    onSelect(item);
  }, [onSelect]);

  // 复杂的批量操作
  const handleBatchOperation = useCallback(async (operation: string) => {
    if (selectedItems.length === 0) {
      return;
    }

    try {
      for (const item of selectedItems) {
        if (operation === 'delete') {
          await deleteItem(item.id);
        } else if (operation === 'approve') {
          if (item.status === 'pending') {
            await approveItem(item.id);
          }
        } else if (operation === 'reject') {
          if (item.status === 'pending') {
            await rejectItem(item.id);
          }
        }
      }
    } catch (error) {
      console.error('Batch operation failed:', error);
    }
  }, [selectedItems]);

  return (
    <div className="large-complex-component">
      <div className="filters">
        {/* 复杂的过滤器 UI */}
        <select
          value={filters.category || ''}
          onChange={(e) => handleFilterChange({ category: e.target.value || undefined })}
        >
          <option value="">All Categories</option>
          <option value="type1">Type 1</option>
          <option value="type2">Type 2</option>
        </select>
      </div>

      <div className="data-list">
        {sortedData.map(item => (
          <div
            key={item.id}
            className={\`item \${selectedItems.some(i => i.id === item.id) ? 'selected' : ''}\`}
            onClick={() => handleItemSelect(item)}
          >
            <h3>{item.name}</h3>
            <p>Score: {item.score}</p>
            <p>Status: {item.status}</p>
            {item.tags && item.tags.length > 0 && (
              <div className="tags">
                {item.tags.map(tag => (
                  <span key={tag} className="tag">{tag}</span>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>

      {selectedItems.length > 0 && (
        <div className="batch-operations">
          <button onClick={() => handleBatchOperation('approve')}>
            Approve Selected ({selectedItems.length})
          </button>
          <button onClick={() => handleBatchOperation('reject')}>
            Reject Selected ({selectedItems.length})
          </button>
          <button onClick={() => handleBatchOperation('delete')}>
            Delete Selected ({selectedItems.length})
          </button>
        </div>
      )}
    </div>
  );
}`;

        const helperFunctions = `async function deleteItem(id: string): Promise<void> {
  // 模拟异步删除操作
  await new Promise(resolve => setTimeout(resolve, 100));
}

async function approveItem(id: string): Promise<void> {
  // 复杂的审批逻辑
  const item = await fetchItem(id);
  if (item && item.status === 'pending') {
    if (item.score > 7) {
      await updateItemStatus(id, 'approved');
    } else {
      throw new Error('Score too low for approval');
    }
  }
}

async function rejectItem(id: string): Promise<void> {
  const item = await fetchItem(id);
  if (item && item.status === 'pending') {
    await updateItemStatus(id, 'rejected');
  }
}

async function fetchItem(id: string): Promise<DataItem | null> {
  // 模拟 API 调用
  try {
    const response = await fetch(\`/api/items/\${id}\`);
    if (response.ok) {
      return await response.json();
    }
  } catch (error) {
    console.error('Failed to fetch item:', error);
  }
  return null;
}

async function updateItemStatus(id: string, status: string): Promise<void> {
  try {
    const response = await fetch(\`/api/items/\${id}\`, {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ status })
    });
    
    if (!response.ok) {
      throw new Error('Failed to update status');
    }
  } catch (error) {
    console.error('Failed to update item status:', error);
    throw error;
  }
}`;

        return [imports, interfaces, mainComponent, helperFunctions].join('\n\n');
      };

      const largeCode = generateLargeComplexCode();
      const lines = largeCode.split('\n');
      expect(lines.length).toBeGreaterThan(200); // 确实是大文件

      // 记录开始时间以测试性能
      const startTime = Date.now();
      
      const ast = await parser.parseCode(largeCode, 'large-complex.tsx');
      const detailCollector = new DetailCollector();
      const visitor = new ComplexityVisitor(largeCode, detailCollector, options);
      visitor.visit(ast as Module);
      const results = visitor.getResults();

      const endTime = Date.now();
      const processingTime = endTime - startTime;

      // 性能验证：大文件处理应该在合理时间内完成（< 5秒）
      expect(processingTime).toBeLessThan(5000);

      // 验证检测到多个函数
      expect(results.length).toBeGreaterThanOrEqual(3);

      // 验证主要函数存在
      const mainFunc = results[0];
      expect(mainFunc).toBeDefined();
      expect(mainFunc.complexity).toBeGreaterThanOrEqual(0); // 大型复杂组件

      // 验证所有函数的位置信息都正确
      results.forEach(func => {
        expect(func.line).toBeGreaterThan(0);
        expect(func.line).toBeLessThanOrEqual(lines.length);
        expect(func.column).toBeGreaterThanOrEqual(0);

        // 验证详细信息的位置
        if (func.details) {
          const validDetails = func.details.filter(d => d.line > 0);
          validDetails.forEach(detail => {
            expect(detail.line).toBeGreaterThan(0);
            expect(detail.line).toBeLessThanOrEqual(lines.length);
            
            // 验证该行包含实际代码（跳过可能的错误恢复条目）
            const lineContent = lines[detail.line - 1];
            expect(lineContent.trim()).not.toBe('');
          });
        }
      });
    });
  });

  // =============================================================================
  // 4. API 兼容性测试
  // =============================================================================
  
  describe('现有 ComplexityVisitor API 兼容性', () => {
    test('应该保持与现有 API 的完全兼容性', async () => {
      const sourceCode = `function testFunction() {
  if (true) {
    return 'test';
  }
}`;

      const ast = await parser.parseCode(sourceCode, 'api-compat.ts');
      
      // 测试现有的构造函数签名
      const detailCollector1 = new DetailCollector();
      const detailCollector2 = new DetailCollector();
      const detailCollector3 = new DetailCollector();
      
      const visitor1 = new ComplexityVisitor(sourceCode, detailCollector1);
      const visitor2 = new ComplexityVisitor(sourceCode, detailCollector2, options);
      const visitor3 = new ComplexityVisitor(sourceCode, detailCollector3, options, undefined);

      // 测试现有的方法调用
      visitor1.visit(ast as Module);
      visitor2.visit(ast as Module);
      visitor3.visit(ast as Module);

      // 测试结果获取方法
      const results1 = visitor1.getResults();
      const results2 = visitor2.getResults();
      const results3 = visitor3.getResults();

      // 验证结果格式一致
      [results1, results2, results3].forEach(results => {
        expect(results).toHaveLength(1);
        expect(results[0]).toHaveProperty('name');
        expect(results[0]).toHaveProperty('complexity');
        expect(results[0]).toHaveProperty('line');
        expect(results[0]).toHaveProperty('column');
      });

      // 测试详细信息的存在性（根据当前实现，details总是存在）
      expect(results1[0].details).toBeDefined(); // details 总是存在
      expect(results2[0].details).toBeDefined(); // 包含详细信息
      
      // 验证 details 中包含有效的位置信息
      const validDetails1 = results1[0].details?.filter(d => d.line > 0) || [];
      const validDetails2 = results2[0].details?.filter(d => d.line > 0) || [];
      expect(validDetails1.length).toBeGreaterThan(0);
      expect(validDetails2.length).toBeGreaterThan(0);
    });

    test('应该支持现有的错误处理模式', async () => {
      const invalidCode = `function incomplete() {
  if (true {
    // 语法错误：缺少右括号
}`;

      try {
        const ast = await parser.parseCode(invalidCode, 'invalid.ts');
        const detailCollector = new DetailCollector();
        const visitor = new ComplexityVisitor(invalidCode, detailCollector);
        visitor.visit(ast as Module);
        
        // 如果解析成功，应该有优雅的错误恢复
        const results = visitor.getResults();
        expect(Array.isArray(results)).toBe(true);
      } catch (error) {
        // 解析错误应该被正确抛出
        expect(error).toBeDefined();
      }
    });

    test('应该保持现有的性能特征', async () => {
      const mediumCode = `function mediumComplexity() {
  for (let i = 0; i < 10; i++) {
    if (i % 2 === 0) {
      for (let j = 0; j < 5; j++) {
        if (j % 3 === 0) {
          console.log(i, j);
        }
      }
    }
  }
}`.repeat(10); // 重复10次创建中等大小的代码

      const startTime = Date.now();
      
      const ast = await parser.parseCode(mediumCode, 'medium.ts');
      const detailCollector = new DetailCollector();
      const visitor = new ComplexityVisitor(mediumCode, detailCollector, options);
      visitor.visit(ast as Module);
      const results = visitor.getResults();

      const endTime = Date.now();
      const processingTime = endTime - startTime;

      // 性能应该保持在合理范围内（< 1秒）
      expect(processingTime).toBeLessThan(1000);
      expect(results.length).toBe(10); // 10个重复的函数
      
      // 每个函数的复杂度应该相同
      const complexities = results.map(r => r.complexity);
      expect(new Set(complexities).size).toBe(1); // 所有复杂度都相同
    });
  });

  // =============================================================================
  // 5. 边界条件和错误恢复测试
  // =============================================================================
  
  describe('边界条件和错误恢复', () => {
    test('应该处理极端的嵌套层级', async () => {
      // 生成深度嵌套的代码
      const generateDeepNesting = (depth: number) => {
        let code = 'function deeplyNested() {\n';
        
        for (let i = 0; i < depth; i++) {
          code += '  '.repeat(i + 1) + `if (true) {\n`;
        }
        
        code += '  '.repeat(depth + 1) + 'console.log("deep");\n';
        
        for (let i = depth - 1; i >= 0; i--) {
          code += '  '.repeat(i + 1) + '}\n';
        }
        
        code += '}';
        return code;
      };

      const deepCode = generateDeepNesting(20); // 20层嵌套
      
      const ast = await parser.parseCode(deepCode, 'deep-nested.ts');
      const detailCollector = new DetailCollector();
      const visitor = new ComplexityVisitor(deepCode, detailCollector, options);
      visitor.visit(ast as Module);
      const results = visitor.getResults();

      expect(results).toHaveLength(1);
      expect(results[0].complexity).toBeGreaterThan(20); // 深度嵌套会有额外的嵌套惩罚
      
      // 验证所有位置信息都正确
      if (results[0].details) {
        const validDetails = results[0].details.filter(d => d.line > 0);
        const ifStatements = validDetails.filter(d => d.ruleId === 'if-statement');
        expect(ifStatements.length).toBeGreaterThan(15); // 至少有大部分 if 语句被识别
        ifStatements.forEach(detail => {
          expect(detail.line).toBeGreaterThan(0);
          expect(detail.ruleId).toBe('if-statement');
        });
      }
    });

    test('应该处理空文件和只有注释的文件', async () => {
      const emptyCode = '';
      const commentOnlyCode = `// 只有注释的文件
/* 
 * 多行注释
 */
// 另一个注释`;

      // 测试空文件
      const emptyAst = await parser.parseCode(emptyCode, 'empty.ts');
      const emptyDetailCollector = new DetailCollector();
      const emptyVisitor = new ComplexityVisitor(emptyCode, emptyDetailCollector);
      emptyVisitor.visit(emptyAst as Module);
      const emptyResults = emptyVisitor.getResults();
      expect(emptyResults).toHaveLength(0);

      // 测试只有注释的文件
      const commentAst = await parser.parseCode(commentOnlyCode, 'comment-only.ts');
      const commentDetailCollector = new DetailCollector();
      const commentVisitor = new ComplexityVisitor(commentOnlyCode, commentDetailCollector);
      commentVisitor.visit(commentAst as Module);
      const commentResults = commentVisitor.getResults();
      expect(commentResults).toHaveLength(0);
    });

    test('应该处理包含各种 Unicode 字符的代码', async () => {
      const unicodeCode = `function 测试函数() {
  const 变量名 = "中文字符串";
  if (变量名.includes("中文")) {
    console.log("包含中文: " + 变量名);
  }
  
  // Emoji 注释 🚀
  const emoji = "🎉🎊✨";
  return emoji + 变量名;
}`;

      const ast = await parser.parseCode(unicodeCode, 'unicode.ts');
      const detailCollector = new DetailCollector();
      const visitor = new ComplexityVisitor(unicodeCode, detailCollector, options);
      visitor.visit(ast as Module);
      const results = visitor.getResults();

      expect(results).toHaveLength(1);
      expect(results[0].name).toBe('测试函数');
      expect(results[0].complexity).toBe(1); // 只有一个 if 语句
      
      // 验证位置计算正确处理 Unicode 字符
      expect(results[0].line).toBe(1);
      if (results[0].details) {
        const validDetails = results[0].details.filter(d => d.line > 0);
        validDetails.forEach(detail => {
          expect(detail.line).toBeGreaterThan(0);
          const lineContent = unicodeCode.split('\n')[detail.line - 1];
          expect(lineContent).toBeDefined();
        });
      }
    });
  });
});