/**
 * 调试 JSX 节点查找问题
 */

import { describe, test, expect } from 'vitest';
import { ASTParser } from '../../core/parser';

describe('JSX 节点查找调试', () => {
  const parser = new ASTParser();

  test('检查自闭合元素的 AST 结构', async () => {
    const code = `
      function MyComponent() {
        return <img src="test.jpg" alt="test" />;
      }
    `;
    
    const ast = await parser.parseCode(code, 'test.tsx');
    
    // 递归打印 AST 结构来调试
    const printAST = (node: any, depth = 0): void => {
      if (!node || typeof node !== 'object') return;
      
      const indent = '  '.repeat(depth);
      if (node.type) {
        console.log(`${indent}${node.type}`);
        
        // 特别打印 JSX 相关节点
        if (node.type.startsWith('JSX')) {
          console.log(`${indent}  → JSX 节点详情:`, {
            type: node.type,
            hasSpan: !!node.span,
            span: node.span
          });
        }
      }
      
      for (const key in node) {
        if (key === 'span' || key === 'type') continue;
        const value = node[key];
        
        if (Array.isArray(value)) {
          value.forEach(item => printAST(item, depth + 1));
        } else if (value && typeof value === 'object') {
          printAST(value, depth + 1);
        }
      }
    };
    
    console.log('AST 结构:');
    printAST(ast);
    
    expect(ast).toBeTruthy();
  });

  test('查找所有 JSX 相关节点类型', async () => {
    const code = `
      function MyComponent() {
        return (
          <>
            <div className="container">
              <img src="test.jpg" alt="test" />
              <span>{value}</span>
              {condition ? <A /> : <B />}
            </div>
          </>
        );
      }
    `;
    
    const ast = await parser.parseCode(code, 'test.tsx');
    
    const jsxNodes: { type: string; node: any }[] = [];
    
    const findJSXNodes = (node: any) => {
      if (!node || typeof node !== 'object') return;
      
      if (node.type && node.type.startsWith('JSX')) {
        jsxNodes.push({ type: node.type, node });
      }
      
      for (const key in node) {
        if (key === 'span' || key === 'type') continue;
        const value = node[key];
        
        if (Array.isArray(value)) {
          value.forEach(findJSXNodes);
        } else if (value && typeof value === 'object') {
          findJSXNodes(value);
        }
      }
    };
    
    findJSXNodes(ast);
    
    console.log('找到的 JSX 节点类型:', jsxNodes.map(n => n.type));
    
    expect(jsxNodes.length).toBeGreaterThan(0);
  });
});