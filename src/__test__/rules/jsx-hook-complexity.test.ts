/**
 * JSX Hook复杂度分析规则测试
 */

import { describe, test, expect, beforeEach } from 'vitest';
import { JSXHookComplexityRule } from '../../rules/jsx-hook-complexity';
import { parseCodeToAST } from '../helpers/test-utils';
import type { AnalysisContext, ResolvedEngineConfig } from '../../engine/types';
import { DEFAULT_ENGINE_CONFIG } from '../../engine/types';

// 简单的 mock 上下文，与实际实现兼容
function createMockContext(): AnalysisContext {
  return {
    filePath: 'test.tsx',
    fileContent: '',
    ast: {} as any,
    currentFunction: {} as any,
    functionName: 'TestComponent',
    nestingLevel: 0,
    config: DEFAULT_ENGINE_CONFIG,
    jsxMode: 'standard',
    rules: { core: [], jsx: [], plugins: [] },
    cache: {
      getCachedRuleResult: async () => null,
      setCachedRuleResult: async () => {},
    } as any,
    metrics: {
      totalNodes: 0,
      processedNodes: 0,
      cacheHits: 0,
      cacheMisses: 0,
      ruleExecutions: 0,
      parallelExecutions: 0,
      errors: 0,
    },
    plugins: [],
    customData: new Map(),
  };
}

function findCallExpression(ast: any, hookName: string): any {
  let found: any = null;
  
  function traverse(node: any): void {
    if (!node || typeof node !== 'object') return;
    
    if (node.type === 'CallExpression' && node.callee) {
      const callee = node.callee;
      
      // 检查直接调用
      if (callee.type === 'Identifier' && callee.value === hookName) {
        found = node;
        return;
      }
      
      // 检查成员表达式调用 (React.useEffect)
      if (callee.type === 'MemberExpression' && 
          callee.property && callee.property.value === hookName) {
        found = node;
        return;
      }
    }
    
    for (const key in node) {
      if (key === 'span' || key === 'type') continue;
      const value = node[key];
      
      if (Array.isArray(value)) {
        value.forEach(traverse);
      } else if (value && typeof value === 'object') {
        traverse(value);
      }
    }
  }
  
  traverse(ast);
  return found;
}

describe('JSXHookComplexityRule', () => {
  let rule: JSXHookComplexityRule;
  let mockContext: AnalysisContext;

  beforeEach(() => {
    rule = new JSXHookComplexityRule();
    mockContext = createMockContext();
  });

  describe('Rule Identification', () => {
    test('should have correct rule id and priority', () => {
      expect(rule.id).toBe('jsx.hook.complexity');
      expect(rule.name).toBe('JSX Hook Complexity Analysis');
      expect(rule.priority).toBe(700);
    });
  });

  describe('canHandle', () => {
    test('should recognize useEffect calls', () => {
      const code = `useEffect(() => {}, []);`;
      const ast = parseCodeToAST(code);
      const callExpression = findCallExpression(ast, 'useEffect');
      
      if (callExpression) {
        expect(rule.canHandle(callExpression)).toBe(true);
      }
    });

    test('should recognize useCallback calls', () => {
      const code = `useCallback(() => {}, []);`;
      const ast = parseCodeToAST(code);
      const callExpression = findCallExpression(ast, 'useCallback');
      
      if (callExpression) {
        expect(rule.canHandle(callExpression)).toBe(true);
      }
    });

    test('should recognize useMemo calls', () => {
      const code = `useMemo(() => {}, []);`;
      const ast = parseCodeToAST(code);
      const callExpression = findCallExpression(ast, 'useMemo');
      
      if (callExpression) {
        expect(rule.canHandle(callExpression)).toBe(true);
      }
    });

    test('should recognize custom Hook calls', () => {
      const code = `useCustomHook();`;
      const ast = parseCodeToAST(code);
      const callExpression = findCallExpression(ast, 'useCustomHook');
      
      if (callExpression) {
        expect(rule.canHandle(callExpression)).toBe(true);
      }
    });

    test('should ignore non-Hook calls', () => {
      const code = `console.log('test');`;
      const ast = parseCodeToAST(code);
      const callExpression = findCallExpression(ast, 'console');
      
      if (callExpression) {
        expect(rule.canHandle(callExpression)).toBe(false);
      }
    });

    test('should recognize React.useEffect calls', () => {
      const code = `React.useEffect(() => {}, []);`;
      const ast = parseCodeToAST(code);
      const callExpression = findCallExpression(ast, 'useEffect');
      
      if (callExpression) {
        expect(rule.canHandle(callExpression)).toBe(true);
      }
    });
  });

  describe('useEffect Analysis', () => {
    test('simple useEffect should have low complexity', async () => {
      const code = `useEffect(() => { console.log('simple'); }, []);`;
      const ast = parseCodeToAST(code);
      const callExpression = findCallExpression(ast, 'useEffect');
      
      if (callExpression) {
        const result = await rule.evaluate(callExpression, mockContext);
        
        expect(result.complexity).toBeGreaterThanOrEqual(0);
        expect(typeof result.isExempted).toBe('boolean');
      }
    });

    test('useEffect with conditional logic', async () => {
      const code = `
        useEffect(() => {
          if (condition) {
            doSomething();
          } else {
            doSomethingElse();
          }
        }, [condition]);
      `;
      const ast = parseCodeToAST(code);
      const callExpression = findCallExpression(ast, 'useEffect');
      
      if (callExpression) {
        const result = await rule.evaluate(callExpression, mockContext);
        
        expect(result.complexity).toBeGreaterThanOrEqual(0);
      }
    });

    test('useEffect without dependency array', async () => {
      const code = `useEffect(() => { console.log('no deps'); });`;
      const ast = parseCodeToAST(code);
      const callExpression = findCallExpression(ast, 'useEffect');
      
      if (callExpression) {
        const result = await rule.evaluate(callExpression, mockContext);
        
        expect(result.complexity).toBeGreaterThanOrEqual(0);
        expect(result.suggestions.length).toBeGreaterThanOrEqual(0);
      }
    });

    test('useEffect with complex dependencies', async () => {
      const code = `
        useEffect(() => {
          console.log('effect');
        }, [user.profile?.name, calculateValue(), condition ? a : b]);
      `;
      const ast = parseCodeToAST(code);
      const callExpression = findCallExpression(ast, 'useEffect');
      
      if (callExpression) {
        const result = await rule.evaluate(callExpression, mockContext);
        
        expect(result.complexity).toBeGreaterThanOrEqual(0);
      }
    });

    test('useEffect with many dependencies', async () => {
      const code = `
        useEffect(() => {
          console.log('many deps');
        }, [a, b, c, d, e, f, g]);
      `;
      const ast = parseCodeToAST(code);
      const callExpression = findCallExpression(ast, 'useEffect');
      
      if (callExpression) {
        const result = await rule.evaluate(callExpression, mockContext);
        
        expect(result.complexity).toBeGreaterThanOrEqual(0);
      }
    });
  });

  describe('useCallback Analysis', () => {
    test('simple useCallback should have low complexity', async () => {
      const code = `useCallback(() => {}, []);`;
      const ast = parseCodeToAST(code);
      const callExpression = findCallExpression(ast, 'useCallback');
      
      if (callExpression) {
        const result = await rule.evaluate(callExpression, mockContext);
        
        expect(result.complexity).toBeGreaterThanOrEqual(0);
      }
    });

    test('complex useCallback should increase complexity', async () => {
      const code = `
        useCallback(() => {
          if (condition) {
            processData();
          }
          return result;
        }, [condition, data]);
      `;
      const ast = parseCodeToAST(code);
      const callExpression = findCallExpression(ast, 'useCallback');
      
      if (callExpression) {
        const result = await rule.evaluate(callExpression, mockContext);
        
        expect(result.complexity).toBeGreaterThanOrEqual(0);
      }
    });
  });

  describe('useMemo Analysis', () => {
    test('simple useMemo should have low complexity', async () => {
      const code = `useMemo(() => data.length, [data]);`;
      const ast = parseCodeToAST(code);
      const callExpression = findCallExpression(ast, 'useMemo');
      
      if (callExpression) {
        const result = await rule.evaluate(callExpression, mockContext);
        
        expect(result.complexity).toBeGreaterThanOrEqual(0);
      }
    });

    test('complex useMemo computation', async () => {
      const code = `
        useMemo(() => {
          return data.filter(item => item.active)
                    .map(item => transform(item))
                    .sort((a, b) => a.priority - b.priority);
        }, [data]);
      `;
      const ast = parseCodeToAST(code);
      const callExpression = findCallExpression(ast, 'useMemo');
      
      if (callExpression) {
        const result = await rule.evaluate(callExpression, mockContext);
        
        expect(result.complexity).toBeGreaterThanOrEqual(0);
      }
    });
  });

  describe('Custom Hook Analysis', () => {
    test('simple custom Hook', async () => {
      const code = `useCustomHook();`;
      const ast = parseCodeToAST(code);
      const callExpression = findCallExpression(ast, 'useCustomHook');
      
      if (callExpression) {
        const result = await rule.evaluate(callExpression, mockContext);
        
        expect(result.complexity).toBeGreaterThanOrEqual(0);
      }
    });

    test('custom Hook with complex parameters', async () => {
      const code = `useCustomHook(data.map(x => x.id), config.options, isEnabled && hasPermission);`;
      const ast = parseCodeToAST(code);
      const callExpression = findCallExpression(ast, 'useCustomHook');
      
      if (callExpression) {
        const result = await rule.evaluate(callExpression, mockContext);
        
        expect(result.complexity).toBeGreaterThanOrEqual(0);
      }
    });
  });

  describe('State Hook Analysis', () => {
    test('simple useState', async () => {
      const code = `useState(0);`;
      const ast = parseCodeToAST(code);
      const callExpression = findCallExpression(ast, 'useState');
      
      if (callExpression) {
        const result = await rule.evaluate(callExpression, mockContext);
        
        expect(result.complexity).toBeGreaterThanOrEqual(0);
      }
    });

    test('useState with complex initialization', async () => {
      const code = `useState(() => data.reduce((acc, item) => acc + item.value, 0));`;
      const ast = parseCodeToAST(code);
      const callExpression = findCallExpression(ast, 'useState');
      
      if (callExpression) {
        const result = await rule.evaluate(callExpression, mockContext);
        
        expect(result.complexity).toBeGreaterThanOrEqual(0);
      }
    });
  });

  describe('Edge Cases', () => {
    test('should handle incomplete Hook calls', async () => {
      const incompleteNode = {
        type: 'CallExpression',
        callee: null,
        arguments: []
      } as any;

      const result = await rule.evaluate(incompleteNode, mockContext);
      
      expect(result.complexity).toBe(0);
      expect(result.reason).toContain('Not a valid hook call');
    });

    test('should handle invalid Hook call nodes', async () => {
      const invalidNode = {
        type: 'CallExpression',
        callee: {
          type: 'Identifier',
          value: 'notAHook'
        },
        arguments: []
      } as any;

      const result = await rule.evaluate(invalidNode, mockContext);
      
      expect(result.complexity).toBe(0);
      expect(result.reason).toContain('Not a valid hook call');
    });
  });
});