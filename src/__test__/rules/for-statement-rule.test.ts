/**
 * ForStatementRule测试用例
 * 验证for循环语句复杂度计算的正确性
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { ForStatementRule } from '../../rules/for-statement-rule';
import type { AnalysisContext } from '../../engine/types';
import { parse } from '@swc/core';

// 创建模拟的分析上下文
function createMockContext(nestingLevel: number = 0): AnalysisContext {
  return {
    filePath: 'test.ts',
    fileContent: '',
    ast: {} as any,
    functionName: 'testFunction',
    nestingLevel,
    config: {} as any,
    jsxMode: 'standard',
    rules: { core: [], jsx: [], plugins: [] },
    cache: {
      getCachedRuleResult: async () => null,
      setCachedRuleResult: async () => {},
    } as any,
    metrics: {} as any,
    plugins: [],
    customData: new Map(),
  };
}

// 解析代码并获取第一个循环语句节点
async function parseForStatement(code: string): Promise<any> {
  const result = await parse(code, {
    syntax: 'typescript',
    target: 'es2022',
  });

  // 查找循环语句节点
  function findForStatement(node: any): any {
    if (node?.type === 'ForStatement' || 
        node?.type === 'ForInStatement' || 
        node?.type === 'ForOfStatement') {
      return node;
    }
    
    if (node && typeof node === 'object') {
      for (const key in node) {
        if (key !== 'parent') {
          const child = node[key];
          if (Array.isArray(child)) {
            for (const item of child) {
              const found = findForStatement(item);
              if (found) return found;
            }
          } else {
            const found = findForStatement(child);
            if (found) return found;
          }
        }
      }
    }
    
    return null;
  }

  return findForStatement(result);
}

describe('ForStatementRule', () => {
  let rule: ForStatementRule;

  beforeEach(() => {
    rule = new ForStatementRule();
  });

  describe('基本属性', () => {
    it('应该有正确的规则ID和名称', () => {
      expect(rule.id).toBe('for-statement');
      expect(rule.name).toBe('For Statement Complexity');
      expect(rule.priority).toBe(450);
    });
  });

  describe('canHandle方法', () => {
    it('应该识别for语句', async () => {
      const forNode = await parseForStatement(`
        function test() {
          for (let i = 0; i < 10; i++) {
            console.log(i);
          }
        }
      `);

      expect(rule.canHandle(forNode)).toBe(true);
    });

    it('应该识别for-in语句', async () => {
      const forInNode = await parseForStatement(`
        function test() {
          for (const key in object) {
            console.log(key);
          }
        }
      `);

      expect(rule.canHandle(forInNode)).toBe(true);
    });

    it('应该识别for-of语句', async () => {
      const forOfNode = await parseForStatement(`
        function test() {
          for (const item of array) {
            console.log(item);
          }
        }
      `);

      expect(rule.canHandle(forOfNode)).toBe(true);
    });

    it('应该拒绝非循环语句节点', async () => {
      const ifNode = { type: 'IfStatement' } as any;
      const whileNode = { type: 'WhileStatement' } as any;

      expect(rule.canHandle(ifNode)).toBe(false);
      expect(rule.canHandle(whileNode)).toBe(false);
    });
  });

  describe('传统for循环', () => {
    it('应该为简单for循环返回基础复杂度1', async () => {
      const forNode = await parseForStatement(`
        function test() {
          for (let i = 0; i < 10; i++) {
            console.log(i);
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(forNode, context);

      expect(result.complexity).toBe(1);
      expect(result.shouldIncreaseNesting).toBe(true);
      expect(result.reason).toContain('For loop increases cognitive complexity by 1');
      expect(result.metadata?.loopType).toBe('for-loop');
    });

    it('应该为带嵌套的for循环应用嵌套惩罚', async () => {
      const forNode = await parseForStatement(`
        function test() {
          if (condition) {
            for (let i = 0; i < 10; i++) {
              console.log(i);
            }
          }
        }
      `);

      const context = createMockContext(2); // 嵌套层级为2
      const result = await rule.evaluate(forNode, context);

      expect(result.complexity).toBe(3); // 基础1 + 嵌套惩罚2
      expect(result.metadata?.nestingLevel).toBe(2);
      expect(result.reason).toContain('nesting penalty: +2');
    });

    it('应该检测复杂的循环条件', async () => {
      const forNode = await parseForStatement(`
        function test() {
          for (let i = 0; i < array.length && isValid(i) && !stopped; i++) {
            process(i);
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(forNode, context);

      expect(result.complexity).toBe(1);
      expect(result.metadata?.hasComplexCondition).toBe(true);
      expect(result.reason).toContain('complex condition detected');
    });

    it('应该检测复杂的更新表达式', async () => {
      const forNode = await parseForStatement(`
        function test() {
          for (let i = 0; i < 10; i = calculateNext(i)) {
            process(i);
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(forNode, context);

      expect(result.complexity).toBe(1);
      expect(result.metadata?.hasComplexUpdate).toBe(true);
      expect(result.reason).toContain('complex update expression');
    });
  });

  describe('for-in循环', () => {
    it('应该为for-in循环返回基础复杂度1', async () => {
      const forInNode = await parseForStatement(`
        function test() {
          for (const key in object) {
            console.log(key, object[key]);
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(forInNode, context);

      expect(result.complexity).toBe(1);
      expect(result.shouldIncreaseNesting).toBe(true);
      expect(result.reason).toContain('For-in loop increases cognitive complexity by 1');
      expect(result.metadata?.loopType).toBe('for-in-loop');
    });

    it('应该为带嵌套的for-in循环应用嵌套惩罚', async () => {
      const forInNode = await parseForStatement(`
        function test() {
          if (condition) {
            for (const key in object) {
              process(key);
            }
          }
        }
      `);

      const context = createMockContext(1);
      const result = await rule.evaluate(forInNode, context);

      expect(result.complexity).toBe(2); // 基础1 + 嵌套惩罚1
      expect(result.metadata?.nestingLevel).toBe(1);
    });
  });

  describe('for-of循环', () => {
    it('应该为for-of循环返回基础复杂度1', async () => {
      const forOfNode = await parseForStatement(`
        function test() {
          for (const item of array) {
            console.log(item);
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(forOfNode, context);

      expect(result.complexity).toBe(1);
      expect(result.shouldIncreaseNesting).toBe(true);
      expect(result.reason).toContain('For-of loop increases cognitive complexity by 1');
      expect(result.metadata?.loopType).toBe('for-of-loop');
    });

    it('应该为带嵌套的for-of循环应用嵌套惩罚', async () => {
      const forOfNode = await parseForStatement(`
        function test() {
          for (const outer of outerArray) {
            for (const inner of outer.items) {
              process(inner);
            }
          }
        }
      `);

      const context = createMockContext(1); // 内层循环的嵌套层级
      const result = await rule.evaluate(forOfNode, context);

      expect(result.complexity).toBe(2); // 基础1 + 嵌套惩罚1
      expect(result.metadata?.nestingLevel).toBe(1);
    });
  });

  describe('建议生成', () => {
    it('应该为复杂条件生成重构建议', async () => {
      const forNode = await parseForStatement(`
        function test() {
          for (let i = 0; i < arr.length && isValid(arr[i]) && !stopped; i++) {
            process(arr[i]);
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(forNode, context);

      const conditionSuggestion = result.suggestions.find(s => 
        s.message.includes('Complex loop condition')
      );
      expect(conditionSuggestion).toBeDefined();
      expect(conditionSuggestion?.type).toBe('refactor');
    });

    it('应该为复杂更新表达式生成重构建议', async () => {
      const forNode = await parseForStatement(`
        function test() {
          for (let i = 0; i < 10; i = calculateNextIndex(i, step)) {
            process(i);
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(forNode, context);

      const updateSuggestion = result.suggestions.find(s => 
        s.message.includes('Complex update expression')
      );
      expect(updateSuggestion).toBeDefined();
      expect(updateSuggestion?.type).toBe('refactor');
    });

    it('应该为深度嵌套生成重构建议', async () => {
      const forNode = await parseForStatement(`
        function test() {
          for (const item of items) {
            process(item);
          }
        }
      `);

      const context = createMockContext(3); // 深度嵌套
      const result = await rule.evaluate(forNode, context);

      const nestingSuggestion = result.suggestions.find(s => 
        s.message.includes('extracting nested loop logic')
      );
      expect(nestingSuggestion).toBeDefined();
      expect(nestingSuggestion?.type).toBe('refactor');
    });

    it('应该为简单数组遍历生成现代语法建议', async () => {
      const forNode = await parseForStatement(`
        function test() {
          for (let i = 0; i < array.length; i++) {
            results.push(process(array[i]));
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(forNode, context);

      const modernSuggestion = result.suggestions.find(s => 
        s.message.includes('modern array methods')
      );
      expect(modernSuggestion).toBeDefined();
      expect(modernSuggestion?.type).toBe('refactor');
    });
  });

  describe('元数据验证', () => {
    it('应该提供完整的元数据信息', async () => {
      const forNode = await parseForStatement(`
        function test() {
          for (let i = 0; i < array.length && active; i++) {
            process(i);
          }
        }
      `);

      const context = createMockContext(1);
      const result = await rule.evaluate(forNode, context);

      expect(result.metadata).toMatchObject({
        nodeType: 'ForStatement',
        baseComplexity: 1,
        nestingLevel: 1,
        loopType: 'for-loop',
        hasComplexCondition: true,
        hasComplexUpdate: false,
      });
    });
  });

  describe('边界条件', () => {
    it('应该处理空的for循环', async () => {
      const forNode = await parseForStatement(`
        function test() {
          for (let i = 0; i < 10; i++) {
            // 空循环体
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(forNode, context);

      expect(result.complexity).toBe(1);
      expect(result.isExempted).toBe(false);
    });

    it('应该处理无限循环的情况', async () => {
      const forNode = await parseForStatement(`
        function test() {
          for (;;) {
            if (shouldBreak) break;
            process();
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(forNode, context);

      expect(result.complexity).toBe(1);
      expect(result.metadata?.hasComplexCondition).toBe(false);
    });
  });
});