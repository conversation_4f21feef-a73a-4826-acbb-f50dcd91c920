/**
 * ConditionalExpressionRule测试用例
 * 验证三元运算符复杂度计算的正确性
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { ConditionalExpressionRule } from '../../rules/conditional-expression-rule';
import type { AnalysisContext } from '../../engine/types';
import { parse } from '@swc/core';

// 创建模拟的分析上下文
function createMockContext(nestingLevel: number = 0): AnalysisContext {
  return {
    filePath: 'test.ts',
    fileContent: '',
    ast: {} as any,
    functionName: 'testFunction',
    nestingLevel,
    config: {} as any,
    jsxMode: 'standard',
    rules: { core: [], jsx: [], plugins: [] },
    cache: {
      getCachedRuleResult: async () => null,
      setCachedRuleResult: async () => {},
    } as any,
    metrics: {} as any,
    plugins: [],
    customData: new Map(),
  };
}

// 解析代码并获取第一个条件表达式节点
async function parseConditionalExpression(code: string): Promise<any> {
  const result = await parse(code, {
    syntax: 'typescript',
    target: 'es2022',
  });

  // 查找条件表达式节点
  function findConditionalExpression(node: any): any {
    if (node?.type === 'ConditionalExpression') {
      return node;
    }
    
    if (node && typeof node === 'object') {
      for (const key in node) {
        if (key !== 'parent') {
          const child = node[key];
          if (Array.isArray(child)) {
            for (const item of child) {
              const found = findConditionalExpression(item);
              if (found) return found;
            }
          } else {
            const found = findConditionalExpression(child);
            if (found) return found;
          }
        }
      }
    }
    
    return null;
  }

  return findConditionalExpression(result);
}

describe('ConditionalExpressionRule', () => {
  let rule: ConditionalExpressionRule;

  beforeEach(() => {
    rule = new ConditionalExpressionRule();
  });

  describe('基本属性', () => {
    it('应该有正确的规则ID和名称', () => {
      expect(rule.id).toBe('conditional-expression');
      expect(rule.name).toBe('Conditional Expression Complexity');
      expect(rule.priority).toBe(400);
    });
  });

  describe('canHandle方法', () => {
    it('应该识别条件表达式', async () => {
      const conditionalNode = await parseConditionalExpression(`
        function test() {
          const result = condition ? value1 : value2;
        }
      `);

      expect(rule.canHandle(conditionalNode)).toBe(true);
    });

    it('应该拒绝非条件表达式节点', async () => {
      const ifNode = { type: 'IfStatement' } as any;
      const forNode = { type: 'ForStatement' } as any;

      expect(rule.canHandle(ifNode)).toBe(false);
      expect(rule.canHandle(forNode)).toBe(false);
    });
  });

  describe('简单条件表达式', () => {
    it('应该为简单三元运算符返回基础复杂度1', async () => {
      const conditionalNode = await parseConditionalExpression(`
        function test() {
          const result = condition ? value1 : value2;
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(conditionalNode, context);

      expect(result.complexity).toBe(1);
      expect(result.shouldIncreaseNesting).toBe(false); // 三元运算符不增加嵌套层级
      expect(result.reason).toContain('Ternary operator increases cognitive complexity by 1');
      expect(result.metadata?.nodeType).toBe('ConditionalExpression');
      expect(result.metadata?.baseComplexity).toBe(1);
    });

    it('应该为带嵌套的三元运算符应用嵌套惩罚', async () => {
      const conditionalNode = await parseConditionalExpression(`
        function test() {
          if (outerCondition) {
            const result = condition ? value1 : value2;
          }
        }
      `);

      const context = createMockContext(2); // 嵌套层级为2
      const result = await rule.evaluate(conditionalNode, context);

      expect(result.complexity).toBe(3); // 基础1 + 嵌套惩罚2
      expect(result.metadata?.nestingLevel).toBe(2);
      expect(result.reason).toContain('nesting penalty: +2');
    });
  });

  describe('复杂条件分析', () => {
    it('应该检测复杂的条件表达式', async () => {
      const conditionalNode = await parseConditionalExpression(`
        function test() {
          const result = isValid(data) && data.length > 0 ? processData(data) : defaultValue;
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(conditionalNode, context);

      expect(result.complexity).toBe(1);
      expect(result.metadata?.hasComplexCondition).toBe(true);
      expect(result.reason).toContain('complex condition detected');
      expect(result.metadata?.conditionComplexity).toBeGreaterThan(0);
    });

    it('应该检测嵌套的三元运算符', async () => {
      const conditionalNode = await parseConditionalExpression(`
        function test() {
          const result = condition1 ? (condition2 ? value1 : value2) : value3;
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(conditionalNode, context);

      expect(result.complexity).toBe(1);
      expect(result.metadata?.hasNestedConditionals).toBe(true);
      expect(result.reason).toContain('contains nested ternary operators');
    });

    it('应该计算包含多个逻辑运算符的条件复杂度', async () => {
      const conditionalNode = await parseConditionalExpression(`
        function test() {
          const result = a && b || c ? value1 : value2;
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(conditionalNode, context);

      expect(result.metadata?.hasComplexCondition).toBe(true);
      expect(result.metadata?.conditionComplexity).toBeGreaterThan(1);
    });

    it('应该计算包含函数调用的条件复杂度', async () => {
      const conditionalNode = await parseConditionalExpression(`
        function test() {
          const result = isValid(data) && checkPermission(user) ? processData() : getDefault();
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(conditionalNode, context);

      expect(result.metadata?.hasComplexCondition).toBe(true);
      expect(result.metadata?.conditionComplexity).toBeGreaterThan(0);
    });
  });

  describe('建议生成', () => {
    it('应该为复杂条件生成重构建议', async () => {
      const conditionalNode = await parseConditionalExpression(`
        function test() {
          const result = isValid(data) && data.length > 0 && !stopped ? processData(data) : defaultValue;
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(conditionalNode, context);

      const conditionSuggestion = result.suggestions.find(s => 
        s.message.includes('Complex ternary condition')
      );
      expect(conditionSuggestion).toBeDefined();
      expect(conditionSuggestion?.type).toBe('refactor');
    });

    it('应该为嵌套三元运算符生成重构建议', async () => {
      const conditionalNode = await parseConditionalExpression(`
        function test() {
          const result = condition1 ? (condition2 ? value1 : value2) : (condition3 ? value3 : value4);
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(conditionalNode, context);

      const nestingSuggestion = result.suggestions.find(s => 
        s.message.includes('Nested ternary operators detected')
      );
      expect(nestingSuggestion).toBeDefined();
      expect(nestingSuggestion?.type).toBe('refactor');
      expect(nestingSuggestion?.priority).toBe('high');
    });

    it('应该为多个函数调用生成性能建议', async () => {
      const conditionalNode = await parseConditionalExpression(`
        function test() {
          const result = isValid(item) && hasPermission(user) ? processItem(item) : getDefaultValue();
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(conditionalNode, context);

      const performanceSuggestion = result.suggestions.find(s => 
        s.message.includes('Multiple function calls')
      );
      expect(performanceSuggestion).toBeDefined();
      expect(performanceSuggestion?.type).toBe('optimize');
    });

    it('应该为深度嵌套生成重构建议', async () => {
      const conditionalNode = await parseConditionalExpression(`
        function test() {
          const result = condition ? value1 : value2;
        }
      `);

      const context = createMockContext(3); // 深度嵌套
      const result = await rule.evaluate(conditionalNode, context);

      const nestingSuggestion = result.suggestions.find(s => 
        s.message.includes('extracting nested ternary logic')
      );
      expect(nestingSuggestion).toBeDefined();
      expect(nestingSuggestion?.type).toBe('refactor');
    });

    it('应该为简单三元运算符提供信息性建议', async () => {
      const conditionalNode = await parseConditionalExpression(`
        function test() {
          const status = isActive ? 'active' : 'inactive';
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(conditionalNode, context);

      const infoSuggestion = result.suggestions.find(s => 
        s.message.includes('Simple ternary operator')
      );
      expect(infoSuggestion).toBeDefined();
      expect(infoSuggestion?.type).toBe('info');
      expect(infoSuggestion?.priority).toBe('low');
    });
  });

  describe('元数据验证', () => {
    it('应该提供完整的元数据信息', async () => {
      const conditionalNode = await parseConditionalExpression(`
        function test() {
          const result = isValid(data) && data.active ? processData(data) : getDefault();
        }
      `);

      const context = createMockContext(1);
      const result = await rule.evaluate(conditionalNode, context);

      expect(result.metadata).toMatchObject({
        nodeType: 'ConditionalExpression',
        baseComplexity: 1,
        nestingLevel: 1,
        hasComplexCondition: true,
        hasNestedConditionals: false,
      });
      expect(result.metadata?.conditionComplexity).toBeGreaterThan(0);
    });
  });

  describe('边界条件', () => {
    it('应该处理空条件的三元运算符', async () => {
      // 注意：这种语法在实际代码中是不合法的，但测试边界情况
      const conditionalNode = {
        type: 'ConditionalExpression',
        test: null,
        consequent: { type: 'Identifier', value: 'value1' },
        alternate: { type: 'Identifier', value: 'value2' }
      } as any;

      const context = createMockContext(0);
      const result = await rule.evaluate(conditionalNode, context);

      expect(result.complexity).toBe(1);
      expect(result.metadata?.hasComplexCondition).toBe(false);
      expect(result.metadata?.conditionComplexity).toBe(0);
    });

    it('应该处理简单标识符条件', async () => {
      const conditionalNode = await parseConditionalExpression(`
        function test() {
          const result = condition ? value1 : value2;
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(conditionalNode, context);

      expect(result.complexity).toBe(1);
      expect(result.metadata?.hasComplexCondition).toBe(false);
    });

    it('应该处理布尔字面量条件', async () => {
      const conditionalNode = await parseConditionalExpression(`
        function test() {
          const result = true ? value1 : value2;
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(conditionalNode, context);

      expect(result.complexity).toBe(1);
      expect(result.metadata?.hasComplexCondition).toBe(false);
    });

    it('应该处理复杂的成员表达式链', async () => {
      const conditionalNode = await parseConditionalExpression(`
        function test() {
          const result = obj.deep.property.chain ? value1 : value2;
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(conditionalNode, context);

      expect(result.complexity).toBe(1);
      expect(result.metadata?.hasComplexCondition).toBe(true);
    });
  });
});