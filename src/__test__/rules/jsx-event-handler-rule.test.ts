/**
 * JSX事件处理器规则测试用例
 * 验证事件处理器复杂度分析的准确性
 */

import { describe, test, expect, beforeEach } from 'vitest';
import { JSXEventHandlerRule } from '../../rules/jsx-event-handler-rule';
import type { AnalysisContext } from '../../engine/types';
import { createMockAnalysisContext, parseCodeToAST } from '../helpers/test-utils';

function findJSXEventAttribute(ast: any, eventName: string): any {
  let found: any = null;
  
  function traverse(node: any): void {
    if (!node || typeof node !== 'object') return;
    
    if (node.type === 'JSXAttribute' && 
        node.name?.type === 'Identifier' && 
        node.name?.value === eventName) {
      found = node;
      return;
    }
    
    for (const key in node) {
      if (key === 'span' || key === 'type') continue;
      const value = node[key];
      
      if (Array.isArray(value)) {
        value.forEach(traverse);
      } else if (value && typeof value === 'object') {
        traverse(value);
      }
    }
  }
  
  traverse(ast);
  return found;
}

function findArrowFunction(ast: any): any {
  let found: any = null;
  
  function traverse(node: any): void {
    if (!node || typeof node !== 'object') return;
    
    if (node.type === 'ArrowFunctionExpression') {
      found = node;
      return;
    }
    
    for (const key in node) {
      if (key === 'span' || key === 'type') continue;
      const value = node[key];
      
      if (Array.isArray(value)) {
        value.forEach(traverse);
      } else if (value && typeof value === 'object') {
        traverse(value);
      }
    }
  }
  
  traverse(ast);
  return found;
}

describe('JSXEventHandlerRule', () => {
  let rule: JSXEventHandlerRule;
  let context: AnalysisContext;

  beforeEach(() => {
    rule = new JSXEventHandlerRule();
    context = createMockAnalysisContext({
      jsx: {
        enabled: true,
        scoring: {
          eventHandlers: true,
        },
      },
    });
  });

  describe('Rule Identification', () => {
    test('should have correct rule id and priority', () => {
      expect(rule.id).toBe('jsx.event.handler');
      expect(rule.name).toBe('JSX Event Handler Analysis');
      expect(rule.priority).toBe(750);
    });

    test('should not have dependencies', () => {
      expect(rule.getDependencies()).toEqual([]);
    });
  });

  describe('Handler Detection', () => {
    test('should detect JSX event attributes', async () => {
      const code = `<button onClick={() => console.log('clicked')} />`;
      const ast = parseCodeToAST(code);
      const jsxAttribute = findJSXEventAttribute(ast, 'onClick');
      
      if (jsxAttribute) {
        expect(rule.canHandle(jsxAttribute)).toBe(true);
      } else {
        // If we can't find the JSX attribute, test the arrow function directly
        const arrowFunc = findArrowFunction(ast);
        expect(rule.canHandle(arrowFunc)).toBe(true);
      }
    });

    test('should detect various event types', async () => {
      const eventTypes = ['onClick', 'onMouseDown', 'onKeyPress', 'onChange'];

      for (const eventType of eventTypes) {
        const code = `<button ${eventType}={() => {}} />`;
        const ast = parseCodeToAST(code);
        const jsxAttribute = findJSXEventAttribute(ast, eventType);
        
        if (jsxAttribute) {
          expect(rule.canHandle(jsxAttribute)).toBe(true);
        }
      }
    });

    test('should not handle non-event attributes', async () => {
      const code = `<button className="btn" data-test="value" />`;
      const ast = parseCodeToAST(code);
      
      function findNonEventAttribute(ast: any, attrName: string): any {
        let found: any = null;
        
        function traverse(node: any): void {
          if (!node || typeof node !== 'object') return;
          
          if (node.type === 'JSXAttribute' && 
              node.name?.type === 'Identifier' && 
              node.name?.value === attrName) {
            found = node;
            return;
          }
          
          for (const key in node) {
            if (key === 'span' || key === 'type') continue;
            const value = node[key];
            
            if (Array.isArray(value)) {
              value.forEach(traverse);
            } else if (value && typeof value === 'object') {
              traverse(value);
            }
          }
        }
        
        traverse(ast);
        return found;
      }
      
      const classAttribute = findNonEventAttribute(ast, 'className');
      const dataAttribute = findNonEventAttribute(ast, 'data-test');
      
      if (classAttribute) {
        expect(rule.canHandle(classAttribute)).toBe(false);
      }
      if (dataAttribute) {
        expect(rule.canHandle(dataAttribute)).toBe(false);
      }
    });
  });

  describe('Simple Inline Handlers', () => {
    test('should analyze simple arrow function', async () => {
      const code = `<button onClick={() => console.log('test')} />`;
      const ast = parseCodeToAST(code);
      const arrowFunc = findArrowFunction(ast);
      
      if (arrowFunc) {
        const result = await rule.evaluate(arrowFunc, context);
        
        expect(result.complexity).toBeGreaterThanOrEqual(1);
        expect(result.isExempted).toBe(false);
      }
    });

    test('should handle disabled configuration', async () => {
      const disabledContext = createMockAnalysisContext({
        jsx: {
          enabled: false,
        },
      });

      const code = `<button onClick={() => console.log('test')} />`;
      const ast = parseCodeToAST(code);
      const arrowFunc = findArrowFunction(ast);
      
      if (arrowFunc) {
        const result = await rule.evaluate(arrowFunc, disabledContext);
        
        expect(result.complexity).toBe(0);
        expect(result.reason).toContain('disabled');
      }
    });
  });

  describe('Complex Handlers', () => {
    test('should analyze complex inline handlers', async () => {
      const code = `
        <button onClick={() => {
          console.log('start');
          setCount(count + 1);
          setVisible(true);
          console.log('end');
        }} />
      `;
      const ast = parseCodeToAST(code);
      const arrowFunc = findArrowFunction(ast);
      
      if (arrowFunc) {
        const result = await rule.evaluate(arrowFunc, context);
        
        // Complex handlers should have higher complexity
        expect(result.complexity).toBeGreaterThan(1);
      }
    });

    test('should detect async handlers', async () => {
      const code = `
        <button onClick={async () => {
          const result = await fetch('/api/data');
          setData(result);
        }} />
      `;
      const ast = parseCodeToAST(code);
      const arrowFunc = findArrowFunction(ast);
      
      if (arrowFunc) {
        const result = await rule.evaluate(arrowFunc, context);
        
        // Async handlers should have complexity
        expect(result.complexity).toBeGreaterThan(0);
      }
    });
  });

  describe('Performance Patterns', () => {
    test('should detect expensive operations', async () => {
      const code = `
        <button onClick={() => {
          data.map(item => item.value).filter(v => v > 0).sort();
        }} />
      `;
      const ast = parseCodeToAST(code);
      const arrowFunc = findArrowFunction(ast);
      
      if (arrowFunc) {
        const result = await rule.evaluate(arrowFunc, context);
        
        // Should detect performance issues
        expect(result.complexity).toBeGreaterThanOrEqual(1);
      }
    });

    test('should detect loop operations', async () => {
      const code = `
        <button onClick={() => {
          for (let i = 0; i < 1000; i++) {
            console.log(i);
          }
        }} />
      `;
      const ast = parseCodeToAST(code);
      const arrowFunc = findArrowFunction(ast);
      
      if (arrowFunc) {
        const result = await rule.evaluate(arrowFunc, context);
        
        // Should detect expensive loops
        expect(result.complexity).toBeGreaterThanOrEqual(1);
      }
    });
  });

  describe('Suggestions', () => {
    test('should generate suggestions for complex handlers', async () => {
      const code = `
        <button onClick={() => {
          console.log('complex logic');
          setCount(count + 1);
          setVisible(true);
        }} />
      `;
      const ast = parseCodeToAST(code);
      const arrowFunc = findArrowFunction(ast);
      
      if (arrowFunc) {
        const result = await rule.evaluate(arrowFunc, context);
        
        // Complex handlers should generate suggestions
        expect(result.suggestions.length).toBeGreaterThanOrEqual(0);
      }
    });
  });

  describe('Edge Cases', () => {
    test('should handle function references', async () => {
      const code = `<button onClick={handleClick} />`;
      const ast = parseCodeToAST(code);
      
      function findIdentifier(ast: any): any {
        let found: any = null;
        
        function traverse(node: any): void {
          if (!node || typeof node !== 'object') return;
          
          if (node.type === 'Identifier' && node.value === 'handleClick') {
            found = node;
            return;
          }
          
          for (const key in node) {
            if (key === 'span' || key === 'type') continue;
            const value = node[key];
            
            if (Array.isArray(value)) {
              value.forEach(traverse);
            } else if (value && typeof value === 'object') {
              traverse(value);
            }
          }
        }
        
        traverse(ast);
        return found;
      }
      
      const identifier = findIdentifier(ast);
      
      if (identifier) {
        const result = await rule.evaluate(identifier, context);
        
        // Function references should be handled
        expect(result.complexity).toBeGreaterThanOrEqual(0);
      }
    });

    test('should handle bound methods', async () => {
      const code = `<button onClick={this.handleClick.bind(this)} />`;
      const ast = parseCodeToAST(code);
      
      function findCallExpression(ast: any): any {
        let found: any = null;
        
        function traverse(node: any): void {
          if (!node || typeof node !== 'object') return;
          
          if (node.type === 'CallExpression') {
            found = node;
            return;
          }
          
          for (const key in node) {
            if (key === 'span' || key === 'type') continue;
            const value = node[key];
            
            if (Array.isArray(value)) {
              value.forEach(traverse);
            } else if (value && typeof value === 'object') {
              traverse(value);
            }
          }
        }
        
        traverse(ast);
        return found;
      }
      
      const callExpr = findCallExpression(ast);
      
      if (callExpr) {
        const result = await rule.evaluate(callExpr, context);
        
        // Bound methods should be handled
        expect(result.complexity).toBeGreaterThanOrEqual(0);
      }
    });
  });
});