/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { Ellipsis, enqueueSnackbar, useFireExport } from '@imile/components';
import { useEffect, useMemo } from 'react';

interface NDRProps {
  refreshKey?: number;
}

const bizType = 201117; // NDR业务类型

const Ndr: React.FC<NDRProps> = ({ refreshKey }) => {
  const { t } = useTranslation();
  const fireExport = useFireExport();
  const dispatcher = useDispatchers(NDRModule);
  const ndrStatConfig = useGetNDRStatsConfig();
  const showEmpty =
    ndrStatConfig.Interception === false && ndrStatConfig.NDR === false && ndrStatConfig.ScheduleForFuture === false;

  const currentCountry = useCurrentCountry();
  const { currentOcCode } = useCurrentOcCode();

  const [{ queryParams, selectedStatus, selectedRows }] = useModule(NDRModule, {
    selector: (s) => ({
      queryParams: {
        ...s.queryParams,
        ocCode: currentOcCode,
        country: currentCountry,
      },
      selectedStatus: s.selectedStatus,
      selectedRows: s.selectedRows,
    }),
    dependencies: [currentCountry, currentOcCode],
    equalFn: isEqual,
  });

  const columns = [
    {
      title: t('运单号'),
      dataIndex: 'waybillNo',
      width: 180,
      fixed: 'left',
      render: (text: string) => <Ellipsis>{text || '--'}</Ellipsis>,
    },
    {
      title: <div style={{ textAlign: 'left', width: '100%' }}>{t('收件信息')}</div>,
      dataIndex: 'recipientInfo',
      children: [
        {
          title: t('城市'),
          dataIndex: 'consigneeCity',
          width: 120,
          render: (text: string) => <Ellipsis>{text || '--'}</Ellipsis>,
        },
        {
          title: t('电话'),
          dataIndex: 'consigneeMobile',
          width: 180,
          render: (text: string) => <Ellipsis>{text || '--'}</Ellipsis>,
        },
      ],
    },
    {
      title: t('运单清关交邮时间'),
      dataIndex: 'clearanceDate',
      width: 180,
      render: (text: string) => <Ellipsis>{text || '--'}</Ellipsis>,
    },
  ];

  const rowSelection = {
    onChange: (selectedRowKeys?: string[] | number[] | undefined, selectedRows?: any[] | undefined) => {
      if (selectedRows) {
        dispatcher.setSelectedRows(selectedRows);
      }
    },
    selectedRowKeys: selectedRows.map?.((i) => i?.waybillNo),
  };

  const handleExport = (type: 'selected' | 'all') => {
    const exportParams = handleExportParams(formatedQueryParams);

    if (type === 'selected') {
      if (selectedRows.length === 0) {
        enqueueSnackbar(t('请先选中'), { variant: 'info' });
        return;
      }

      fireExport({
        exportParams: {
          bizType,
          jobName: 'NDR', // TODO: 国际化
          queryContent: {
            ...exportParams,
            ids: selectedRows.map((i) => i.waybillNo).join(','),
          },
          system: 'tms',
        },
      });
    }

    if (type === 'all') {
      if (!tableProps.dataSource?.length) {
        enqueueSnackbar(t('未查询到数据'), { variant: 'info' });
        return;
      }

      fireExport({
        exportParams: {
          bizType,
          jobName: 'NDR', // TODO: 国际化
          queryContent: { ...exportParams },
          system: 'tms',
        },
      });
    }
  };

  return (
    <TableQueryContext.Provider value={query}>
      {/* <DataFlowNotice /> */}
      <Notice
        pageKey="NDR"
        unExpandedContent={<>{t('1、拦截状态订单：——NDR status-temporary storage')}</>}
        expandedContent={
          <>
            <br />
            {t('2、预约派件订单：——NDR status-temporary storage')}
            <br />
            {t('3、NDR等待订单：——NDR status-temporary storage')}
            <br />
          </>
        }
      />

      {showEmpty && (
        <>
          <EmptyState />
        </>
      )}

      {!showEmpty && (
        <>
          {/* 卡片筛选 */}
          <StatsPanel />

          <PageTitle onExport={handleExport} />

          <ProPage.TableBox>
            <Table
              columns={columns}
              {...tableProps}
              rowKey="waybillNo"
              rowSelection={rowSelection}
              sx={{
                '.table-header-cell-parent': { justifyContent: 'flex-start !important' },
                width: '100%',
                overflowX: 'auto',
              }}
            />
          </ProPage.TableBox>
        </>
      )}
    </TableQueryContext.Provider>
  );
};

export default Ndr;
