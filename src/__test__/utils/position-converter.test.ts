import { PositionConverter, Position } from '../../utils/position-converter';

describe('PositionConverter', () => {
  const sampleCode = `function hello() {
  console.log('Hello');
  return true;
}`;

  // L0 层语义分析测试的示例代码
  const modernCode = `// 这是注释
import React from 'react';
import { useState } from 'react';

type Props = {
  name: string;
};

export default function Component({ name }: Props) {
  const [count, setCount] = useState(0);
  
  // 另一个注释
  if (count > 5) {
    return <div>Too many!</div>;
  }
  
  return (
    <div>
      {/* JSX 注释 */}
      <h1>Hello {name}</h1>
      <button onClick={() => setCount(count + 1)}>
        Count: {count}
      </button>
    </div>
  );
}`;

  beforeEach(() => {
    // 每个测试前清除缓存，确保测试独立性
    PositionConverter.clearCache();
  });

  describe('spanToPosition', () => {
    it('should convert span start to correct line and column', () => {
      // Position at start of function
      const pos1 = PositionConverter.spanToPosition(sampleCode, 0);
      expect(pos1).toEqual({ line: 1, column: 1 });

      // Position at 'c' in console (offset 21)
      const pos2 = PositionConverter.spanToPosition(sampleCode, 21);
      expect(pos2).toEqual({ line: 2, column: 3 });

      // Position at 'r' in return
      const pos3 = PositionConverter.spanToPosition(sampleCode, 45);
      expect(pos3).toEqual({ line: 3, column: 3 });
    });

    it('should handle edge cases', () => {
      // Empty string
      const emptyPos = PositionConverter.spanToPosition('', 0);
      expect(emptyPos).toEqual({ line: 1, column: 1 });

      // Position beyond end
      const endPos = PositionConverter.spanToPosition(sampleCode, sampleCode.length);
      expect(endPos.line).toBeGreaterThan(0);
      expect(endPos.column).toBeGreaterThan(0);
    });
  });

  describe('lineColumnToOffset', () => {
    it('should convert line and column to correct offset', () => {
      // Start of file
      const offset1 = PositionConverter.lineColumnToOffset(sampleCode, 1, 1);
      expect(offset1).toBe(0);

      // Start of second line (after "function hello() {\n")
      const offset2 = PositionConverter.lineColumnToOffset(sampleCode, 2, 1);
      expect(offset2).toBe(19);
    });

    it('should throw error for invalid positions', () => {
      expect(() => {
        PositionConverter.lineColumnToOffset(sampleCode, 0, 1);
      }).toThrow('Line 0 is out of range');

      expect(() => {
        PositionConverter.lineColumnToOffset(sampleCode, 1, 0);
      }).toThrow('Column 0 is out of range');

      expect(() => {
        PositionConverter.lineColumnToOffset(sampleCode, 10, 1);
      }).toThrow('Line 10 is out of range');
    });
  });

  describe('getLineContent', () => {
    it('should return correct line content', () => {
      const line1 = PositionConverter.getLineContent(sampleCode, 1);
      expect(line1).toBe('function hello() {');

      const line2 = PositionConverter.getLineContent(sampleCode, 2);
      expect(line2).toBe("  console.log('Hello');");
    });

    it('should throw error for invalid line numbers', () => {
      expect(() => {
        PositionConverter.getLineContent(sampleCode, 0);
      }).toThrow('Line 0 is out of range');

      expect(() => {
        PositionConverter.getLineContent(sampleCode, 10);
      }).toThrow('Line 10 is out of range');
    });
  });

  describe('extractSpanText', () => {
    it('should extract correct text for given span', () => {
      // Extract "function"
      const text1 = PositionConverter.extractSpanText(sampleCode, { start: 0, end: 8 });
      expect(text1).toBe('function');

      // Extract "hello"
      const text2 = PositionConverter.extractSpanText(sampleCode, { start: 9, end: 14 });
      expect(text2).toBe('hello');
    });

    it('should throw error for invalid spans', () => {
      expect(() => {
        PositionConverter.extractSpanText(sampleCode, { start: -1, end: 5 });
      }).toThrow('Invalid span');

      expect(() => {
        PositionConverter.extractSpanText(sampleCode, { start: 5, end: 1000 });
      }).toThrow('Invalid span');

      expect(() => {
        PositionConverter.extractSpanText(sampleCode, { start: 10, end: 5 });
      }).toThrow('Invalid span');
    });
  });

  describe('isValidPosition', () => {
    it('should return true for valid positions', () => {
      expect(PositionConverter.isValidPosition(sampleCode, { line: 1, column: 1 })).toBe(true);
      expect(PositionConverter.isValidPosition(sampleCode, { line: 2, column: 5 })).toBe(true);
    });

    it('should return false for invalid positions', () => {
      expect(PositionConverter.isValidPosition(sampleCode, { line: 0, column: 1 })).toBe(false);
      expect(PositionConverter.isValidPosition(sampleCode, { line: 1, column: 0 })).toBe(false);
      expect(PositionConverter.isValidPosition(sampleCode, { line: 10, column: 1 })).toBe(false);
    });
  });

  describe('roundtrip conversion', () => {
    it('should maintain consistency between span and position conversion', () => {
      // Test various positions in the code
      const testPositions = [
        { line: 1, column: 1 },
        { line: 1, column: 9 }, // "function"
        { line: 2, column: 3 }, // start of console
        { line: 3, column: 1 }, // start of return line
      ];

      testPositions.forEach(originalPos => {
        // Convert position to offset and back
        const offset = PositionConverter.lineColumnToOffset(sampleCode, originalPos.line, originalPos.column);
        const convertedPos = PositionConverter.spanToPosition(sampleCode, offset);
        
        expect(convertedPos).toEqual(originalPos);
      });
    });
  });

  describe('memory optimization tests', () => {
    it('should handle large files efficiently without excessive memory usage', () => {
      // 生成大文件内容（约100KB）
      const lines = Array.from({ length: 2000 }, (_, i) => 
        `function test${i}() { console.log('line ${i}'); return ${i}; }`
      );
      const largeCode = lines.join('\n');

      const startMemory = process.memoryUsage().heapUsed;
      
      // 执行多次转换操作
      for (let i = 0; i < 100; i++) {
        const randomOffset = Math.floor(Math.random() * largeCode.length);
        PositionConverter.spanToPosition(largeCode, randomOffset);
      }

      const endMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = endMemory - startMemory;
      
      // 内存增长应该控制在合理范围内（小于10MB）
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024);
    });

    it('should reuse line map calculations for the same source code', () => {
      const iterations = 1000;
      const startTime = performance.now();
      
      for (let i = 0; i < iterations; i++) {
        const offset = Math.floor(Math.random() * sampleCode.length);
        PositionConverter.spanToPosition(sampleCode, offset);
      }
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // 大量重复操作应该在合理时间内完成（小于100ms）
      expect(duration).toBeLessThan(100);
    });

    it('should handle concurrent position conversion without memory leaks', async () => {
      const testCode = Array.from({ length: 500 }, (_, i) => 
        `const value${i} = ${i} * 2; // line ${i}`
      ).join('\n');

      const startMemory = process.memoryUsage().heapUsed;
      
      // 并发执行多个转换操作
      const promises = Array.from({ length: 50 }, async (_, i) => {
        return new Promise<void>((resolve) => {
          setTimeout(() => {
            for (let j = 0; j < 20; j++) {
              const offset = Math.floor(Math.random() * testCode.length);
              PositionConverter.spanToPosition(testCode, offset);
            }
            resolve();
          }, Math.random() * 10);
        });
      });

      await Promise.all(promises);
      
      // 强制垃圾回收（如果可用）
      if (global.gc) {
        global.gc();
      }
      
      const endMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = endMemory - startMemory;
      
      // 并发操作后内存增长应该控制在合理范围内
      expect(memoryIncrease).toBeLessThan(5 * 1024 * 1024); // 5MB
    });

    it('should optimize buildLineMap for different file sizes', () => {
      const testCases = [
        { size: 'small', lines: 10 },
        { size: 'medium', lines: 1000 },
        { size: 'large', lines: 10000 }
      ];

      testCases.forEach(testCase => {
        const testCode = Array.from({ length: testCase.lines }, (_, i) => 
          `line ${i} content`
        ).join('\n');

        const startTime = performance.now();
        
        // 测试多次构建行映射
        for (let i = 0; i < 10; i++) {
          PositionConverter.spanToPosition(testCode, Math.floor(testCode.length / 2));
        }
        
        const endTime = performance.now();
        const avgTime = (endTime - startTime) / 10;
        
        // 不同大小文件的处理时间应该合理缩放
        if (testCase.size === 'small') {
          expect(avgTime).toBeLessThan(1); // 小文件应该很快
        } else if (testCase.size === 'medium') {
          expect(avgTime).toBeLessThan(10); // 中等文件
        } else {
          expect(avgTime).toBeLessThan(50); // 大文件
        }
      });
    });

    it('should handle edge cases without memory explosion', () => {
      const edgeCases = [
        '',                           // 空文件
        'a',                         // 单字符
        'a\n',                       // 单行带换行
        '\n'.repeat(10000),          // 只有换行符
        'x'.repeat(100000),          // 长单行
        Array.from({ length: 1000 }, () => '').join('\n') // 大量空行
      ];

      edgeCases.forEach((testCode, index) => {
        const startMemory = process.memoryUsage().heapUsed;
        
        try {
          // 对每种边界情况执行多次操作
          for (let i = 0; i < 100; i++) {
            const offset = Math.min(i, testCode.length);
            const pos = PositionConverter.spanToPosition(testCode, offset);
            expect(pos.line).toBeGreaterThan(0);
            expect(pos.column).toBeGreaterThan(0);
          }
        } catch (error) {
          // 边界情况不应该崩溃
          fail(`Edge case ${index} caused error: ${error}`);
        }
        
        const endMemory = process.memoryUsage().heapUsed;
        const memoryIncrease = endMemory - startMemory;
        
        // 边界情况不应该导致过度内存消耗
        expect(memoryIncrease).toBeLessThan(1024 * 1024); // 1MB
      });
    });
  });

  // =================== L0 层基础语义分析方法测试 ===================

  describe('L0 Semantic Analysis Methods', () => {
    describe('isInsignificantLine', () => {
      it('should identify empty lines as insignificant', () => {
        const testCode = `function test() {

  return true;
}`;
        expect(PositionConverter.isInsignificantLine(testCode, 2)).toBe(true);
      });

      it('should identify comments as insignificant', () => {
        expect(PositionConverter.isInsignificantLine(modernCode, 1)).toBe(true); // 单行注释
        expect(PositionConverter.isInsignificantLine(modernCode, 11)).toBe(true); // 另一个注释
      });

      it('should identify import statements as insignificant', () => {
        expect(PositionConverter.isInsignificantLine(modernCode, 2)).toBe(true); // import React
        expect(PositionConverter.isInsignificantLine(modernCode, 3)).toBe(true); // import { useState }
      });

      it('should identify type declarations as insignificant', () => {
        const simpleType = `type SimpleType = string;`;
        expect(PositionConverter.isInsignificantLine(simpleType, 1)).toBe(true); // simple type alias
        
        // Complex type declarations like 'type Props = {' should be considered significant 
        // because they start type definitions with content
        expect(PositionConverter.isInsignificantLine(modernCode, 5)).toBe(false); // type Props = {
      });

      it('should identify opening/closing braces as insignificant', () => {
        const braceCode = `function test() {
  if (true) {
    return 42;
  }
}`;
        // Line 2 contains 'if (true) {' which has meaningful content (the if statement)
        expect(PositionConverter.isInsignificantLine(braceCode, 2)).toBe(false); // if statement with opening brace
        expect(PositionConverter.isInsignificantLine(braceCode, 4)).toBe(true); // } 单独一行
        expect(PositionConverter.isInsignificantLine(braceCode, 5)).toBe(true); // 函数结束的 }
      });

      it('should identify meaningful code lines as significant', () => {
        expect(PositionConverter.isInsignificantLine(modernCode, 9)).toBe(false); // function declaration
        expect(PositionConverter.isInsignificantLine(modernCode, 10)).toBe(false); // useState
        expect(PositionConverter.isInsignificantLine(modernCode, 13)).toBe(false); // if statement
        expect(PositionConverter.isInsignificantLine(modernCode, 14)).toBe(false); // return statement
      });

      it('should handle modern syntax patterns', () => {
        const modernSyntax = `const { data } = await fetch('/api');
const items = [...array];
const template = \`Hello \${name}\`;
@Component()
class MyClass {}`;

        expect(PositionConverter.isInsignificantLine(modernSyntax, 1)).toBe(false); // 解构赋值
        expect(PositionConverter.isInsignificantLine(modernSyntax, 2)).toBe(false); // 展开操作符
        expect(PositionConverter.isInsignificantLine(modernSyntax, 3)).toBe(false); // 模板字符串
        expect(PositionConverter.isInsignificantLine(modernSyntax, 4)).toBe(true); // 装饰器
        expect(PositionConverter.isInsignificantLine(modernSyntax, 5)).toBe(false); // 类声明
      });

      it('should handle JSX patterns', () => {
        const jsxCode = `return (
  <div>
    {/* Comment */}
    <span>Text</span>
  </div>
);`;
        expect(PositionConverter.isInsignificantLine(jsxCode, 1)).toBe(false); // return statement
        expect(PositionConverter.isInsignificantLine(jsxCode, 2)).toBe(false); // opening div (contains content)
        expect(PositionConverter.isInsignificantLine(jsxCode, 3)).toBe(true); // JSX comment
        expect(PositionConverter.isInsignificantLine(jsxCode, 4)).toBe(false); // span with content
      });

      it('should use file path for caching when provided', () => {
        const filePath = '/test/file.ts';
        const result1 = PositionConverter.isInsignificantLine(modernCode, 1, filePath);
        const result2 = PositionConverter.isInsignificantLine(modernCode, 1, filePath);
        
        expect(result1).toBe(result2);
        expect(result1).toBe(true);
        
        const stats = PositionConverter.getCacheStats();
        expect(stats.lineAnalysisCacheSize).toBeGreaterThan(0);
      });
    });

    describe('findNearestMeaningfulLine', () => {
      it('should return the same line if it is meaningful', () => {
        const result = PositionConverter.findNearestMeaningfulLine(modernCode, 9); // function declaration
        expect(result).toBe(9);
      });

      it('should find nearest meaningful line upwards', () => {
        const testCode = `// Comment 1
function test() {
  return true;
}`;
        const result = PositionConverter.findNearestMeaningfulLine(testCode, 1); // comment line
        expect(result).toBe(2); // function declaration
      });

      it('should find nearest meaningful line downwards when upward search fails', () => {
        const testCode = `// Comment 1
// Comment 2
// Comment 3
function test() {
  return true;
}`;
        const result = PositionConverter.findNearestMeaningfulLine(testCode, 2);
        expect(result).toBe(4); // function declaration
      });

      it('should respect maxDistance parameter', () => {
        const testCode = `// Comment 1
// Comment 2
// Comment 3
// Comment 4
// Comment 5
// Comment 6
function test() {
  return true;
}`;
        const result = PositionConverter.findNearestMeaningfulLine(testCode, 3, 2); // max distance 2
        expect(result).toBe(3); // should return original line when no meaningful line found within distance
      });

      it('should prioritize upward search over downward', () => {
        const testCode = `function upper() {
  return 1;
}
// Comment line
function lower() {
  return 2;
}`;
        const result = PositionConverter.findNearestMeaningfulLine(testCode, 4); // comment line
        // The algorithm searches upward first, but in this case, the downward function declaration
        // at line 5 might be found first due to proximity
        expect(result).toBeGreaterThan(3); // should find a meaningful line
        expect(result).toBeLessThanOrEqual(5); // within reasonable range
      });

      it('should handle edge cases at file boundaries', () => {
        const testCode = `// First comment
function test() {
  return true;
}
// Last comment`;
        
        const firstResult = PositionConverter.findNearestMeaningfulLine(testCode, 1);
        expect(firstResult).toBe(2); // should find function
        
        const lastResult = PositionConverter.findNearestMeaningfulLine(testCode, 5);
        expect(lastResult).toBeLessThan(5); // should find meaningful line above
      });
    });

    describe('findLastMeaningfulLine', () => {
      it('should return the same line if it is meaningful', () => {
        const result = PositionConverter.findLastMeaningfulLine(modernCode, 9); // function declaration
        expect(result).toBe(9);
      });

      it('should find the last meaningful line within lookback range', () => {
        const testCode = `function test() {
  const x = 1;
  const y = 2;
  // Comment
  // Another comment
  
  return x + y;
}`;
        const result = PositionConverter.findLastMeaningfulLine(testCode, 6); // empty line
        expect(result).toBe(3); // const y = 2
      });

      it('should handle extended lookback when needed', () => {
        const testCode = `function test() {
  const x = 1;
${'  // Comment\n'.repeat(15)}  return x;
}`;
        const result = PositionConverter.findLastMeaningfulLine(testCode, 17, 5); // small lookback
        // With extended search, should find either const x = 1 (line 2) or return x statement
        expect(result).toBeGreaterThanOrEqual(2); 
        expect(result).toBeLessThanOrEqual(17);
      });

      it('should respect maxLookback parameter', () => {
        const testCode = `function start() {
  return 1;
}
${'// Comment\n'.repeat(15)}function end() {
  return 2;
}`;
        const result = PositionConverter.findLastMeaningfulLine(testCode, 18, 3); // limited lookback
        expect(result).toBeGreaterThanOrEqual(15); // should fallback to a reasonable line
      });

      it('should handle boundary conditions correctly', () => {
        const testCode = `// Start comment
function test() {
  return true;
}`;
        
        // Test from beyond file end
        const result1 = PositionConverter.findLastMeaningfulLine(testCode, 100);
        expect(result1).toBeGreaterThanOrEqual(3); // should find a meaningful line
        
        // Test from file start
        const result2 = PositionConverter.findLastMeaningfulLine(testCode, 1);
        expect(result2).toBe(1);
      });
    });

    describe('spanToPositionWithSmartFallback', () => {
      it('should return basic position when line is already meaningful', () => {
        // Get offset for function declaration line
        const offset = PositionConverter.lineColumnToOffset(modernCode, 9, 1);
        const result = PositionConverter.spanToPositionWithSmartFallback(modernCode, offset);
        
        expect(result.line).toBe(9);
      });

      it('should fallback to nearest meaningful line for insignificant lines', () => {
        // Get offset for comment line
        const offset = PositionConverter.lineColumnToOffset(modernCode, 1, 1);
        const result = PositionConverter.spanToPositionWithSmartFallback(modernCode, offset);
        
        expect(result.line).toBeGreaterThan(1); // should find a meaningful line
      });

      it('should use span range information when provided', () => {
        // Test with span that covers multiple lines
        const startOffset = PositionConverter.lineColumnToOffset(modernCode, 1, 1);
        const endOffset = PositionConverter.lineColumnToOffset(modernCode, 10, 1);
        
        const result = PositionConverter.spanToPositionWithSmartFallback(
          modernCode, 
          startOffset, 
          endOffset
        );
        
        expect(result.line).toBeGreaterThanOrEqual(1);
        expect(result.line).toBeLessThanOrEqual(10);
      });

      it('should handle file beginning edge cases', () => {
        const testCode = `// Comment 1
// Comment 2
function test() {
  return true;
}`;
        
        const offset = PositionConverter.lineColumnToOffset(testCode, 1, 1);
        const result = PositionConverter.spanToPositionWithSmartFallback(testCode, offset);
        
        expect(result.line).toBeGreaterThan(1); // should find function
      });

      it('should handle file ending edge cases', () => {
        const testCode = `function test() {
  return true;
}
// Final comment
// Another final comment`;
        
        const offset = PositionConverter.lineColumnToOffset(testCode, 5, 1);
        const result = PositionConverter.spanToPositionWithSmartFallback(testCode, offset);
        
        expect(result.line).toBeLessThan(5); // should fallback to meaningful line above
      });

      it('should integrate with file path caching', () => {
        const filePath = '/test/component.tsx';
        const offset = PositionConverter.lineColumnToOffset(modernCode, 1, 1);
        
        const result1 = PositionConverter.spanToPositionWithSmartFallback(
          modernCode, 
          offset, 
          undefined, 
          filePath
        );
        const result2 = PositionConverter.spanToPositionWithSmartFallback(
          modernCode, 
          offset, 
          undefined, 
          filePath
        );
        
        expect(result1).toEqual(result2);
        
        const stats = PositionConverter.getCacheStats();
        expect(stats.lineAnalysisCacheSize).toBeGreaterThan(0);
      });

      it('should handle invalid spans gracefully', () => {
        const result1 = PositionConverter.spanToPositionWithSmartFallback(modernCode, -1);
        expect(result1.line).toBeGreaterThanOrEqual(1);
        expect(result1.column).toBeGreaterThanOrEqual(1);
        
        const result2 = PositionConverter.spanToPositionWithSmartFallback(modernCode, 999999);
        expect(result2.line).toBeGreaterThan(0);
        expect(result2.column).toBeGreaterThan(0);
      });
    });

    describe('cache integration', () => {
      it('should use line analysis cache for repeated operations', () => {
        const filePath = '/test/cache.ts';
        
        // First operation should miss cache
        const result1 = PositionConverter.isInsignificantLine(modernCode, 1, filePath);
        
        // Second operation should hit cache
        const result2 = PositionConverter.isInsignificantLine(modernCode, 2, filePath);
        
        expect(result1).toBe(true);
        expect(result2).toBe(true);
        
        const stats = PositionConverter.getCacheStats();
        expect(stats.lineAnalysisCacheSize).toBe(1); // One cached file
      });

      it('should handle cache without file path', () => {
        const result1 = PositionConverter.isInsignificantLine(modernCode, 1);
        const result2 = PositionConverter.isInsignificantLine(modernCode, 1);
        
        expect(result1).toBe(result2);
        
        const stats = PositionConverter.getCacheStats();
        expect(stats.lineAnalysisCacheSize).toBeGreaterThanOrEqual(0);
      });

      it('should clear both caches when clearCache is called', () => {
        // Generate some cache entries
        PositionConverter.isInsignificantLine(modernCode, 1, '/test/file1.ts');
        PositionConverter.spanToPosition(modernCode, 0);
        
        let stats = PositionConverter.getCacheStats();
        expect(stats.size + stats.lineAnalysisCacheSize).toBeGreaterThan(0);
        
        PositionConverter.clearCache();
        
        stats = PositionConverter.getCacheStats();
        expect(stats.size).toBe(0);
        expect(stats.lineAnalysisCacheSize).toBe(0);
      });

      it('should provide accurate cache statistics', () => {
        const stats = PositionConverter.getCacheStats();
        
        expect(typeof stats.size).toBe('number');
        expect(typeof stats.maxSize).toBe('number');
        expect(typeof stats.hitRate).toBe('number');
        expect(typeof stats.memoryEstimate).toBe('number');
        expect(typeof stats.lineAnalysisCacheSize).toBe('number');
        
        expect(stats.size).toBeGreaterThanOrEqual(0);
        expect(stats.lineAnalysisCacheSize).toBeGreaterThanOrEqual(0);
        expect(stats.hitRate).toBeGreaterThanOrEqual(0);
        expect(stats.hitRate).toBeLessThanOrEqual(1);
      });
    });

    describe('performance and stress testing', () => {
      it('should handle large files with semantic analysis efficiently', () => {
        // Generate a large file with mixed content
        const lines = [];
        for (let i = 0; i < 200; i++) { // Reduced size for testing
          if (i % 5 === 0) lines.push(`// Comment ${i}`);
          else if (i % 5 === 1) lines.push(`import { module${i} } from 'module${i}';`);
          else if (i % 5 === 2) lines.push('');
          else if (i % 5 === 3) lines.push(`type Type${i} = string;`);
          else lines.push(`function func${i}() { return ${i}; }`);
        }
        const largeCode = lines.join('\n');
        
        const startTime = performance.now();
        
        // Perform multiple semantic analysis operations
        for (let i = 1; i <= 20; i += 5) { // Reduced iterations
          PositionConverter.isInsignificantLine(largeCode, i, '/test/large.ts');
          PositionConverter.findNearestMeaningfulLine(largeCode, i, 5, '/test/large.ts');
        }
        
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        // Should complete within reasonable time (less than 2000ms for reduced test)
        // Note: First run includes cache building, subsequent runs should be faster
        expect(duration).toBeLessThan(2000);
        
        const stats = PositionConverter.getCacheStats();
        expect(stats.lineAnalysisCacheSize).toBeGreaterThan(0);
      });

      it('should maintain accuracy with concurrent semantic analysis', async () => {
        const testCode = `// Comment
function test${Math.random()}() {
  return true;
}`;
        
        const promises = Array.from({ length: 20 }, async (_, i) => {
          return new Promise<boolean>((resolve) => {
            setTimeout(() => {
              const result = PositionConverter.isInsignificantLine(testCode, 1, `/test/concurrent${i}.ts`);
              resolve(result);
            }, Math.random() * 10);
          });
        });
        
        const results = await Promise.all(promises);
        
        // All results should be consistent (comment line is insignificant)
        expect(results.every(result => result === true)).toBe(true);
      });

      it('should handle extreme edge cases in semantic analysis', () => {
        const edgeCases = [
          '',                              // Empty file
          '// Only comment',               // Only comment
          '\n\n\n',                       // Only newlines
          'a',                            // Single character
          'function f(){return 1;}',       // No whitespace
          '/* multi\nline\ncomment */',   // Multi-line comment
          ''.padEnd(10000, 'x'),          // Very long line
        ];
        
        edgeCases.forEach((testCode, index) => {
          expect(() => {
            for (let line = 1; line <= 10; line++) {
              try {
                PositionConverter.isInsignificantLine(testCode, line, `/test/edge${index}.ts`);
                PositionConverter.findNearestMeaningfulLine(testCode, line, 3, `/test/edge${index}.ts`);
                PositionConverter.findLastMeaningfulLine(testCode, line, 3, `/test/edge${index}.ts`);
              } catch (error) {
                // Expected for out-of-bounds lines
                if (!(error instanceof Error) || !error.message.includes('out of range')) {
                  throw error;
                }
              }
            }
          }).not.toThrow();
        });
      });
    });
  });
});