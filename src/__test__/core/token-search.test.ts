import { describe, test, expect, beforeEach } from 'vitest';
import { ComplexityVisitor } from '../../core/complexity-visitor';
import { DetailCollector } from '../../core/detail-collector';
import { ASTParser } from '../../core/parser';
import type { Node, Module } from '@swc/core';

/**
 * SWC Token 查找系统测试套件
 * 
 * 测试覆盖：
 * - Token查找的三级降级策略
 * - 搜索范围限制和优化
 * - 不同关键字的精确定位
 * - 错误恢复和边界条件
 */
describe('SWC Token查找系统', () => {
  let parser: ASTParser;
  
  beforeEach(() => {
    parser = new ASTParser();
  });

  // =============================================================================
  // Token查找基础功能测试
  // =============================================================================
  
  describe('Token查找基础功能', () => {
    test('应该能找到简单的if关键字', async () => {
      const sourceCode = `
        function test() {
          if (condition) {
            return true;
          }
        }
      `;
      const ast = await parser.parseCode(sourceCode, 'test.ts');
      const detailCollector = new DetailCollector();
      const visitor = new ComplexityVisitor(sourceCode, detailCollector);
      
      // 访问AST来获取函数节点
      visitor.visit(ast);
      
      // 验证if关键字被找到
      const results = visitor.getResults();
      expect(results.length).toBe(1);
      
      const functionDetails = results[0];
      expect(functionDetails.details?.length).toBeGreaterThan(0);
      
      // 查找token-search相关的详细信息
      const tokenSearchSteps = functionDetails.details?.filter(step => 
        step.ruleId === 'token-search' && step.description.includes('if')
      ) || [];
      
      if (tokenSearchSteps.length > 0) {
        // 如果有token搜索记录，验证成功找到
        const successfulSearch = tokenSearchSteps.find(step => 
          step.context?.includes('成功')
        );
        expect(successfulSearch).toBeDefined();
      }
    });

    test('应该能找到for循环关键字', async () => {
      const sourceCode = `
        function test() {
          for (let i = 0; i < 10; i++) {
            console.log(i);
          }
        }
      `;
      const ast = await parser.parseCode(sourceCode, 'test.ts');
      const detailCollector = new DetailCollector();
      const visitor = new ComplexityVisitor(sourceCode, detailCollector);
      
      visitor.visit(ast);
      
      const results = visitor.getResults();
      expect(results.length).toBe(1);
      expect(results[0].complexity).toBe(1); // for循环应该增加复杂度1
    });

    test('应该能找到while循环关键字', async () => {
      const sourceCode = `
        function test() {
          while (condition) {
            doSomething();
          }
        }
      `;
      const ast = await parser.parseCode(sourceCode, 'test.ts');
      const detailCollector = new DetailCollector();
      const visitor = new ComplexityVisitor(sourceCode, detailCollector);
      
      visitor.visit(ast);
      
      const results = visitor.getResults();
      expect(results.length).toBe(1);
      expect(results[0].complexity).toBe(1); // while循环应该增加复杂度1
    });

    test('应该能找到switch语句关键字', async () => {
      const sourceCode = `
        function test() {
          switch (value) {
            case 1:
              return 'one';
            case 2:
              return 'two';
            default:
              return 'other';
          }
        }
      `;
      const ast = await parser.parseCode(sourceCode, 'test.ts');
      const detailCollector = new DetailCollector();
      const visitor = new ComplexityVisitor(sourceCode, detailCollector);
      
      visitor.visit(ast);
      
      const results = visitor.getResults();
      expect(results.length).toBe(1);
      expect(results[0].complexity).toBe(1); // switch语句应该增加复杂度1
    });
  });

  // =============================================================================
  // 降级策略测试
  // =============================================================================
  
  describe('Token查找降级策略', () => {
    test('应该处理紧凑格式的代码', async () => {
      const sourceCode = `function test(){if(a>0){for(let i=0;i<10;i++){if(b)break;}}}`;
      const ast = await parser.parseCode(sourceCode, 'test.ts');
      const visitor = new ComplexityVisitor(sourceCode);
      
      visitor.visit(ast);
      
      // 即使在紧凑格式下，也应该正确识别关键字
      const results = visitor.getResults();
      expect(results.length).toBe(1);
      expect(results[0].complexity).toBeGreaterThan(0);
    });

    test('应该处理包含注释的代码', async () => {
      const sourceCode = `
        function test() {
          // 这是一个if语句
          if (/* 条件注释 */ condition) {
            return true;
          }
        }
      `;
      const ast = await parser.parseCode(sourceCode, 'test.ts');
      const visitor = new ComplexityVisitor(sourceCode);
      
      visitor.visit(ast);
      
      const results = visitor.getResults();
      expect(results.length).toBe(1);
      expect(results[0].complexity).toBe(1);
    });

    test('应该处理多行字符串和特殊字符', async () => {
      const sourceCode = `
        function test() {
          const message = \`
            这是一个包含if的字符串，但不应该被识别为关键字
          \`;
          
          if (condition) { // 这个if才是真正的关键字
            return message;
          }
        }
      `;
      const ast = await parser.parseCode(sourceCode, 'test.ts');
      const visitor = new ComplexityVisitor(sourceCode);
      
      visitor.visit(ast);
      
      const results = visitor.getResults();
      expect(results.length).toBe(1);
      expect(results[0].complexity).toBe(1); // 只有真正的if语句应该被计算
    });
  });

  // =============================================================================
  // 搜索范围限制测试
  // =============================================================================
  
  describe('搜索范围限制', () => {
    test('应该能限制在节点span范围内搜索', async () => {
      const sourceCode = `
        function test1() {
          if (condition1) {
            return 'test1';
          }
        }
        
        function test2() {
          if (condition2) {
            return 'test2';
          }
        }
      `;
      const ast = await parser.parseCode(sourceCode, 'test.ts');
      const visitor = new ComplexityVisitor(sourceCode);
      
      visitor.visit(ast);
      
      // 应该找到两个函数，各自有一个if语句
      const results = visitor.getResults();
      expect(results.length).toBe(2);
      expect(results[0].complexity).toBe(1);
      expect(results[1].complexity).toBe(1);
    });

    test('应该处理嵌套结构的范围限制', async () => {
      const sourceCode = `
        function outer() {
          if (condition1) {                    // +1
            for (let i = 0; i < 10; i++) {    // +1 + 1(嵌套)
              if (condition2) {               // +1 + 2(嵌套)
                while (condition3) {          // +1 + 3(嵌套)
                  break;
                }
              }
            }
          }
        }
      `;
      const ast = await parser.parseCode(sourceCode, 'test.ts');
      const visitor = new ComplexityVisitor(sourceCode);
      
      visitor.visit(ast);
      
      const results = visitor.getResults();
      expect(results.length).toBe(1);
      expect(results[0].complexity).toBe(10); // 1 + 2 + 3 + 4 = 10
    });
  });

  // =============================================================================
  // 错误恢复和边界条件测试
  // =============================================================================
  
  describe('错误恢复和边界条件', () => {
    test('应该处理空的控制流语句', async () => {
      const sourceCode = `
        function test() {
          if (condition) {
            // 空的if分支
          }
          
          while (condition) {
            // 空的while循环
          }
        }
      `;
      const ast = await parser.parseCode(sourceCode, 'test.ts');
      const visitor = new ComplexityVisitor(sourceCode);
      
      visitor.visit(ast);
      
      const results = visitor.getResults();
      expect(results.length).toBe(1);
      expect(results[0].complexity).toBe(2); // if + while
    });

    test('应该处理无效span的节点', async () => {
      const sourceCode = `
        function test() {
          if (condition) {
            return true;
          }
        }
      `;
      const ast = await parser.parseCode(sourceCode, 'test.ts');
      
      // 创建一个访问者，模拟span修正场景
      const visitor = new ComplexityVisitor(sourceCode);
      
      visitor.visit(ast);
      
      // 即使有span问题，也应该能够找到关键字
      const results = visitor.getResults();
      expect(results.length).toBe(1);
      expect(results[0].complexity).toBeGreaterThanOrEqual(1);
    });

    test('应该处理极端的代码格式', async () => {
      const sourceCode = `function test(){if(a)if(b)if(c)return 1;}`;
      const ast = await parser.parseCode(sourceCode, 'test.ts');
      const visitor = new ComplexityVisitor(sourceCode);
      
      visitor.visit(ast);
      
      // 多层嵌套的if应该被正确识别
      const results = visitor.getResults();
      expect(results.length).toBe(1);
      expect(results[0].complexity).toBe(6); // 1 + 2 + 3 = 6
    });
  });

  // =============================================================================
  // 性能和兼容性测试
  // =============================================================================
  
  describe('性能和兼容性', () => {
    test('应该在大型代码块中保持性能', async () => {
      // 生成包含100个if语句的大型函数
      const ifStatements = Array.from({ length: 100 }, (_, i) => 
        `  if (condition${i}) { doSomething${i}(); }`
      ).join('\n');
      
      const sourceCode = `
        function largeFunction() {
${ifStatements}
        }
      `;
      
      const startTime = Date.now();
      const ast = await parser.parseCode(sourceCode, 'test.ts');
      const visitor = new ComplexityVisitor(sourceCode);
      
      visitor.visit(ast);
      const endTime = Date.now();
      
      const results = visitor.getResults();
      expect(results.length).toBe(1);
      expect(results[0].complexity).toBe(100);
      
      // Token查找系统不应该显著影响性能
      expect(endTime - startTime).toBeLessThan(2000); // 允许2秒处理时间
    });

    test('应该与现有的span修正系统兼容', async () => {
      const sourceCode = `
        function complexTest() {
          try {
            if (condition1) {
              for (let i = 0; i < 10; i++) {
                switch (value) {
                  case 1:
                    while (condition2) {
                      break;
                    }
                    break;
                  default:
                    return;
                }
              }
            }
          } catch (error) {
            console.error(error);
          }
        }
      `;
      const ast = await parser.parseCode(sourceCode, 'test.ts');
      const detailCollector = new DetailCollector();
      const visitor = new ComplexityVisitor(sourceCode, detailCollector);
      
      visitor.visit(ast);
      
      const results = visitor.getResults();
      expect(results.length).toBe(1);
      expect(results[0].complexity).toBeGreaterThan(5);
      
      // 所有复杂度计算步骤都应该有有效的位置信息
      const complexitySteps = results[0].details?.filter(step => step.increment > 0) || [];
      expect(complexitySteps.length).toBeGreaterThan(0);
      
      complexitySteps.forEach(step => {
        expect(step.line).toBeGreaterThan(0);
        expect(step.column).toBeGreaterThanOrEqual(0);
      });
    });
  });

  // =============================================================================
  // 直接API测试
  // =============================================================================
  
  describe('直接API测试', () => {
    test('findKeywordPosition方法应该正确工作', async () => {
      const sourceCode = `
        function test() {
          if (condition) {
            return true;
          }
        }
      `;
      const ast = await parser.parseCode(sourceCode, 'test.ts');
      const visitor = new ComplexityVisitor(sourceCode);
      
      // 创建一个模拟的节点来测试直接API调用
      const mockNode = {
        type: 'IfStatement',
        span: { start: 30, end: 80, ctxt: 0 }
      };
      
      const position = visitor.findKeywordPosition(mockNode, 'if');
      expect(position).not.toBeNull();
      expect(typeof position).toBe('number');
      
      if (position !== null) {
        expect(position).toBeGreaterThanOrEqual(0);
        expect(position).toBeLessThan(sourceCode.length);
        
        // 验证找到的位置确实包含'if'关键字
        const foundText = sourceCode.slice(position, position + 2);
        expect(foundText).toBe('if');
      }
    });

    test('应该处理未找到关键字的情况', async () => {
      const sourceCode = `
        function test() {
          return true;
        }
      `;
      const visitor = new ComplexityVisitor(sourceCode);
      
      const mockNode = {
        type: 'IfStatement',
        span: { start: 0, end: sourceCode.length, ctxt: 0 }
      };
      
      // 尝试查找不存在的关键字
      const position = visitor.findKeywordPosition(mockNode, 'if');
      expect(position).toBeNull();
    });
  });
});