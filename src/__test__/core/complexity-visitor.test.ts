import { describe, test, expect, beforeEach } from 'vitest';
import { SemanticComplexityVisitor } from '../../core/complexity-visitor-refactored';
import { SemanticComplexityVisitorFactory } from '../../core/semantic-complexity-visitor-factory';
import { DetailCollector } from '../../core/detail-collector';
import { ASTParser } from '../../core/parser';
import type { Node, Module } from '@swc/core';

/**
 * SemanticComplexityVisitor 综合测试套件
 * 
 * 测试范围包括：
 * - 各种语句类型的复杂度计算正确性
 * - 语义感知定位的准确性
 * - 与 DetailCollector 的集成
 * - 工厂模式的创建方法
 * - 边界条件和错误恢复
 */
describe('SemanticComplexityVisitor', () => {
  let parser: ASTParser;

  beforeEach(() => {
    parser = new ASTParser();
  });

  // =============================================================================
  // 工厂模式测试
  // =============================================================================
  
  describe('工厂模式创建', () => {
    test('应该能够创建轻量级实例', () => {
      const sourceCode = 'function test() { if (true) return 42; }';
      const visitor = SemanticComplexityVisitorFactory.createLightweight(sourceCode);
      
      expect(visitor).toBeInstanceOf(SemanticComplexityVisitor);
    });

    test('应该能够创建完整功能实例', () => {
      const sourceCode = 'function test() { if (true) return 42; }';
      const detailCollector = new DetailCollector();
      const visitor = SemanticComplexityVisitorFactory.createComplete(sourceCode, detailCollector);
      
      expect(visitor).toBeInstanceOf(SemanticComplexityVisitor);
    });

    test('应该能够创建JSX优化实例', () => {
      const sourceCode = 'const Component = () => <div>{condition && <span>test</span>}</div>';
      const visitor = SemanticComplexityVisitorFactory.createForJSX(sourceCode);
      
      expect(visitor).toBeInstanceOf(SemanticComplexityVisitor);
    });
  });

  // =============================================================================
  // 基础复杂度计算测试
  // =============================================================================
  
  describe('基础复杂度计算', () => {
    test('简单函数应该返回0复杂度', async () => {
      const code = 'function simple() { return 42; }';
      const ast = await parser.parseCode(code);
      const visitor = SemanticComplexityVisitorFactory.createLightweight(code);
      
      visitor.visit(ast);
      const complexity = visitor.getComplexity();
      
      expect(complexity).toBe(0);
    });

    test('包含if语句的函数应该增加复杂度', async () => {
      const code = 'function withIf() { if (true) return 42; }';
      const ast = await parser.parseCode(code);
      const visitor = SemanticComplexityVisitorFactory.createLightweight(code);
      
      visitor.visit(ast);
      const complexity = visitor.getComplexity();
      
      expect(complexity).toBeGreaterThan(0);
    });

    test('包含循环的函数应该增加复杂度', async () => {
      const code = 'function withLoop() { for (let i = 0; i < 10; i++) { console.log(i); } }';
      const ast = await parser.parseCode(code);
      const visitor = SemanticComplexityVisitorFactory.createLightweight(code);
      
      visitor.visit(ast);
      const complexity = visitor.getComplexity();
      
      expect(complexity).toBeGreaterThan(0);
    });

    test('包含逻辑操作符的函数应该增加复杂度', async () => {
      const code = 'function withLogical() { return a && b || c; }';
      const ast = await parser.parseCode(code);
      const visitor = SemanticComplexityVisitorFactory.createLightweight(code);
      
      visitor.visit(ast);
      const complexity = visitor.getComplexity();
      
      expect(complexity).toBeGreaterThan(0);
    });
  });

  // =============================================================================
  // 函数结果测试
  // =============================================================================
  
  describe('函数结果获取', () => {
    test('应该能够获取函数分析结果', async () => {
      const code = `
        function test1() { if (true) return 1; }
        function test2() { while (true) break; }
      `;
      const ast = await parser.parseCode(code);
      const visitor = SemanticComplexityVisitorFactory.createLightweight(code);
      
      visitor.visit(ast);
      const functionResults = visitor.getFunctionResults();
      
      expect(functionResults.size).toBeGreaterThan(0);
      
      // 检查函数结果的结构
      const results = Array.from(functionResults.values());
      if (results.length > 0) {
        const firstResult = results[0];
        expect(firstResult).toHaveProperty('name');
        expect(firstResult).toHaveProperty('complexity');
        expect(firstResult).toHaveProperty('position');
      }
    });
  });

  // =============================================================================
  // 语义感知特性测试
  // =============================================================================
  
  describe('语义感知特性', () => {
    test('应该能够处理JSX表达式', async () => {
      const code = `
        const Component = () => {
          return <div>{condition && <span>test</span>}</div>;
        };
      `;
      const ast = await parser.parseCode(code, 'test.tsx');
      const visitor = SemanticComplexityVisitorFactory.createForJSX(code);
      
      visitor.visit(ast);
      const complexity = visitor.getComplexity();
      
      // JSX中的逻辑表达式应该贡献复杂度
      expect(complexity).toBeGreaterThan(0);
    });

    test('应该能够处理箭头函数', async () => {
      const code = `
        const arrow = (x) => {
          if (x > 0) return x * 2;
          return 0;
        };
      `;
      const ast = await parser.parseCode(code);
      const visitor = SemanticComplexityVisitorFactory.createLightweight(code);
      
      visitor.visit(ast);
      const functionResults = visitor.getFunctionResults();
      
      expect(functionResults.size).toBeGreaterThan(0);
    });
  });

  // =============================================================================
  // 详细模式集成测试
  // =============================================================================
  
  describe('详细模式集成', () => {
    test('应该能够与DetailCollector集成', async () => {
      const code = 'function complex() { if (true) { while (false) { return; } } }';
      const ast = await parser.parseCode(code);
      const detailCollector = new DetailCollector();
      const visitor = SemanticComplexityVisitorFactory.createComplete(code, detailCollector);
      
      visitor.visit(ast);
      const complexity = visitor.getComplexity();
      
      expect(complexity).toBeGreaterThan(0);
      // 详细信息应该被收集
      // 注意：这个测试需要根据DetailCollector的实际API调整
    });
  });

  // =============================================================================
  // 错误处理测试
  // =============================================================================
  
  describe('错误处理', () => {
    test('应该能够处理空代码', () => {
      const visitor = SemanticComplexityVisitorFactory.createLightweight('');
      
      expect(() => {
        visitor.visit({} as any);
      }).not.toThrow();
    });

    test('应该能够处理无效节点', () => {
      const visitor = SemanticComplexityVisitorFactory.createLightweight('function test() {}');
      
      expect(() => {
        visitor.visit(null as any);
      }).not.toThrow();
    });
  });
});