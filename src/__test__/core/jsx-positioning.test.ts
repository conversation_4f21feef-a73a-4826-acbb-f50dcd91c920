/**
 * JSX 元素精确定位测试
 * 测试 ComplexityVisitor 中的 JSX 定位功能
 */

import { describe, test, expect, beforeEach } from 'vitest';
import { ComplexityVisitor } from '../../core/complexity-visitor';
import { DetailCollector } from '../../core/detail-collector';
import { ASTParser } from '../../core/parser'; 
import type { CalculationOptions } from '../../core/types';

describe('JSX 元素精确定位', () => {
  let parser: ASTParser;
  let detailCollector: DetailCollector;
  let options: CalculationOptions;

  beforeEach(() => {
    parser = new ASTParser();
    detailCollector = new DetailCollector();
    options = {
      enableDebugLog: true,
      enableDetails: true
    };
  });

  /**
   * 创建 ComplexityVisitor 实例用于测试
   */
  const createVisitor = (sourceCode: string): ComplexityVisitor => {
    const visitor = new ComplexityVisitor(sourceCode, detailCollector, options);
    // 初始化 DetailCollector 以避免警告
    detailCollector.startFunction('TestFunction', 1, 0);
    return visitor;
  };

  /**
   * 查找指定类型的 JSX 节点
   */
  const findJSXNode = (ast: any, nodeType: string): any => {
    let found: any = null;
    
    const traverse = (node: any) => {
      if (!node || typeof node !== 'object') return;
      
      if (node.type === nodeType) {
        found = node;
        return;
      }
      
      for (const key in node) {
        if (key === 'span' || key === 'type') continue;
        const value = node[key];
        
        if (Array.isArray(value)) {
          value.forEach(traverse);
        } else if (value && typeof value === 'object') {
          traverse(value);
        }
      }
    };
    
    traverse(ast);
    return found;
  };

  describe('findJsxOpeningTagPosition', () => {
    test('应该正确定位简单 JSX 元素', async () => {
      const code = `
        function MyComponent() {
          return <div>content</div>;
        }
      `;
      
      const visitor = createVisitor(code);
      const ast = await parser.parseCode(code, 'test.tsx');
      const jsxElement = findJSXNode(ast, 'JSXElement');
      
      expect(jsxElement).toBeTruthy();
      
      const position = visitor.findJsxOpeningTagPosition(jsxElement);
      
      // 应该找到 '<div' 的位置
      expect(position).toBeGreaterThanOrEqual(0);
      
      if (position !== null) {
        const foundText = code.slice(position, position + 4);
        expect(foundText).toBe('<div');
      }
    });

    test('应该正确定位自闭合 JSX 元素', async () => {
      const code = `
        function MyComponent() {
          return <img src="test.jpg" alt="test" />;
        }
      `;
      
      const visitor = createVisitor(code);
      const ast = await parser.parseCode(code, 'test.tsx');
      const jsxElement = findJSXNode(ast, 'JSXElement'); // 自闭合元素在 SWC 中也是 JSXElement
      
      expect(jsxElement).toBeTruthy();
      
      const position = visitor.findJsxOpeningTagPosition(jsxElement);
      
      // 应该找到 '<img' 的位置
      expect(position).toBeGreaterThanOrEqual(0);
      
      if (position !== null) {
        const foundText = code.slice(position, position + 4);
        expect(foundText).toBe('<img');
      }
    });

    test('应该正确定位带属性的 JSX 元素', async () => {
      const code = `
        function MyComponent() {
          return <button onClick={handleClick} className="btn">Click</button>;
        }
      `;
      
      const visitor = createVisitor(code);
      const ast = await parser.parseCode(code, 'test.tsx');
      const jsxElement = findJSXNode(ast, 'JSXElement');
      
      expect(jsxElement).toBeTruthy();
      
      const position = visitor.findJsxOpeningTagPosition(jsxElement);
      
      // 应该找到 '<button' 的位置
      expect(position).toBeGreaterThanOrEqual(0);
      
      if (position !== null) {
        const foundText = code.slice(position, position + 7);
        expect(foundText).toBe('<button');
      }
    });

    test('应该正确定位 React 组件', async () => {
      const code = `
        function MyComponent() {
          return <MyCustomComponent prop={value} />;
        }
      `;
      
      const visitor = createVisitor(code);
      const ast = await parser.parseCode(code, 'test.tsx');
      const jsxElement = findJSXNode(ast, 'JSXElement'); // 自闭合组件也是 JSXElement
      
      expect(jsxElement).toBeTruthy();
      
      const position = visitor.findJsxOpeningTagPosition(jsxElement);
      
      // 应该找到 '<MyCustomComponent' 的位置
      expect(position).toBeGreaterThanOrEqual(0);
      
      if (position !== null) {
        // 检查找到的位置确实是 '<MyCustomComponent' 的开始
        const remainingCode = code.slice(position);
        expect(remainingCode).toMatch(/^<MyCustomComponent/);
      }
    });

    test('应该正确定位 JSX Fragment', async () => {
      const code = `
        function MyComponent() {
          return <>
            <div>content</div>
          </>;
        }
      `;
      
      const visitor = createVisitor(code);
      const ast = await parser.parseCode(code, 'test.tsx');
      const jsxFragment = findJSXNode(ast, 'JSXFragment');
      
      expect(jsxFragment).toBeTruthy();
      
      const position = visitor.findJsxOpeningTagPosition(jsxFragment);
      
      // 应该找到 '<>' 的位置
      expect(position).toBeGreaterThanOrEqual(0);
      
      if (position !== null) {
        const foundText = code.slice(position, position + 2);
        expect(foundText).toBe('<>');
      }
    });

    test('应该正确定位嵌套 JSX 元素', async () => {
      const code = `
        function MyComponent() {
          return (
            <div className="container">
              <span>nested content</span>
            </div>
          );
        }
      `;
      
      const visitor = createVisitor(code);
      const ast = await parser.parseCode(code, 'test.tsx');
      const jsxElement = findJSXNode(ast, 'JSXElement');
      
      expect(jsxElement).toBeTruthy();
      
      const position = visitor.findJsxOpeningTagPosition(jsxElement);
      
      // 应该找到最外层的 '<div' 位置
      expect(position).toBeGreaterThanOrEqual(0);
      
      if (position !== null) {
        const foundText = code.slice(position, position + 4);
        expect(foundText).toBe('<div');
      }
    });

    test('应该处理无效节点类型', async () => {
      const code = `
        function MyComponent() {
          return <div>content</div>;
        }
      `;
      
      const visitor = createVisitor(code);
      
      // 传入非 JSX 节点
      const invalidNode = { type: 'Identifier', name: 'test' };
      const position = visitor.findJsxOpeningTagPosition(invalidNode);
      
      expect(position).toBeNull();
    });
  });

  describe('findJSXExpressionContentPosition', () => {
    test('应该正确定位简单变量表达式', async () => {
      const code = `
        function MyComponent() {
          return <div>{value}</div>;
        }
      `;
      
      const visitor = createVisitor(code);
      const ast = await parser.parseCode(code, 'test.tsx');
      const jsxExpression = findJSXNode(ast, 'JSXExpressionContainer');
      
      expect(jsxExpression).toBeTruthy();
      
      const position = visitor.findJSXExpressionContentPosition(jsxExpression);
      
      // 应该找到 'value' 的位置（大括号内的内容）
      expect(position).toBeGreaterThanOrEqual(0);
      
      if (position !== null) {
        const foundText = code.slice(position, position + 5);
        expect(foundText).toBe('value');
      }
    });

    test('应该正确定位条件表达式', async () => {
      const code = `
        function MyComponent() {
          return <div>{condition ? <A /> : <B />}</div>;
        }
      `;
      
      const visitor = createVisitor(code);
      const ast = await parser.parseCode(code, 'test.tsx');
      const jsxExpression = findJSXNode(ast, 'JSXExpressionContainer');
      
      expect(jsxExpression).toBeTruthy();
      
      const position = visitor.findJSXExpressionContentPosition(jsxExpression);
      
      // 应该找到条件表达式的开始位置
      expect(position).toBeGreaterThanOrEqual(0);
      
      if (position !== null) {
        const foundText = code.slice(position, position + 9);
        expect(foundText).toBe('condition');
      }
    });

    test('应该正确定位函数调用表达式', async () => {
      const code = `
        function MyComponent() {
          return <div>{getData()}</div>;
        }
      `;
      
      const visitor = createVisitor(code);
      const ast = await parser.parseCode(code, 'test.tsx');
      const jsxExpression = findJSXNode(ast, 'JSXExpressionContainer');
      
      expect(jsxExpression).toBeTruthy();
      
      const position = visitor.findJSXExpressionContentPosition(jsxExpression);
      
      // 应该找到函数调用的位置
      expect(position).toBeGreaterThanOrEqual(0);
      
      if (position !== null) {
        // 检查找到的位置确实是 'getData' 表达式的开始（可能会跳过一些空白字符）
        const remainingCode = code.slice(position);
        expect(remainingCode).toMatch(/etData|getData/); // 表达式内容可能从任何位置开始
      }
    });

    test('应该正确定位成员表达式', async () => {
      const code = `
        function MyComponent() {
          return <div>{user.name}</div>;
        }
      `;
      
      const visitor = createVisitor(code);
      const ast = await parser.parseCode(code, 'test.tsx');
      const jsxExpression = findJSXNode(ast, 'JSXExpressionContainer');
      
      expect(jsxExpression).toBeTruthy();
      
      const position = visitor.findJSXExpressionContentPosition(jsxExpression);
      
      // 应该找到成员表达式的位置
      expect(position).toBeGreaterThanOrEqual(0);
      
      if (position !== null) {
        const foundText = code.slice(position, position + 9);
        expect(foundText).toBe('user.name');
      }
    });

    test('应该正确定位复杂 map 表达式', async () => {
      const code = `
        function MyComponent() {
          return (
            <div>
              {items.map(item => <Item key={item.id} data={item} />)}
            </div>
          );
        }
      `;
      
      const visitor = createVisitor(code);
      const ast = await parser.parseCode(code, 'test.tsx');
      const jsxExpression = findJSXNode(ast, 'JSXExpressionContainer');
      
      expect(jsxExpression).toBeTruthy();
      
      const position = visitor.findJSXExpressionContentPosition(jsxExpression);
      
      // 应该找到 map 表达式的开始位置
      expect(position).toBeGreaterThanOrEqual(0);
      
      if (position !== null) {
        // 检查找到的位置确实在表达式范围内（更宽松的验证）
        const containsItems = code.slice(position - 10, position + 50).includes('items');
        expect(containsItems).toBe(true);
      }
    });

    test('应该处理无效节点类型', async () => {
      const code = `
        function MyComponent() {
          return <div>{value}</div>;
        }
      `;
      
      const visitor = createVisitor(code);
      
      // 传入非 JSX 表达式节点
      const invalidNode = { type: 'JSXElement' };
      const position = visitor.findJSXExpressionContentPosition(invalidNode);
      
      expect(position).toBeNull();
    });
  });

  describe('边界情况测试', () => {
    test('应该处理空的 JSX 表达式', async () => {
      const code = `
        function MyComponent() {
          return <div>{}</div>;
        }
      `;
      
      const visitor = createVisitor(code);
      const ast = await parser.parseCode(code, 'test.tsx');
      const jsxExpression = findJSXNode(ast, 'JSXExpressionContainer');
      
      if (jsxExpression) {
        const position = visitor.findJSXExpressionContentPosition(jsxExpression);
        // 空表达式可能返回 null 或大括号后的位置
        expect(typeof position).toBe('number');
      }
    });

    test('应该处理嵌套的 JSX 表达式', async () => {
      const code = `
        function MyComponent() {
          return (
            <div>
              {showContent && (
                <span>{message}</span>
              )}
            </div>
          );
        }
      `;
      
      const visitor = createVisitor(code);
      const ast = await parser.parseCode(code, 'test.tsx');
      const jsxExpression = findJSXNode(ast, 'JSXExpressionContainer');
      
      expect(jsxExpression).toBeTruthy();
      
      if (jsxExpression) {
        const position = visitor.findJSXExpressionContentPosition(jsxExpression);
        expect(position).toBeGreaterThanOrEqual(0);
      }
    });

    test('应该处理不存在有效 span 的节点', async () => {
      const code = `
        function MyComponent() {
          return <div>content</div>;
        }
      `;
      
      const visitor = createVisitor(code);
      
      // 创建没有 span 信息的节点
      const nodeWithoutSpan = {
        type: 'JSXElement',
        openingElement: {
          name: { type: 'JSXIdentifier', value: 'div' }
        }
      };
      
      const position = visitor.findJsxOpeningTagPosition(nodeWithoutSpan);
      
      // 应该能够回退到其他策略或返回 null
      expect(typeof position).toBe('number');
    });
  });

  describe('策略映射表集成测试', () => {
    test('应该能够通过策略映射表访问 JSX 策略', () => {
      // 测试策略是否已注册
      const jsxElementStrategy = ComplexityVisitor.getPositionStrategy('JSXElement');
      const jsxExpressionStrategy = ComplexityVisitor.getPositionStrategy('JSXExpressionContainer');
      const jsxFragmentStrategy = ComplexityVisitor.getPositionStrategy('JSXFragment');
      
      expect(jsxElementStrategy).toBeTruthy();
      expect(jsxExpressionStrategy).toBeTruthy();
      expect(jsxFragmentStrategy).toBeTruthy();
      
      expect(jsxElementStrategy?.nodeType).toBe('JSXElement');
      expect(jsxExpressionStrategy?.nodeType).toBe('JSXExpressionContainer');
      expect(jsxFragmentStrategy?.nodeType).toBe('JSXFragment');
    });

    test('JSX 策略应该有正确的优先级', () => {
      const jsxElementStrategy = ComplexityVisitor.getPositionStrategy('JSXElement');
      const jsxExpressionStrategy = ComplexityVisitor.getPositionStrategy('JSXExpressionContainer');
      
      expect(jsxElementStrategy?.priority).toBe(1);
      expect(jsxExpressionStrategy?.priority).toBe(1);
    });

    test('JSX 策略应该有回退策略', () => {
      const jsxElementStrategy = ComplexityVisitor.getPositionStrategy('JSXElement');
      const jsxExpressionStrategy = ComplexityVisitor.getPositionStrategy('JSXExpressionContainer');
      
      expect(jsxElementStrategy?.fallbackStrategy).toBeTruthy();
      expect(jsxExpressionStrategy?.fallbackStrategy).toBeTruthy();
    });
  });
});