import { describe, test, expect, beforeEach } from 'vitest';
import { ComplexityVisitor } from '../../core/complexity-visitor';
import { DetailCollector } from '../../core/detail-collector';
import { ASTParser } from '../../core/parser';

/**
 * 在 AST 中查找所有箭头函数节点
 * @param ast AST 根节点
 * @returns 箭头函数节点数组
 */
function findArrowFunctions(ast: any): any[] {
  const arrowFunctions: any[] = [];
  
  const traverse = (node: any) => {
    if (!node || typeof node !== 'object') return;
    
    if (node.type === 'ArrowFunctionExpression') {
      arrowFunctions.push(node);
    }
    
    // 递归遍历所有子节点
    for (const key in node) {
      if (key === 'span' || key === 'type') continue;
      
      const value = node[key];
      if (Array.isArray(value)) {
        value.forEach(traverse);
      } else if (value && typeof value === 'object') {
        traverse(value);
      }
    }
  };
  
  traverse(ast);
  return arrowFunctions;
}

/**
 * 箭头函数精确定位测试套件 - Task 6
 * 
 * 测试箭头函数的各种形式和复杂情况的精确定位：
 * - 单参数箭头函数：x => x + 1
 * - 多参数箭头函数：(a, b) => a + b  
 * - 类型注解箭头函数：(x: number): number => x * 2
 * - 无参数箭头函数：() => console.log('hello')
 * - 异步箭头函数：async (x) => await fetch(x)
 * - 复杂返回值：x => ({ value: x })
 * - 嵌套箭头函数
 */
describe('箭头函数精确定位 - Task 6', () => {
  let parser: ASTParser;

  beforeEach(() => {
    parser = new ASTParser();
  });

  // =============================================================================
  // 策略映射表集成测试
  // =============================================================================
  
  describe('策略映射表集成', () => {
    test('应该为 ArrowFunctionExpression 注册策略', () => {
      const arrowStrategy = ComplexityVisitor.getPositionStrategy('ArrowFunctionExpression');
      
      expect(arrowStrategy).toBeDefined();
      expect(arrowStrategy?.nodeType).toBe('ArrowFunctionExpression');
      expect(arrowStrategy?.priority).toBe(1);
      expect(typeof arrowStrategy?.strategy).toBe('function');
      expect(typeof arrowStrategy?.fallbackStrategy).toBe('function');
    });

    test('应该在复杂度计算时使用箭头函数策略', async () => {
      const sourceCode = `
        const add = (a, b) => a + b;
        
        function test() {
          const multiply = x => x * 2;
          return multiply(5);
        }
      `;
      
      const detailCollector = new DetailCollector();
      const ast = await parser.parseCode(sourceCode, 'test.ts');
      const visitor = new ComplexityVisitor(sourceCode, detailCollector);
      
      visitor.visit(ast);
      
      // 验证策略被正确使用
      const results = visitor.getResults();
      expect(results.length).toBeGreaterThan(0); // 应该有函数结果
      
      // 箭头函数本身不增加复杂度，但位置应该被正确记录
      expect(visitor.getTotalComplexity()).toBe(0);
    });
  });

  // =============================================================================
  // 基础箭头函数形式测试
  // =============================================================================
  
  describe('基础箭头函数形式', () => {
    test('单参数箭头函数：x => x + 1', async () => {
      const sourceCode = `
        function test() {
          const square = x => x * x;
          return square(5);
        }
      `;
      
      const detailCollector = new DetailCollector();
      const ast = await parser.parseCode(sourceCode, 'test.ts');
      const visitor = new ComplexityVisitor(sourceCode, detailCollector);
      
      // 测试实例方法
      const arrowFunctions = findArrowFunctions(ast);
      expect(arrowFunctions.length).toBeGreaterThan(0);
      
      for (const arrowFunc of arrowFunctions) {
        const position = visitor.findArrowFunctionPosition(arrowFunc);
        expect(position).not.toBeNull();
        expect(typeof position).toBe('number');
        expect(position).toBeGreaterThanOrEqual(0);
      }
      
      visitor.visit(ast);
      expect(visitor.getTotalComplexity()).toBe(0); // 箭头函数不增加复杂度
    });

    test('多参数箭头函数：(a, b) => a + b', async () => {
      const sourceCode = `
        function test() {
          const add = (a, b) => a + b;
          const subtract = (x, y, z) => x - y - z;
          return add(1, 2);
        }
      `;
      
      const ast = await parser.parseCode(sourceCode, 'test.ts');
      const visitor = new ComplexityVisitor(sourceCode);
      
      const arrowFunctions = findArrowFunctions(ast);
      expect(arrowFunctions.length).toBe(2);
      
      for (const arrowFunc of arrowFunctions) {
        const position = visitor.findArrowFunctionPosition(arrowFunc);
        expect(position).not.toBeNull();
        
        // 验证位置在合理范围内
        if (position !== null) {
          const line = sourceCode.substring(0, position).split('\n').length;
          expect(line).toBeGreaterThan(0);
        }
      }
    });

    test('无参数箭头函数：() => value', async () => {
      const sourceCode = `
        function test() {
          const getValue = () => 42;
          const getArray = () => [1, 2, 3];
          return getValue();
        }
      `;
      
      const ast = await parser.parseCode(sourceCode, 'test.ts');
      const visitor = new ComplexityVisitor(sourceCode);
      
      const arrowFunctions = findArrowFunctions(ast);
      expect(arrowFunctions.length).toBe(2);
      
      for (const arrowFunc of arrowFunctions) {
        const position = visitor.findArrowFunctionPosition(arrowFunc);
        expect(position).not.toBeNull();
        expect(typeof position).toBe('number');
      }
    });

    test('复杂返回值箭头函数：x => ({ value: x })', async () => {
      const sourceCode = `
        function test() {
          const createObject = x => ({ value: x, doubled: x * 2 });
          const createArray = (a, b) => [a, b, a + b];
          return createObject(5);
        }
      `;
      
      const ast = await parser.parseCode(sourceCode, 'test.ts');
      const visitor = new ComplexityVisitor(sourceCode);
      
      const arrowFunctions = findArrowFunctions(ast);
      expect(arrowFunctions.length).toBe(2);
      
      for (const arrowFunc of arrowFunctions) {
        const position = visitor.findArrowFunctionPosition(arrowFunc);
        expect(position).not.toBeNull();
      }
    });
  });

  // =============================================================================
  // TypeScript 特性支持测试
  // =============================================================================
  
  describe('TypeScript 特性支持', () => {
    test('类型注解箭头函数：(x: number): number => x * 2', async () => {
      const sourceCode = `
        function test() {
          const typed: (x: number) => number = (x: number): number => x * 2;
          const generic = <T>(value: T): T => value;
          return typed(5);
        }
      `;
      
      const ast = await parser.parseCode(sourceCode, 'test.ts');
      const visitor = new ComplexityVisitor(sourceCode);
      
      const arrowFunctions = findArrowFunctions(ast);
      expect(arrowFunctions.length).toBeGreaterThan(0);
      
      for (const arrowFunc of arrowFunctions) {
        const position = visitor.findArrowFunctionPosition(arrowFunc);
        // 类型注解的箭头函数也应该能正确定位
        expect(position).not.toBeNull();
      }
    });

    test('泛型箭头函数：<T>(value: T) => T', async () => {
      const sourceCode = `
        function test() {
          const identity = <T>(value: T): T => value;
          const mapper = <T, U>(value: T, fn: (t: T) => U): U => fn(value);
          return identity(42);
        }
      `;
      
      const ast = await parser.parseCode(sourceCode, 'test.ts');
      const visitor = new ComplexityVisitor(sourceCode);
      
      const arrowFunctions = findArrowFunctions(ast);
      expect(arrowFunctions.length).toBeGreaterThan(0);
      
      for (const arrowFunc of arrowFunctions) {
        const position = visitor.findArrowFunctionPosition(arrowFunc);
        expect(position).not.toBeNull();
      }
    });
  });

  // =============================================================================
  // 异步箭头函数测试
  // =============================================================================
  
  describe('异步箭头函数', () => {
    test('异步箭头函数：async (x) => await fetch(x)', async () => {
      const sourceCode = `
        function test() {
          const fetchData = async (url) => await fetch(url);
          const processData = async (data) => {
            return await data.json();
          };
          return fetchData('/api/data');
        }
      `;
      
      const ast = await parser.parseCode(sourceCode, 'test.ts');
      const visitor = new ComplexityVisitor(sourceCode);
      
      const arrowFunctions = findArrowFunctions(ast);
      expect(arrowFunctions.length).toBe(2);
      
      for (const arrowFunc of arrowFunctions) {
        const position = visitor.findArrowFunctionPosition(arrowFunc);
        expect(position).not.toBeNull();
      }
    });

    test('异步箭头函数与Promise：async () => Promise.resolve()', async () => {
      const sourceCode = `
        function test() {
          const asyncValue = async () => Promise.resolve(42);
          const asyncError = async () => Promise.reject(new Error('test'));
          return asyncValue();
        }
      `;
      
      const ast = await parser.parseCode(sourceCode, 'test.ts');
      const visitor = new ComplexityVisitor(sourceCode);
      
      const arrowFunctions = findArrowFunctions(ast);
      expect(arrowFunctions.length).toBe(2);
      
      for (const arrowFunc of arrowFunctions) {
        const position = visitor.findArrowFunctionPosition(arrowFunc);
        expect(position).not.toBeNull();
      }
    });
  });

  // =============================================================================
  // 嵌套和复杂场景测试
  // =============================================================================
  
  describe('嵌套和复杂场景', () => {
    test('嵌套箭头函数', async () => {
      const sourceCode = `
        function test() {
          const curry = a => b => c => a + b + c;
          const complexNested = x => y => z => {
            return (a, b) => x * y * z + a + b;
          };
          return curry(1)(2)(3);
        }
      `;
      
      const ast = await parser.parseCode(sourceCode, 'test.ts');
      const visitor = new ComplexityVisitor(sourceCode);
      
      const arrowFunctions = findArrowFunctions(ast);
      expect(arrowFunctions.length).toBeGreaterThan(3); // 多个嵌套的箭头函数
      
      for (const arrowFunc of arrowFunctions) {
        const position = visitor.findArrowFunctionPosition(arrowFunc);
        expect(position).not.toBeNull();
      }
    });

    test('数组方法中的箭头函数', async () => {
      const sourceCode = `
        function test() {
          const numbers = [1, 2, 3, 4, 5];
          const result = numbers
            .filter(x => x > 2)
            .map((x, index) => ({ value: x, index }))
            .reduce((acc, item) => acc + item.value, 0);
          return result;
        }
      `;
      
      const ast = await parser.parseCode(sourceCode, 'test.ts');
      const visitor = new ComplexityVisitor(sourceCode);
      
      const arrowFunctions = findArrowFunctions(ast);
      expect(arrowFunctions.length).toBe(3); // filter, map, reduce 的箭头函数
      
      for (const arrowFunc of arrowFunctions) {
        const position = visitor.findArrowFunctionPosition(arrowFunc);
        expect(position).not.toBeNull();
      }
    });

    test('箭头函数作为参数传递', async () => {
      const sourceCode = `
        function test() {
          const setTimeout = (callback, delay) => {};
          const addEventListener = (event, handler) => {};
          
          setTimeout(() => console.log('timeout'), 1000);
          addEventListener('click', (event) => {
            console.log('clicked');
          });
          
          return Promise.resolve()
            .then(value => value * 2)
            .catch(error => console.error(error));
        }
      `;
      
      const ast = await parser.parseCode(sourceCode, 'test.ts');
      const visitor = new ComplexityVisitor(sourceCode);
      
      const arrowFunctions = findArrowFunctions(ast);
      expect(arrowFunctions.length).toBeGreaterThan(3);
      
      for (const arrowFunc of arrowFunctions) {
        const position = visitor.findArrowFunctionPosition(arrowFunc);
        expect(position).not.toBeNull();
      }
    });
  });

  // =============================================================================
  // 边界情况和错误处理测试
  // =============================================================================
  
  describe('边界情况和错误处理', () => {
    test('应该处理格式不规范的箭头函数', async () => {
      const sourceCode = `function test(){const f=x=>x+1;const g=(a,b)=>a*b;return f(g(2,3));}`;
      
      const ast = await parser.parseCode(sourceCode, 'test.ts');
      const visitor = new ComplexityVisitor(sourceCode);
      
      const arrowFunctions = findArrowFunctions(ast);
      expect(arrowFunctions.length).toBe(2);
      
      // 即使代码格式紧凑，也应该能正确定位
      for (const arrowFunc of arrowFunctions) {
        const position = visitor.findArrowFunctionPosition(arrowFunc);
        expect(position).not.toBeNull();
        expect(typeof position).toBe('number');
      }
    });

    test('应该处理包含注释的箭头函数', async () => {
      const sourceCode = `
        function test() {
          // 这是一个简单的箭头函数
          const simple = /* 参数 */ x /* 箭头 */ => /* 函数体 */ x + 1;
          
          const complex = (
            a, // 第一个参数
            b  // 第二个参数
          ) => {
            // 复杂的函数体
            return a + b;
          };
          
          return simple(5);
        }
      `;
      
      const ast = await parser.parseCode(sourceCode, 'test.ts');
      const visitor = new ComplexityVisitor(sourceCode);
      
      const arrowFunctions = findArrowFunctions(ast);
      expect(arrowFunctions.length).toBe(2);
      
      for (const arrowFunc of arrowFunctions) {
        const position = visitor.findArrowFunctionPosition(arrowFunc);
        expect(position).not.toBeNull();
      }
    });

    test('应该处理无效或损坏的箭头函数节点', () => {
      const visitor = new ComplexityVisitor('const f = x => x;');
      
      // 测试各种无效输入
      expect(visitor.findArrowFunctionPosition(null)).toBeNull();
      expect(visitor.findArrowFunctionPosition(undefined)).toBeNull();
      expect(visitor.findArrowFunctionPosition({})).toBeNull();
      expect(visitor.findArrowFunctionPosition({ type: 'FunctionDeclaration' })).toBeNull();
      
      // 测试不完整的箭头函数节点
      const incompleteNode = { type: 'ArrowFunctionExpression' };
      const result = visitor.findArrowFunctionPosition(incompleteNode);
      // 应该返回 null 或一个有效的数字位置，因为它可能会使用 span 作为回退
      expect(typeof result === 'number' || result === null).toBe(true);
    });

    test('应该正确记录错误信息', async () => {
      const sourceCode = `
        function test() {
          const arrow = x => x + 1;
          return arrow(5);
        }
      `;
      
      const detailCollector = new DetailCollector();
      const ast = await parser.parseCode(sourceCode, 'test.ts');
      const visitor = new ComplexityVisitor(sourceCode, detailCollector);
      
      // 模拟一个可能出错的场景
      const arrowFunctions = findArrowFunctions(ast);
      if (arrowFunctions.length > 0) {
        // 强制触发错误路径（通过修改源代码为空）
        const corruptedVisitor = new ComplexityVisitor('', detailCollector);
        const position = corruptedVisitor.findArrowFunctionPosition(arrowFunctions[0]);
        
        // 应该处理错误而不是崩溃（可能返回null或有效位置）
        expect(typeof position === 'number' || position === null).toBe(true);
      }
    });
  });

  // =============================================================================
  // 性能测试
  // =============================================================================
  
  describe('性能测试', () => {
    test('应该在大量箭头函数场景下保持性能', async () => {
      // 生成包含100个箭头函数的代码
      const arrowFunctions = Array.from({ length: 100 }, (_, i) => 
        `  const func${i} = x => x * ${i + 1};`
      ).join('\n');
      
      const sourceCode = `
        function test() {
${arrowFunctions}
          return func0(5);
        }
      `;
      
      const startTime = Date.now();
      const ast = await parser.parseCode(sourceCode, 'test.ts');
      const visitor = new ComplexityVisitor(sourceCode);
      
      const arrowFunctionNodes = findArrowFunctions(ast);
      expect(arrowFunctionNodes.length).toBe(100);
      
      // 测试所有箭头函数的位置查找
      for (const arrowFunc of arrowFunctionNodes) {
        const position = visitor.findArrowFunctionPosition(arrowFunc);
        expect(position).not.toBeNull();
      }
      
      const endTime = Date.now();
      
      // 处理时间应该在合理范围内（小于2秒）
      expect(endTime - startTime).toBeLessThan(2000);
    });
  });
});