import { describe, it, expect, beforeEach } from 'vitest';
import { ComplexityVisitor } from '../../core/complexity-visitor';
import { DetailCollector } from '../../core/detail-collector';
import type { PositionStrategyEntry } from '../../core/types';

describe('Strategy Mapping Table Enhancement - Task 8', () => {
  let visitor: ComplexityVisitor;
  let detailCollector: DetailCollector;

  beforeEach(() => {
    detailCollector = new DetailCollector();
  });

  describe('策略映射表扩展', () => {
    it('应该注册所有新的控制流语句策略', () => {
      const controlFlowTypes = [
        'DoWhileStatement',
        'SwitchStatement', 
        'TryStatement',
        'CatchClause'
      ];

      for (const nodeType of controlFlowTypes) {
        const strategy = ComplexityVisitor.getPositionStrategy(nodeType);
        expect(strategy).toBeDefined();
        expect(strategy?.nodeType).toBe(nodeType);
        expect(strategy?.priority).toBe(1);
        expect(strategy?.strategy).toBeTypeOf('function');
        expect(strategy?.fallbackStrategy).toBeTypeOf('function');
      }
    });

    it('应该注册条件表达式策略', () => {
      const strategy = ComplexityVisitor.getPositionStrategy('ConditionalExpression');
      expect(strategy).toBeDefined();
      expect(strategy?.nodeType).toBe('ConditionalExpression');
      expect(strategy?.priority).toBe(1);
    });

    it('应该注册函数表达式策略', () => {
      const functionTypes = ['FunctionExpression', 'FunctionDeclaration'];
      
      for (const nodeType of functionTypes) {
        const strategy = ComplexityVisitor.getPositionStrategy(nodeType);
        expect(strategy).toBeDefined();
        expect(strategy?.nodeType).toBe(nodeType);
        expect(strategy?.priority).toBe(1);
      }
    });

    it('应该注册方法定义策略', () => {
      const strategy = ComplexityVisitor.getPositionStrategy('MethodDefinition');
      expect(strategy).toBeDefined();
      expect(strategy?.nodeType).toBe('MethodDefinition');
      expect(strategy?.priority).toBe(2); // 更高优先级
    });

    it('应该注册类型注解策略', () => {
      const typeTypes = ['TypeAnnotation', 'TSTypeAnnotation'];
      
      for (const nodeType of typeTypes) {
        const strategy = ComplexityVisitor.getPositionStrategy(nodeType);
        expect(strategy).toBeDefined();
        expect(strategy?.nodeType).toBe(nodeType);
        expect(strategy?.priority).toBe(1);
      }
    });
  });

  describe('DoWhile 语句定位', () => {
    it('应该准确定位 do-while 语句的 do 关键字', () => {
      const sourceCode = `
function test() {
  let i = 0;
  do {
    console.log(i);
    i++;
  } while (i < 5);
}`;

      visitor = new ComplexityVisitor(sourceCode, detailCollector);
      
      // 创建模拟的 DoWhileStatement 节点
      const mockNode = {
        type: 'DoWhileStatement',
        span: { start: 35, end: 95 },
        body: {
          type: 'BlockStatement',
          span: { start: 38, end: 75 }
        },
        test: {
          type: 'BinaryExpression',
          span: { start: 85, end: 90 }
        }
      };

      const position = visitor.findKeywordPosition(mockNode, 'do');
      expect(position).toBeGreaterThan(0);
      
      // 验证找到的位置确实是 'do' 关键字
      const foundKeyword = sourceCode.slice(position!, position! + 2);
      expect(foundKeyword).toBe('do');
    });
  });

  describe('Switch 语句定位', () => {
    it('应该准确定位 switch 语句的 switch 关键字', () => {
      const sourceCode = `
function test(value) {
  switch (value) {
    case 1:
      return 'one';
    case 2:
      return 'two';
    default:
      return 'other';
  }
}`;

      visitor = new ComplexityVisitor(sourceCode, detailCollector);
      
      // 创建模拟的 SwitchStatement 节点
      const mockNode = {
        type: 'SwitchStatement',
        span: { start: 25, end: 145 },
        discriminant: {
          type: 'Identifier',
          span: { start: 33, end: 38 }
        }
      };

      const position = visitor.findKeywordPosition(mockNode, 'switch');
      expect(position).toBeGreaterThan(0);
      
      // 验证找到的位置确实是 'switch' 关键字
      const foundKeyword = sourceCode.slice(position!, position! + 6);
      expect(foundKeyword).toBe('switch');
    });
  });

  describe('Try-Catch 语句定位', () => {
    it('应该准确定位 try 语句的 try 关键字', () => {
      const sourceCode = `
function test() {
  try {
    riskyOperation();
  } catch (error) {
    handleError(error);
  }
}`;

      visitor = new ComplexityVisitor(sourceCode, detailCollector);
      
      // 创建模拟的 TryStatement 节点
      const mockNode = {
        type: 'TryStatement',
        span: { start: 21, end: 95 },
        block: {
          type: 'BlockStatement',
          span: { start: 25, end: 50 }
        }
      };

      const position = visitor.findKeywordPosition(mockNode, 'try');
      expect(position).toBeGreaterThan(0);
      
      // 验证找到的位置确实是 'try' 关键字
      const foundKeyword = sourceCode.slice(position!, position! + 3);
      expect(foundKeyword).toBe('try');
    });

    it('应该准确定位 catch 子句的 catch 关键字', () => {
      const sourceCode = `
function test() {
  try {
    riskyOperation();
  } catch (error) {
    handleError(error);
  }
}`;

      visitor = new ComplexityVisitor(sourceCode, detailCollector);
      
      // 创建模拟的 CatchClause 节点
      const mockNode = {
        type: 'CatchClause',
        span: { start: 52, end: 93 },
        param: {
          type: 'Identifier',
          span: { start: 59, end: 64 }
        }
      };

      const position = visitor.findKeywordPosition(mockNode, 'catch');
      expect(position).toBeGreaterThan(0);
      
      // 验证找到的位置确实是 'catch' 关键字
      const foundKeyword = sourceCode.slice(position!, position! + 5);
      expect(foundKeyword).toBe('catch');
    });
  });

  describe('三元运算符定位', () => {
    it('应该准确定位三元运算符的 ? 符号', () => {
      const sourceCode = `
function test(condition) {
  const result = condition ? 'yes' : 'no';
  return result;
}`;

      visitor = new ComplexityVisitor(sourceCode, detailCollector);
      
      // 创建模拟的 ConditionalExpression 节点
      const mockNode = {
        type: 'ConditionalExpression',
        span: { start: 43, end: 66 },
        test: {
          type: 'Identifier',
          span: { start: 43, end: 52 }
        },
        consequent: {
          type: 'StringLiteral',
          span: { start: 55, end: 60 }
        },
        alternate: {
          type: 'StringLiteral',
          span: { start: 63, end: 67 }
        }
      };

      const position = visitor.findTernaryOperatorPosition(mockNode);
      expect(position).not.toBeNull();
      
      if (position !== null) {
        // 验证找到的位置确实是 '?' 符号
        const foundSymbol = sourceCode.slice(position, position + 1);
        expect(foundSymbol).toBe('?');
      }
    });

    it('应该处理复杂的嵌套三元运算符', () => {
      const sourceCode = `
const result = a > b ? (c > d ? 'max' : 'mid') : 'min';`;

      visitor = new ComplexityVisitor(sourceCode, detailCollector);
      
      // 创建模拟的外层 ConditionalExpression 节点
      const mockNode = {
        type: 'ConditionalExpression',
        span: { start: 15, end: 52 },
        test: {
          type: 'BinaryExpression',
          span: { start: 15, end: 20 }
        },
        consequent: {
          type: 'ConditionalExpression',
          span: { start: 23, end: 43 }
        }
      };

      const position = visitor.findTernaryOperatorPosition(mockNode);
      expect(position).toBeGreaterThan(0);
      
      // 验证找到的是第一个 '?' 符号
      const foundSymbol = sourceCode.slice(position!, position! + 1);
      expect(foundSymbol).toBe('?');
    });
  });

  describe('方法定义定位', () => {
    it('应该定位普通方法定义', () => {
      const sourceCode = `
class TestClass {
  normalMethod() {
    return 'normal';
  }
}`;

      visitor = new ComplexityVisitor(sourceCode, detailCollector);
      
      // 创建模拟的 MethodDefinition 节点
      const mockNode = {
        type: 'MethodDefinition',
        span: { start: 20, end: 65 },
        key: {
          type: 'Identifier',
          span: { start: 20, end: 32 },
          value: 'normalMethod'
        },
        kind: 'method'
      };

      const position = visitor.findMethodKeywordPosition(mockNode);
      expect(position).not.toBeNull();
      
      if (position !== null) {
        // 对于普通方法，应该定位到方法名
        const foundMethod = sourceCode.slice(position, position + 12);
        expect(foundMethod).toBe('normalMethod');
      }
    });

    it('应该定位 getter 方法', () => {
      const sourceCode = `
class TestClass {
  get value() {
    return this._value;
  }
}`;

      visitor = new ComplexityVisitor(sourceCode, detailCollector);
      
      // 创建模拟的 MethodDefinition 节点
      const mockNode = {
        type: 'MethodDefinition',
        span: { start: 20, end: 60 },
        key: {
          type: 'Identifier',
          span: { start: 24, end: 29 },
          value: 'value'
        },
        kind: 'get'
      };

      const position = visitor.findMethodKeywordPosition(mockNode);
      expect(position).not.toBeNull();
      
      if (position !== null) {
        // 对于 getter，应该定位到 'get' 关键字
        const foundKeyword = sourceCode.slice(position, position + 3);
        expect(foundKeyword).toBe('get');
      }
    });

    it('应该定位 setter 方法', () => {
      const sourceCode = `
class TestClass {
  set value(newValue) {
    this._value = newValue;
  }
}`;

      visitor = new ComplexityVisitor(sourceCode, detailCollector);
      
      // 创建模拟的 MethodDefinition 节点
      const mockNode = {
        type: 'MethodDefinition',
        span: { start: 20, end: 70 },
        key: {
          type: 'Identifier',
          span: { start: 24, end: 29 },
          value: 'value'
        },
        kind: 'set'
      };

      const position = visitor.findMethodKeywordPosition(mockNode);
      expect(position).toBeGreaterThan(0);
      
      // 对于 setter，应该定位到 'set' 关键字
      const foundKeyword = sourceCode.slice(position!, position! + 3);
      expect(foundKeyword).toBe('set');
    });

    it('应该定位 async 方法', () => {
      const sourceCode = `
class TestClass {
  async asyncMethod() {
    return await someOperation();
  }
}`;

      visitor = new ComplexityVisitor(sourceCode, detailCollector);
      
      // 创建模拟的 MethodDefinition 节点
      const mockNode = {
        type: 'MethodDefinition',
        span: { start: 20, end: 75 },
        key: {
          type: 'Identifier',
          span: { start: 26, end: 37 },
          value: 'asyncMethod'
        },
        async: true,
        kind: 'method'
      };

      const position = visitor.findMethodKeywordPosition(mockNode);
      expect(position).toBeGreaterThan(0);
      
      // 对于 async 方法，应该定位到 'async' 关键字
      const foundKeyword = sourceCode.slice(position!, position! + 5);
      expect(foundKeyword).toBe('async');
    });

    it('应该定位 static 方法', () => {
      const sourceCode = `
class TestClass {
  static staticMethod() {
    return 'static';
  }
}`;

      visitor = new ComplexityVisitor(sourceCode, detailCollector);
      
      // 创建模拟的 MethodDefinition 节点
      const mockNode = {
        type: 'MethodDefinition',
        span: { start: 20, end: 65 },
        key: {
          type: 'Identifier',
          span: { start: 27, end: 39 },
          value: 'staticMethod'
        },
        static: true,
        kind: 'method'
      };

      const position = visitor.findMethodKeywordPosition(mockNode);
      expect(position).toBeGreaterThan(0);
      
      // 对于 static 方法，应该定位到 'static' 关键字
      const foundKeyword = sourceCode.slice(position!, position! + 6);
      expect(foundKeyword).toBe('static');
    });
  });

  describe('类型注解定位', () => {
    it('应该定位函数参数的类型注解', () => {
      const sourceCode = `
function test(value: string): number {
  return value.length;
}`;

      visitor = new ComplexityVisitor(sourceCode, detailCollector);
      
      // 创建模拟的 TypeAnnotation 节点
      const mockNode = {
        type: 'TSTypeAnnotation',
        span: { start: 19, end: 27 },
        typeAnnotation: {
          type: 'TSStringKeyword',
          span: { start: 21, end: 27 }
        }
      };

      const position = visitor.findTypeColonPosition(mockNode);
      expect(position).toBeGreaterThan(0);
      
      // 验证找到的位置确实是 ':' 符号
      const foundSymbol = sourceCode.slice(position!, position! + 1);
      expect(foundSymbol).toBe(':');
    });

    it('应该定位变量的类型注解', () => {
      const sourceCode = `
const count: number = 42;`;

      visitor = new ComplexityVisitor(sourceCode, detailCollector);
      
      // 创建模拟的 TypeAnnotation 节点
      const mockNode = {
        type: 'TSTypeAnnotation',
        span: { start: 11, end: 19 },
        typeAnnotation: {
          type: 'TSNumberKeyword',
          span: { start: 13, end: 19 }
        }
      };

      const position = visitor.findTypeColonPosition(mockNode);
      expect(position).toBeGreaterThan(0);
      
      // 验证找到的位置确实是 ':' 符号
      const foundSymbol = sourceCode.slice(position!, position! + 1);
      expect(foundSymbol).toBe(':');
    });
  });

  describe('策略优先级和回退机制', () => {
    it('应该使用正确的策略优先级', () => {
      // MethodDefinition 应该有更高的优先级 (2)
      const methodStrategy = ComplexityVisitor.getPositionStrategy('MethodDefinition');
      expect(methodStrategy?.priority).toBe(2);

      // 其他策略应该有标准优先级 (1)
      const ifStrategy = ComplexityVisitor.getPositionStrategy('IfStatement');
      expect(ifStrategy?.priority).toBe(1);
    });

    it('应该提供回退策略', () => {
      const controlFlowTypes = [
        'DoWhileStatement',
        'SwitchStatement', 
        'TryStatement',
        'CatchClause',
        'ConditionalExpression',
        'FunctionExpression',
        'FunctionDeclaration',
        'MethodDefinition',
        'TypeAnnotation',
        'TSTypeAnnotation'
      ];

      for (const nodeType of controlFlowTypes) {
        const strategy = ComplexityVisitor.getPositionStrategy(nodeType);
        expect(strategy?.fallbackStrategy).toBeDefined();
        expect(strategy?.fallbackStrategy).toBeTypeOf('function');
      }
    });
  });

  describe('运行时策略扩展', () => {
    it('应该支持运行时注册新策略', () => {
      const customEntry: PositionStrategyEntry = {
        nodeType: 'CustomNode',
        strategy: (node) => node.span?.start || null,
        priority: 3,
        fallbackStrategy: (node) => null
      };

      ComplexityVisitor.registerPositionStrategy(customEntry);
      
      const registeredStrategy = ComplexityVisitor.getPositionStrategy('CustomNode');
      expect(registeredStrategy).toBeDefined();
      expect(registeredStrategy?.nodeType).toBe('CustomNode');
      expect(registeredStrategy?.priority).toBe(3);
    });
  });

  describe('边界情况处理', () => {
    it('应该优雅处理无效的节点输入', () => {
      visitor = new ComplexityVisitor('', detailCollector);
      
      // 测试 null 节点
      const nullResult = visitor.findTernaryOperatorPosition(null);
      expect(nullResult).toBeNull();
      
      // 测试错误类型的节点
      const wrongTypeResult = visitor.findTernaryOperatorPosition({ type: 'WrongType' });
      expect(wrongTypeResult).toBeNull();
    });

    it('应该处理缺少 span 信息的节点', () => {
      visitor = new ComplexityVisitor('some code', detailCollector);
      
      const nodeWithoutSpan = {
        type: 'ConditionalExpression',
        test: { type: 'Identifier' },
        consequent: { type: 'StringLiteral' },
        alternate: { type: 'StringLiteral' }
      };
      
      const result = visitor.findTernaryOperatorPosition(nodeWithoutSpan);
      // 应该返回 null 或者使用回退策略
      expect(result).toBeNull();
    });

    it('应该处理空源代码', () => {
      visitor = new ComplexityVisitor('', detailCollector);
      
      const mockNode = {
        type: 'ConditionalExpression',
        span: { start: 0, end: 10 },
        test: { type: 'Identifier', span: { start: 0, end: 5 } }
      };
      
      const result = visitor.findTernaryOperatorPosition(mockNode);
      expect(result).toBeNull();
    });
  });
});