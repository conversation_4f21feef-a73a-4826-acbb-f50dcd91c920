/**
 * Visitor Pattern 重构性能基准测试
 * 对比重构前后的分析速度，确保新架构不会显著降低性能
 */

import { describe, test, expect, beforeEach, afterEach } from 'vitest';
import { promises as fs } from 'fs';
import { join } from 'path';
import { ComplexityCalculator } from '../../core/calculator';
import { BaseVisitor } from '../../core/base-visitor';
import { FunctionFinderVisitor } from '../../core/function-finder-visitor';
import { SemanticComplexityVisitor } from '../../core/complexity-visitor-refactored';
import { SemanticComplexityVisitorFactory } from '../../core/semantic-complexity-visitor-factory';
import { ASTParser } from '../../core/parser';

describe('Visitor Pattern 性能基准测试', () => {
  let tempDir: string;
  let testFiles: string[];
  let calculator: ComplexityCalculator;
  let parser: ASTParser;

  beforeEach(async () => {
    // 创建临时测试目录
    tempDir = join(process.cwd(), 'visitor-perf-test-' + Date.now());
    await fs.mkdir(tempDir, { recursive: true });
    
    // 创建测试文件
    testFiles = await createBenchmarkTestFiles(tempDir);
    
    // 初始化计算器和解析器
    calculator = new ComplexityCalculator({
      enableMixedLogicOperatorPenalty: true,
      recursionChainThreshold: 10,
      enableDetailedReporting: true,
    });
    
    parser = new ASTParser();
  });

  afterEach(async () => {
    // 清理临时文件
    try {
      await fs.rm(tempDir, { recursive: true, force: true });
    } catch (error) {
      // 忽略清理错误
    }
  });

  describe('Visitor 架构性能基准', () => {
    test('单文件分析性能基准 - 新架构', async () => {
      const testFile = testFiles[0]!;
      const iterations = 10;
      const times: number[] = [];
      
      // 预热
      await calculator.calculateFile(testFile);
      
      // 测量性能
      for (let i = 0; i < iterations; i++) {
        const startTime = performance.now();
        const results = await calculator.calculateFile(testFile);
        const endTime = performance.now();
        times.push(endTime - startTime);
        
        // 验证结果
        expect(results.length).toBeGreaterThan(0);
      }
      
      const averageTime = times.reduce((a, b) => a + b, 0) / times.length;
      const maxTime = Math.max(...times);
      const minTime = Math.min(...times);
      const stdDev = Math.sqrt(times.reduce((acc, time) => acc + Math.pow(time - averageTime, 2), 0) / times.length);
      
      // 性能基准：新架构应该保持良好性能
      expect(averageTime).toBeLessThan(200); // 200ms 基准
      expect(maxTime).toBeLessThan(500); // 最大时间不超过 500ms
      expect(stdDev).toBeLessThan(100); // 标准差小于 100ms，确保稳定性
      
      console.log(`新架构单文件分析性能: 平均 ${averageTime.toFixed(2)}ms, 最大 ${maxTime.toFixed(2)}ms, 最小 ${minTime.toFixed(2)}ms, 标准差 ${stdDev.toFixed(2)}ms`);
    });

    test('BaseVisitor 遍历性能测试', async () => {
      const testFile = testFiles[1]!; // 使用中等复杂度文件
      const sourceCode = await fs.readFile(testFile, 'utf-8');
      const ast = await parser.parseCode(sourceCode, testFile);
      
      // 创建一个简单的 BaseVisitor 实现用于测试
      class TestVisitor extends BaseVisitor {
        protected visitNode(node: any): any {
          // 简单的节点访问，只返回原节点
          return node;
        }
      }
      
      const iterations = 20;
      const times: number[] = [];
      
      for (let i = 0; i < iterations; i++) {
        const visitor = new TestVisitor();
        
        const startTime = performance.now();
        visitor.visit(ast);
        const endTime = performance.now();
        
        times.push(endTime - startTime);
      }
      
      const averageTime = times.reduce((a, b) => a + b, 0) / times.length;
      const maxTime = Math.max(...times);
      
      // BaseVisitor 应该非常快，因为只做遍历和栈管理
      expect(averageTime).toBeLessThan(50); // 调整期望时间，因为要遍历整个 AST
      expect(maxTime).toBeLessThan(100); // 最大时间不超过 100ms
      
      console.log(`BaseVisitor 遍历性能: 平均 ${averageTime.toFixed(2)}ms, 最大 ${maxTime.toFixed(2)}ms`);
    });

    test('FunctionFinderVisitor 查找性能测试', async () => {
      const testFile = testFiles[2]!; // 使用包含多个函数的文件
      const sourceCode = await fs.readFile(testFile, 'utf-8');
      const ast = await parser.parseCode(sourceCode, testFile);
      
      const iterations = 15;
      const times: number[] = [];
      
      for (let i = 0; i < iterations; i++) {
        const startTime = performance.now();
        const functions = FunctionFinderVisitor.find(ast);
        const endTime = performance.now();
        
        times.push(endTime - startTime);
        expect(functions.length).toBeGreaterThan(0);
      }
      
      const averageTime = times.reduce((a, b) => a + b, 0) / times.length;
      
      // 函数查找应该很快
      expect(averageTime).toBeLessThan(20); // 应该在 20ms 以内
      
      console.log(`FunctionFinderVisitor 查找性能: 平均 ${averageTime.toFixed(2)}ms`);
    });

    test('SemanticComplexityVisitor 计算性能测试', async () => {
      const testFile = testFiles[1]!;
      const sourceCode = await fs.readFile(testFile, 'utf-8');
      const ast = await parser.parseCode(sourceCode, testFile);
      const functions = FunctionFinderVisitor.find(ast);
      
      if (functions.length === 0) {
        throw new Error('测试文件中没有找到函数');
      }
      
      const iterations = 10;
      const times: number[] = [];
      
      for (let i = 0; i < iterations; i++) {
        const startTime = performance.now();
        
        // 为每个函数创建 SemanticComplexityVisitor 并计算复杂度
        for (const func of functions) {
          const visitor = SemanticComplexityVisitorFactory.createLightweight(sourceCode);
          visitor.visit(func);
          const complexity = visitor.getComplexity();
          expect(complexity).toBeGreaterThanOrEqual(0);
        }
        
        const endTime = performance.now();
        times.push(endTime - startTime);
      }
      
      const averageTime = times.reduce((a, b) => a + b, 0) / times.length;
      
      // 复杂度计算性能应该合理
      expect(averageTime).toBeLessThan(100); // 应该在 100ms 以内
      
      console.log(`SemanticComplexityVisitor 计算性能: ${functions.length} 个函数，平均 ${averageTime.toFixed(2)}ms`);
    });
  });

  describe('内存效率测试', () => {
    test('Visitor 实例内存开销', async () => {
      // 强制垃圾回收
      if (global.gc) {
        global.gc();
      }
      
      const memoryBefore = process.memoryUsage();
      
      // 创建多个 Visitor 实例并使用
      const testFile = testFiles[0]!;
      const sourceCode = await fs.readFile(testFile, 'utf-8');
      const ast = await parser.parseCode(sourceCode, testFile);
      
      const visitors: BaseVisitor[] = [];
      const results: any[] = [];
      
      // 创建多个实例并使用它们
      for (let i = 0; i < 50; i++) {
        const baseVisitor = new BaseVisitor();
        const complexityVisitor = SemanticComplexityVisitorFactory.createLightweight(sourceCode);
        
        visitors.push(baseVisitor, complexityVisitor);
        
        // 使用访问者
        baseVisitor.visit(ast);
        const functions = FunctionFinderVisitor.find(ast);
        
        if (functions.length > 0) {
          complexityVisitor.visit(functions[0]!);
          results.push(complexityVisitor.getTotalComplexity());
        }
      }
      
      const memoryAfter = process.memoryUsage();
      const heapIncrease = memoryAfter.heapUsed - memoryBefore.heapUsed;
      const heapMB = heapIncrease / 1024 / 1024;
      
      // 内存增长应该在合理范围内
      expect(Math.abs(heapMB)).toBeLessThan(50); // 小于 50MB
      expect(visitors.length).toBe(100); // 确保创建了预期数量的实例
      expect(results.length).toBeGreaterThan(0); // 确保有实际计算结果
      
      console.log(`Visitor 实例内存开销: 100个实例，内存变化 ${heapMB.toFixed(2)}MB`);
    });

    test('父节点栈内存管理', async () => {
      const testFile = testFiles[2]!; // 使用嵌套结构较多的文件
      const sourceCode = await fs.readFile(testFile, 'utf-8');
      const ast = await parser.parseCode(sourceCode, testFile);
      
      // 强制垃圾回收
      if (global.gc) {
        global.gc();
      }
      
      const memoryBefore = process.memoryUsage();
      
      // 使用多个 BaseVisitor 遍历复杂的 AST
      for (let i = 0; i < 20; i++) {
        const visitor = new BaseVisitor();
        visitor.visit(ast);
        
        // 验证栈已被正确清理
        expect((visitor as any).parentStack.length).toBe(0);
      }
      
      const memoryAfter = process.memoryUsage();
      const heapIncrease = memoryAfter.heapUsed - memoryBefore.heapUsed;
      const heapMB = heapIncrease / 1024 / 1024;
      
      // 内存应该没有显著增长（说明栈被正确清理）
      expect(Math.abs(heapMB)).toBeLessThan(10); // 小于 10MB
      
      console.log(`父节点栈内存管理: 20次遍历，内存变化 ${heapMB.toFixed(2)}MB`);
    });
  });

  describe('并发性能测试', () => {
    test('多文件并发分析性能', async () => {
      const concurrentFiles = testFiles.slice(0, Math.min(testFiles.length, 4));
      
      // 串行处理
      const serialStartTime = performance.now();
      const serialResults = [];
      for (const file of concurrentFiles) {
        const result = await calculator.calculateFile(file);
        serialResults.push(result);
      }
      const serialEndTime = performance.now();
      const serialTime = serialEndTime - serialStartTime;
      
      // 并行处理
      const parallelStartTime = performance.now();
      const parallelResults = await Promise.all(
        concurrentFiles.map(file => calculator.calculateFile(file))
      );
      const parallelEndTime = performance.now();
      const parallelTime = parallelEndTime - parallelStartTime;
      
      // 验证结果一致性
      expect(serialResults.length).toBe(parallelResults.length);
      expect(serialResults.length).toBe(concurrentFiles.length);
      
      // 验证并行处理的效率
      expect(parallelTime).toBeGreaterThan(0);
      expect(serialTime).toBeGreaterThan(0);
      expect(parallelTime).toBeLessThan(2000); // 并行处理应该在 2s 内完成
      
      const speedup = serialTime / parallelTime;
      console.log(`并发分析性能: 串行 ${serialTime.toFixed(2)}ms, 并行 ${parallelTime.toFixed(2)}ms, 加速比 ${speedup.toFixed(2)}x`);
      
      // 并行处理应该有一定的性能提升
      expect(speedup).toBeGreaterThanOrEqual(0.8); // 至少不应该显著变慢
    });

    test('Visitor 实例复用 vs 重新创建', async () => {
      const testFile = testFiles[1]!;
      const sourceCode = await fs.readFile(testFile, 'utf-8');
      const ast = await parser.parseCode(sourceCode, testFile);
      const functions = FunctionFinderVisitor.find(ast);
      
      if (functions.length === 0) {
        throw new Error('测试文件中没有找到函数');
      }
      
      const iterations = 100;
      
      // 测试重新创建实例的性能
      const recreateStartTime = performance.now();
      for (let i = 0; i < iterations; i++) {
        const visitor = SemanticComplexityVisitorFactory.createLightweight(sourceCode);
        visitor.visit(functions[0]!);
        visitor.getComplexity();
      }
      const recreateEndTime = performance.now();
      const recreateTime = recreateEndTime - recreateStartTime;
      
      // 测试复用实例的性能（注意：当前实现中 ComplexityVisitor 可能不支持复用）
      const reuseStartTime = performance.now();
      for (let i = 0; i < iterations; i++) {
        // 对于每次使用都创建新实例，因为 ComplexityVisitor 有状态
        const visitor = SemanticComplexityVisitorFactory.createLightweight(sourceCode);
        visitor.visit(functions[0]!);
        visitor.getComplexity();
      }
      const reuseEndTime = performance.now();
      const reuseTime = reuseEndTime - reuseStartTime;
      
      console.log(`Visitor 实例管理: 重新创建 ${recreateTime.toFixed(2)}ms, 复用模式 ${reuseTime.toFixed(2)}ms`);
      
      // 两种方式的性能应该都在合理范围内
      expect(recreateTime).toBeLessThan(5000); // 5s 内完成
      expect(reuseTime).toBeLessThan(5000); // 5s 内完成
    });
  });

  describe('Span 修正性能测试', () => {
    test('结构化 Span 修正性能', async () => {
      const testFile = testFiles[1]!;
      const sourceCode = await fs.readFile(testFile, 'utf-8');
      const ast = await parser.parseCode(sourceCode, testFile);
      const functions = FunctionFinderVisitor.find(ast);
      
      if (functions.length === 0) {
        throw new Error('测试文件中没有找到函数');
      }
      
      const iterations = 50;
      const times: number[] = [];
      
      for (let i = 0; i < iterations; i++) {
        const startTime = performance.now();
        
        // 使用带有详细收集器的 ComplexityVisitor
        const visitor = SemanticComplexityVisitorFactory.createLightweight(sourceCode);
        
        // 遍历所有函数，触发 span 修正逻辑
        for (const func of functions) {
          visitor.visit(func);
        }
        
        const endTime = performance.now();
        times.push(endTime - startTime);
      }
      
      const averageTime = times.reduce((a, b) => a + b, 0) / times.length;
      
      // Span 修正应该不会显著影响性能
      expect(averageTime).toBeLessThan(150); // 150ms 以内
      
      console.log(`结构化 Span 修正性能: ${functions.length} 个函数，平均 ${averageTime.toFixed(2)}ms`);
    });
  });

  describe('性能回归检测', () => {
    test('Visitor 架构性能基准数据收集', async () => {
      const benchmarks = {
        singleFileAnalysis: 0,
        baseVisitorTraversal: 0,
        functionFinding: 0,
        complexityCalculation: 0,
        memoryOverhead: 0,
        concurrentSpeedup: 0,
        spanCorrectionOverhead: 0,
      };
      
      const testFile = testFiles[0]!;
      const sourceCode = await fs.readFile(testFile, 'utf-8');
      const ast = await parser.parseCode(sourceCode, testFile);
      
      // 单文件分析基准
      const singleStartTime = performance.now();
      await calculator.calculateFile(testFile);
      const singleEndTime = performance.now();
      benchmarks.singleFileAnalysis = singleEndTime - singleStartTime;
      
      // BaseVisitor 遍历基准
      const baseVisitor = new BaseVisitor();
      const baseStartTime = performance.now();
      baseVisitor.visit(ast);
      const baseEndTime = performance.now();
      benchmarks.baseVisitorTraversal = baseEndTime - baseStartTime;
      
      // 函数查找基准
      const findStartTime = performance.now();
      const functions = FunctionFinderVisitor.find(ast);
      const findEndTime = performance.now();
      benchmarks.functionFinding = findEndTime - findStartTime;
      
      // 复杂度计算基准
      if (functions.length > 0) {
        const complexityVisitor = SemanticComplexityVisitorFactory.createLightweight(sourceCode);
        const complexityStartTime = performance.now();
        complexityVisitor.visit(functions[0]!);
        complexityVisitor.getComplexity();
        const complexityEndTime = performance.now();
        benchmarks.complexityCalculation = complexityEndTime - complexityStartTime;
      }
      
      // 内存开销基准
      if (global.gc) global.gc();
      const memBefore = process.memoryUsage().heapUsed;
      for (let i = 0; i < 10; i++) {
        const visitor = SemanticComplexityVisitorFactory.createLightweight(sourceCode);
        if (functions.length > 0) {
          visitor.visit(functions[0]!);
        }
      }
      const memAfter = process.memoryUsage().heapUsed;
      benchmarks.memoryOverhead = Math.abs((memAfter - memBefore) / 1024 / 1024);
      
      // 并发加速比基准
      if (testFiles.length >= 2) {
        const files = testFiles.slice(0, 2);
        
        const serialStart = performance.now();
        for (const file of files) {
          await calculator.calculateFile(file);
        }
        const serialEnd = performance.now();
        const serialTime = serialEnd - serialStart;
        
        const parallelStart = performance.now();
        await Promise.all(files.map(file => calculator.calculateFile(file)));
        const parallelEnd = performance.now();
        const parallelTime = parallelEnd - parallelStart;
        
        benchmarks.concurrentSpeedup = serialTime / parallelTime;
      }
      
      // Span 修正开销基准
      if (functions.length > 0) {
        const spanStartTime = performance.now();
        const spanVisitor = SemanticComplexityVisitorFactory.createLightweight(sourceCode);
        spanVisitor.visit(functions[0]!);
        const spanEndTime = performance.now();
        benchmarks.spanCorrectionOverhead = spanEndTime - spanStartTime;
      }
      
      // 验证基准数据合理性
      expect(benchmarks.singleFileAnalysis).toBeGreaterThan(0);
      expect(benchmarks.singleFileAnalysis).toBeLessThan(500);
      expect(benchmarks.baseVisitorTraversal).toBeGreaterThan(0);
      expect(benchmarks.baseVisitorTraversal).toBeLessThan(100);
      expect(benchmarks.functionFinding).toBeGreaterThan(0);
      expect(benchmarks.functionFinding).toBeLessThan(50);
      expect(benchmarks.memoryOverhead).toBeGreaterThanOrEqual(0);
      expect(benchmarks.memoryOverhead).toBeLessThan(100);
      
      console.log('Visitor 架构性能基准数据:', JSON.stringify(benchmarks, null, 2));
      
      // 记录性能指标到文件（可选）
      const benchmarkFile = join(tempDir, 'visitor-benchmarks.json');
      await fs.writeFile(benchmarkFile, JSON.stringify(benchmarks, null, 2));
    });
  });
});

/**
 * 创建基准测试文件
 */
async function createBenchmarkTestFiles(tempDir: string): Promise<string[]> {
  const files: string[] = [];
  
  // 简单文件 - 测试基础性能
  const simpleFile = join(tempDir, 'simple-benchmark.ts');
  await fs.writeFile(simpleFile, `
export function calculateSum(numbers: number[]): number {
  let sum = 0;
  if (numbers.length > 0) {
    for (let i = 0; i < numbers.length; i++) {
      if (numbers[i] > 0) {
        sum += numbers[i];
      } else if (numbers[i] < 0) {
        sum += Math.abs(numbers[i]);
      }
    }
  }
  return sum;
}

export function processArray(items: any[]): any[] {
  const result: any[] = [];
  for (const item of items) {
    if (item && typeof item === 'object') {
      if (item.id && item.value) {
        if (item.value > 10) {
          result.push({ ...item, processed: true });
        } else {
          result.push({ ...item, processed: false });
        }
      }
    }
  }
  return result;
}
  `);
  files.push(simpleFile);
  
  // 中等复杂度文件 - 测试实际场景性能
  const mediumFile = join(tempDir, 'medium-benchmark.ts');
  await fs.writeFile(mediumFile, `
interface DataProcessor {
  process(data: any): any;
  validate(data: any): boolean;
}

export class ComplexDataProcessor implements DataProcessor {
  private config: any;
  private cache: Map<string, any> = new Map();
  
  constructor(config: any) {
    this.config = config;
  }
  
  public process(data: any): any {
    if (!this.validate(data)) {
      throw new Error('Invalid data');
    }
    
    const cacheKey = this.generateCacheKey(data);
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }
    
    let result: any;
    
    if (data.type === 'numerical') {
      result = this.processNumerical(data);
    } else if (data.type === 'textual') {
      result = this.processTextual(data);
    } else if (data.type === 'mixed') {
      result = this.processMixed(data);
    } else {
      result = this.processDefault(data);
    }
    
    if (this.shouldCache(result)) {
      this.cache.set(cacheKey, result);
    }
    
    return result;
  }
  
  public validate(data: any): boolean {
    if (!data) return false;
    if (!data.type) return false;
    if (!data.content) return false;
    
    switch (data.type) {
      case 'numerical':
        return this.validateNumerical(data);
      case 'textual':
        return this.validateTextual(data);
      case 'mixed':
        return this.validateMixed(data);
      default:
        return this.validateDefault(data);
    }
  }
  
  private processNumerical(data: any): any {
    const numbers = data.content as number[];
    let result = 0;
    
    for (const num of numbers) {
      if (num > 0) {
        if (num % 2 === 0) {
          result += num * 2;
        } else {
          result += num * 3;
        }
      } else if (num < 0) {
        if (Math.abs(num) % 2 === 0) {
          result -= Math.abs(num);
        } else {
          result -= Math.abs(num) * 2;
        }
      }
    }
    
    return { type: 'numerical', result, count: numbers.length };
  }
  
  private processTextual(data: any): any {
    const text = data.content as string;
    let processedText = '';
    
    if (text.length > 100) {
      if (text.includes('important')) {
        processedText = text.toUpperCase();
      } else if (text.includes('sensitive')) {
        processedText = text.replace(/sensitive/g, '[REDACTED]');
      } else {
        processedText = text.toLowerCase();
      }
    } else {
      if (text.startsWith('prefix')) {
        processedText = text.substring(6);
      } else if (text.endsWith('suffix')) {
        processedText = text.substring(0, text.length - 6);
      } else {
        processedText = text;
      }
    }
    
    return { type: 'textual', result: processedText, length: text.length };
  }
  
  private processMixed(data: any): any {
    const mixedData = data.content;
    const result: any = {};
    
    for (const key in mixedData) {
      const value = mixedData[key];
      
      if (typeof value === 'number') {
        if (value > 0) {
          result[key] = value * 2;
        } else {
          result[key] = 0;
        }
      } else if (typeof value === 'string') {
        if (value.length > 10) {
          result[key] = value.substring(0, 10);
        } else {
          result[key] = value;
        }
      } else if (typeof value === 'boolean') {
        result[key] = !value;
      } else if (Array.isArray(value)) {
        result[key] = value.filter(item => item != null);
      } else {
        result[key] = null;
      }
    }
    
    return { type: 'mixed', result, keys: Object.keys(mixedData).length };
  }
  
  private processDefault(data: any): any {
    return { type: 'default', result: data.content, processed: true };
  }
  
  private validateNumerical(data: any): boolean {
    return Array.isArray(data.content) && data.content.every((item: any) => typeof item === 'number');
  }
  
  private validateTextual(data: any): boolean {
    return typeof data.content === 'string' && data.content.length > 0;
  }
  
  private validateMixed(data: any): boolean {
    return typeof data.content === 'object' && data.content !== null && !Array.isArray(data.content);
  }
  
  private validateDefault(data: any): boolean {
    return data.content !== undefined;
  }
  
  private generateCacheKey(data: any): string {
    return JSON.stringify(data);
  }
  
  private shouldCache(result: any): boolean {
    return result && Object.keys(result).length > 0;
  }
}
  `);
  files.push(mediumFile);
  
  // 嵌套结构文件 - 测试深度遍历性能
  const nestedFile = join(tempDir, 'nested-benchmark.ts');
  await fs.writeFile(nestedFile, `
export class NestedProcessor {
  public processLevel1(data: any): any {
    if (data.level1) {
      return this.processLevel2(data.level1);
    }
    return null;
  }
  
  private processLevel2(data: any): any {
    if (data.level2) {
      if (data.condition1) {
        return this.processLevel3A(data.level2);
      } else {
        return this.processLevel3B(data.level2);
      }
    }
    return null;
  }
  
  private processLevel3A(data: any): any {
    if (data.level3a) {
      if (data.condition2) {
        if (data.condition3) {
          return this.processLevel4A(data.level3a);
        } else {
          return this.processLevel4B(data.level3a);
        }
      } else {
        if (data.condition4) {
          return this.processLevel4C(data.level3a);
        } else {
          return this.processLevel4D(data.level3a);
        }
      }
    }
    return null;
  }
  
  private processLevel3B(data: any): any {
    if (data.level3b) {
      switch (data.type) {
        case 'typeA':
          if (data.subCondition1) {
            return this.processTypeA1(data.level3b);
          } else {
            return this.processTypeA2(data.level3b);
          }
        case 'typeB':
          if (data.subCondition2) {
            return this.processTypeB1(data.level3b);
          } else {
            return this.processTypeB2(data.level3b);
          }
        case 'typeC':
          if (data.subCondition3) {
            return this.processTypeC1(data.level3b);
          } else {
            return this.processTypeC2(data.level3b);
          }
        default:
          return this.processTypeDefault(data.level3b);
      }
    }
    return null;
  }
  
  private processLevel4A(data: any): any {
    return { result: 'level4A', data };
  }
  
  private processLevel4B(data: any): any {
    return { result: 'level4B', data };
  }
  
  private processLevel4C(data: any): any {
    return { result: 'level4C', data };
  }
  
  private processLevel4D(data: any): any {
    return { result: 'level4D', data };
  }
  
  private processTypeA1(data: any): any {
    return { result: 'typeA1', data };
  }
  
  private processTypeA2(data: any): any {
    return { result: 'typeA2', data };
  }
  
  private processTypeB1(data: any): any {
    return { result: 'typeB1', data };
  }
  
  private processTypeB2(data: any): any {
    return { result: 'typeB2', data };
  }
  
  private processTypeC1(data: any): any {
    return { result: 'typeC1', data };
  }
  
  private processTypeC2(data: any): any {
    return { result: 'typeC2', data };
  }
  
  private processTypeDefault(data: any): any {
    return { result: 'typeDefault', data };
  }
}

// 添加更多函数以增加复杂度
export function recursiveFunction(n: number): number {
  if (n <= 0) {
    return 0;
  } else if (n === 1) {
    return 1;
  } else if (n === 2) {
    return 1;
  } else {
    if (n % 2 === 0) {
      return recursiveFunction(n - 1) + recursiveFunction(n - 2);
    } else {
      return recursiveFunction(n - 1) + recursiveFunction(n - 3);
    }
  }
}

export function complexLoop(items: any[]): any[] {
  const result: any[] = [];
  
  for (let i = 0; i < items.length; i++) {
    const item = items[i];
    
    if (item && typeof item === 'object') {
      for (const key in item) {
        const value = item[key];
        
        if (Array.isArray(value)) {
          for (let j = 0; j < value.length; j++) {
            if (value[j] && value[j].nested) {
              for (const nestedKey in value[j].nested) {
                if (value[j].nested[nestedKey] > 0) {
                  result.push({
                    item: i,
                    key,
                    index: j,
                    nestedKey,
                    value: value[j].nested[nestedKey]
                  });
                }
              }
            }
          }
        }
      }
    }
  }
  
  return result;
}
  `);
  files.push(nestedFile);
  
  return files;
}