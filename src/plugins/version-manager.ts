/**
 * 插件版本管理和兼容性检查系统
 * 处理插件版本控制、升级、降级和兼容性验证
 *
 * 增强版本：集成了类型安全的版本管理器
 */

import type { Plugin, ValidationResult, ValidationError, ValidationWarning, CompatibilityInfo } from './types';

import {
  TypeSafeVersionManager,
  type ValidatedVersionParts,
  type VersionComparisonResult as SafeVersionComparisonResult,
  VersionParsingError,
  VersionComparisonError,
  VersionRangeError,
  type TypeSafePluginUpgradeInfo,
  type TypeSafeCompatibilityCheckResult,
  type TypeSafeCompatibilityIssue,
} from './type-safe-version-manager';

/**
 * 版本范围类型
 */
export type VersionRange = string; // 如: "^1.0.0", "~2.1.0", ">=1.2.0"

/**
 * 版本比较结果
 */
export interface VersionComparisonResult {
  compatible: boolean;
  reason: string;
  recommendation?: string;
}

/**
 * 插件升级信息
 */
export interface PluginUpgradeInfo {
  pluginId: string;
  currentVersion: string;
  availableVersion: string;
  upgradeType: 'major' | 'minor' | 'patch';
  breakingChanges: boolean;
  changelog?: string;
  dependencies: {
    added: string[];
    removed: string[];
    updated: string[];
  };
}

/**
 * 兼容性检查结果
 */
export interface CompatibilityCheckResult {
  isCompatible: boolean;
  issues: CompatibilityIssue[];
  recommendations: string[];
  migrationRequired: boolean;
}

/**
 * 兼容性问题
 */
export interface CompatibilityIssue {
  type: 'version' | 'api' | 'dependency' | 'configuration' | 'engine';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  affectedComponents: string[];
  solution?: string;
}

/**
 * 增强的插件版本管理器
 * 使用组合模式集成类型安全的版本解析和比较
 */
export class PluginVersionManager {
  private readonly typeSafeVersionManager: TypeSafeVersionManager;
  private readonly PLUGIN_ENGINE_VERSION = '1.0.0';
  private readonly PLUGIN_SUPPORTED_NODE_VERSIONS = ['18.0.0', '20.0.0', '21.0.0'] as const;

  constructor() {
    this.typeSafeVersionManager = new TypeSafeVersionManager();
  }

  /**
   * 解析版本字符串 - 使用类型安全实现
   */
  parseVersion(version: string): ValidatedVersionParts {
    return this.typeSafeVersionManager.parseVersion(version);
  }

  /**
   * 解析版本字符串 - 使用类型安全实现（向后兼容）
   * @deprecated 使用 parseVersion 替代，返回 ValidatedVersionParts
   */
  parseVersionLegacy(version: string): VersionParts {
    try {
      const validated = this.typeSafeVersionManager.parseVersion(version);
      return {
        major: validated.major,
        minor: validated.minor,
        patch: validated.patch,
        prerelease: validated.prerelease,
        build: validated.build,
        raw: validated.raw,
      };
    } catch (error: unknown) {
      if (error instanceof VersionParsingError) {
        throw new Error(`Invalid version format: ${version}`);
      }
      throw error;
    }
  }

  // 私有方法 - 类型安全版本

  private async checkEngineCompatibilitySafe(
    plugin: Plugin,
    issues: TypeSafeCompatibilityIssue[],
    recommendations: string[]
  ): Promise<void> {
    if (!plugin.engineVersion) {
      recommendations.push('Consider specifying engineVersion for better compatibility checking');
      return;
    }

    try {
      if (!this.typeSafeVersionManager.satisfiesRange(this.PLUGIN_ENGINE_VERSION, plugin.engineVersion)) {
        issues.push({
          type: 'engine',
          severity: 'critical',
          description: `Plugin requires engine version ${plugin.engineVersion}, but current engine is ${this.PLUGIN_ENGINE_VERSION}`,
          affectedComponents: ['engine'],
          solution: 'Update the engine or use a compatible plugin version',
        });
      }
    } catch (error: unknown) {
      issues.push({
        type: 'engine',
        severity: 'high',
        description: `Failed to check engine compatibility: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        affectedComponents: ['engine'],
        solution: 'Verify engine version format and retry',
      });
    }
  }

  private async checkNodeCompatibilitySafe(
    plugin: Plugin,
    issues: TypeSafeCompatibilityIssue[],
    recommendations: string[]
  ): Promise<void> {
    try {
      const currentNodeVersion = process.version.substring(1); // 移除 'v' 前缀

      // 简化的Node.js版本检查
      const minNodeVersion = '18.0.0';
      if (this.typeSafeVersionManager.compareVersions(currentNodeVersion, minNodeVersion) < 0) {
        issues.push({
          type: 'engine',
          severity: 'high',
          description: `Plugin may require Node.js >= ${minNodeVersion}, but current version is ${currentNodeVersion}`,
          affectedComponents: ['runtime'],
          solution: `Upgrade Node.js to version ${minNodeVersion} or higher`,
        });
      }
    } catch (error: unknown) {
      issues.push({
        type: 'engine',
        severity: 'medium',
        description: `Failed to check Node.js compatibility: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        affectedComponents: ['runtime'],
        solution: 'Verify Node.js version and retry',
      });
    }
  }

  private async checkApiCompatibilitySafe(
    plugin: Plugin,
    context: CompatibilityContext,
    issues: TypeSafeCompatibilityIssue[],
    recommendations: string[]
  ): Promise<void> {
    try {
      // 检查规则API兼容性
      for (const rule of plugin.rules) {
        if (!this.isRuleApiCompatible(rule, context.apiVersion)) {
          issues.push({
            type: 'api',
            severity: 'high',
            description: `Rule '${rule.id}' uses incompatible API`,
            affectedComponents: [rule.id],
            solution: 'Update the rule to use the current API version',
          });
        }
      }
    } catch (error: unknown) {
      issues.push({
        type: 'api',
        severity: 'medium',
        description: `Failed to check API compatibility: ${error instanceof Error ? error.message : 'Unknown error'}`,
        affectedComponents: ['api'],
        solution: 'Review plugin API usage and retry',
      });
    }
  }

  private extractPluginIdSafe(dependency: string): string {
    try {
      const parts = dependency.split('@');
      return parts[0] || dependency;
    } catch (error: unknown) {
      return dependency; // 回退到原始字符串
    }
  }

  private extractVersionRangeSafe(dependency: string): string | null {
    try {
      const parts = dependency.split('@');
      return parts.length > 1 ? parts[1] || null : null;
    } catch (error: unknown) {
      return null;
    }
  }

  private isRuleApiCompatible(rule: any, apiVersion: string): boolean {
    // 简化的API兼容性检查
    // 实际实现应该检查规则使用的API方法和接口
    return true; // 暂时返回true
  }
}

// 辅助接口和类型 - 保持向后兼容

interface VersionParts {
  major: number;
  minor: number;
  patch: number;
  prerelease: string | null;
  build: string | null;
  raw: string;
}

interface CompatibilityContext {
  apiVersion: string;
  availablePlugins: Plugin[];
  currentConfig: any;
}
