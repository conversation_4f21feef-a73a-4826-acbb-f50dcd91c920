// 主要模块导出 - 移除ComplexityCalculator导出
export { SemanticComplexityVisitor } from './core/complexity-visitor-refactored';
export {
  SemanticComplexityVisitorFactory,
  createSemanticComplexityVisitor,
  createSemanticComplexityVisitorWithDetails,
} from './core/semantic-complexity-visitor-factory';
export { ASTParser } from './core/parser';
export { DetailCollector } from './core/detail-collector';
export { RuleRegistry } from './core/rule-registry';
export { ConfigManager } from './config/manager';
export { BaselineManager } from './baseline/manager';
export * from './core/types';
export * from './config/types';
export * from './baseline/types';
export * from './core/errors';

// 工厂模块导出 - 暂时移除CalculatorFactory导出
// TODO: 将来可能需要基于ComplexityVisitor的工厂模式
// export {
//   CalculatorFactory,
//   createCalculatorFactory,
//   createLightweightFactory,
//   createFullFeaturedFactory
// } from './core/calculator-factory';

// 规则相关导出
export {
  initializeRules,
  getDefaultRulesConfig,
  getRulesByCategory,
  isKnownRule,
  getDefaultIncrement,
  getNodeTypeRuleId,
  getLogicalOperatorRuleId,
} from './core/default-rules';

// 规则初始化系统导出
export { RuleInitializationManager, createRuleManager, resetDefaultManager } from './core/rule-initialization';

import type { CalculationOptions, FileResult, AnalysisResult } from './core/types';
import type { CognitiveConfig } from './config/types';
import { SemanticComplexityVisitorFactory } from './core/semantic-complexity-visitor-factory';
import { ASTParser } from './core/parser';
import { DetailCollector } from './core/detail-collector';
import * as fs from 'fs/promises';

// 主要函数导出
export async function analyzeCode(
  sourceCode: string,
  filename: string = 'inline.ts',
  options?: CalculationOptions
): Promise<FileResult> {
  try {
    // 解析AST
    const parser = new ASTParser();
    const ast = await parser.parseCode(sourceCode, filename);

    // 创建详细信息收集器
    const detailCollector = options?.enableDetails ? new DetailCollector() : undefined;

    // 创建语义复杂度访问者
    const visitor = SemanticComplexityVisitorFactory.createComplete(sourceCode, detailCollector, options);

    // 从根节点开始完整遍历AST，确保父节点栈正确维护
    visitor.visit(ast);

    // 获取结果
    const functionResults = visitor.getFunctionResults();
    const results = Array.from(functionResults.values());

    // 设置文件路径
    results.forEach((result) => {
      result.filePath = filename;
    });

    const totalComplexity = results.reduce((sum, fn) => sum + fn.complexity, 0);
    const averageComplexity = results.length > 0 ? totalComplexity / results.length : 0;

    return {
      filePath: filename,
      complexity: totalComplexity,
      functions: results,
      averageComplexity,
    };
  } catch (error) {
    // 错误处理：返回空结果但保留错误信息
    console.warn(`Failed to analyze code for ${filename}:`, error);
    return {
      filePath: filename,
      complexity: 0,
      functions: [],
      averageComplexity: 0,
    };
  }
}

export async function analyzeFile(filePath: string, options?: CalculationOptions): Promise<FileResult> {
  try {
    // 读取文件内容
    const fileContent = await fs.readFile(filePath, 'utf-8');

    // 解析AST
    const parser = new ASTParser();
    const ast = await parser.parseCode(fileContent, filePath);

    // 创建详细信息收集器
    const detailCollector = options?.enableDetails ? new DetailCollector() : undefined;

    // 创建语义复杂度访问者
    const visitor = SemanticComplexityVisitorFactory.createComplete(fileContent, detailCollector, options);

    // 从根节点开始完整遍历AST，确保父节点栈正确维护
    visitor.visit(ast);

    // 获取结果
    const functionResults = visitor.getFunctionResults();
    const results = Array.from(functionResults.values());

    // 设置文件路径
    results.forEach((result) => {
      result.filePath = filePath;
    });

    const totalComplexity = results.reduce((sum, fn) => sum + fn.complexity, 0);
    const averageComplexity = results.length > 0 ? totalComplexity / results.length : 0;

    return {
      filePath,
      complexity: totalComplexity,
      functions: results,
      averageComplexity,
    };
  } catch (error) {
    // 错误处理：返回空结果但保留错误信息
    console.warn(`Failed to analyze file ${filePath}:`, error);
    return {
      filePath,
      complexity: 0,
      functions: [],
      averageComplexity: 0,
    };
  }
}

export async function analyzeProject(paths: string[], config?: CognitiveConfig): Promise<AnalysisResult> {
  // 使用静态方法进行批量文件分析，自动管理资源
  // 未来可以使用 ComplexityCalculator.analyzeFiles(paths, config?.calculationOptions);

  throw new Error('Not implemented yet');
}
