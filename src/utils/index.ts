// 重新导出 position-converter 模块
export * from './position-converter';
export * from './code-frame-generator';
export * from './error-recovery-service';
export * from './compatibility-service';
export * from './file-cache';
export * from './parallel-code-frame-generator';
export * from './file-complexity-filter';

// 新增路径处理模块
export * from './path-processor';
export * from './path-validator';

// Type safety utilities
export * from './type-guards';
export * from './safe-operations';

// Span 调试信息工具函数
export * from './span-debug-info';