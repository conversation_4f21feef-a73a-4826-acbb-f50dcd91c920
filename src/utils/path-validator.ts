import { promises as fs } from 'fs';
import { constants } from 'fs';
import { resolve, extname } from 'path';
import { normalizeToAbsolute, getDisplayPath } from './path-processor';

/**
 * 路径验证和错误处理模块
 * 提供路径存在性检查、权限验证和友好错误信息
 */

/**
 * 路径类型枚举
 */
export enum PathType {
  FILE = 'file',
  DIRECTORY = 'directory',
  NONEXISTENT = 'nonexistent',
  INACCESSIBLE = 'inaccessible'
}

/**
 * 路径验证结果接口
 */
export interface PathValidationResult {
  valid: boolean;
  type: PathType;
  exists: boolean;
  accessible: boolean;
  error?: string;
  suggestion?: string;
}

/**
 * 检查路径是否存在
 * @param filePath 要检查的路径
 * @returns 如果路径存在则返回true
 */
export async function pathExists(filePath: string): Promise<boolean> {
  try {
    await fs.access(filePath);
    return true;
  } catch {
    return false;
  }
}

/**
 * 检查路径访问权限
 * @param filePath 要检查的路径
 * @param mode 访问模式，默认为可读
 * @returns 如果有访问权限则返回true
 */
export async function checkPathAccess(filePath: string, mode: number = constants.R_OK): Promise<boolean> {
  try {
    await fs.access(filePath, mode);
    return true;
  } catch {
    return false;
  }
}

/**
 * 获取路径类型
 * @param filePath 要检查的路径
 * @returns 路径类型
 */
export async function getPathType(filePath: string): Promise<PathType> {
  try {
    const stats = await fs.stat(filePath);
    
    if (stats.isFile()) {
      return PathType.FILE;
    } else if (stats.isDirectory()) {
      return PathType.DIRECTORY;
    } else {
      return PathType.INACCESSIBLE;
    }
  } catch (error) {
    // 检查是权限问题还是不存在
    const exists = await pathExists(filePath);
    if (exists) {
      return PathType.INACCESSIBLE;
    } else {
      return PathType.NONEXISTENT;
    }
  }
}

/**
 * 验证路径的存在性和可访问性
 * @param originalPath 用户输入的原始路径
 * @param basePath 基准路径，默认为当前工作目录
 * @returns 详细的验证结果
 */
export async function validatePathExists(originalPath: string, basePath: string = process.cwd()): Promise<PathValidationResult> {
  const absolutePath = normalizeToAbsolute(originalPath, basePath);
  const displayPath = getDisplayPath(originalPath, absolutePath);
  
  const exists = await pathExists(absolutePath);
  const accessible = exists ? await checkPathAccess(absolutePath) : false;
  const type = await getPathType(absolutePath);
  
  const result: PathValidationResult = {
    valid: exists && accessible,
    type,
    exists,
    accessible,
  };

  // 生成错误信息和建议
  if (!exists) {
    result.error = `路径不存在: "${displayPath}"`;
    result.suggestion = '请检查路径是否正确，或使用绝对路径';
  } else if (!accessible) {
    result.error = `无法访问路径: "${displayPath}"`;
    result.suggestion = '请检查文件权限或运行权限';
  } else if (type === PathType.INACCESSIBLE) {
    result.error = `路径类型无法识别: "${displayPath}"`;
    result.suggestion = '路径可能是特殊文件或符号链接';
  }

  return result;
}

/**
 * 验证文件是否为支持的类型
 * @param filePath 文件路径
 * @returns 如果是支持的文件类型则返回true
 */
export function isSupportedFileType(filePath: string): boolean {
  const supportedExtensions = ['.ts', '.tsx', '.js', '.jsx'];
  const ext = extname(filePath).toLowerCase();
  return supportedExtensions.includes(ext);
}

/**
 * 批量验证路径
 * @param paths 路径数组
 * @param basePath 基准路径
 * @returns 验证结果数组
 */
export async function validateMultiplePaths(paths: string[], basePath: string = process.cwd()): Promise<PathValidationResult[]> {
  const validationPromises = paths.map(path => validatePathExists(path, basePath));
  return Promise.all(validationPromises);
}

/**
 * 生成友好的错误信息
 * @param originalPath 用户输入的原始路径
 * @param error 错误对象或错误信息
 * @returns 友好的错误信息
 */
export function generateFriendlyErrorMessage(originalPath: string, error: Error | string): string {
  const errorMessage = error instanceof Error ? error.message : error;
  const displayPath = originalPath;

  // 常见错误类型的友好提示
  if (errorMessage.includes('ENOENT')) {
    return `文件或目录不存在: "${displayPath}"\n建议：请检查路径是否正确，或使用Tab键自动补全`;
  }
  
  if (errorMessage.includes('EACCES') || errorMessage.includes('EPERM')) {
    return `权限不足，无法访问: "${displayPath}"\n建议：请检查文件权限或使用管理员权限运行`;
  }
  
  if (errorMessage.includes('EISDIR')) {
    return `期望文件但得到目录: "${displayPath}"\n建议：如果要分析目录中的文件，请使用目录路径或glob模式`;
  }
  
  if (errorMessage.includes('ENOTDIR')) {
    return `期望目录但得到文件: "${displayPath}"\n建议：请检查路径是否正确`;
  }

  if (errorMessage.includes('文件路径必须是绝对路径')) {
    return `路径处理错误: "${displayPath}"\n这是一个内部错误，路径处理系统应该自动处理相对路径转换`;
  }

  // 默认错误信息
  return `处理路径时发生错误: "${displayPath}"\n错误详情: ${errorMessage}`;
}

/**
 * 创建路径相关的错误类
 */
export class PathValidationError extends Error {
  public readonly originalPath: string;
  public readonly validationResult: PathValidationResult;

  constructor(originalPath: string, validationResult: PathValidationResult, message?: string) {
    const errorMessage = message || validationResult.error || `路径验证失败: ${originalPath}`;
    super(errorMessage);
    
    this.name = 'PathValidationError';
    this.originalPath = originalPath;
    this.validationResult = validationResult;
  }
}

/**
 * 检查是否为空或无效的路径数组
 * @param paths 路径数组
 * @returns 如果路径数组为空或无效则返回true
 */
export function isEmptyPathArray(paths: string[]): boolean {
  return !paths || paths.length === 0 || paths.every(path => !path || path.trim() === '');
}

/**
 * 过滤和清理路径数组
 * @param paths 原始路径数组
 * @returns 清理后的有效路径数组
 */
export function cleanPathArray(paths: string[]): string[] {
  if (!paths || paths.length === 0) {
    return [];
  }

  return paths
    .filter(path => path && typeof path === 'string' && path.trim() !== '')
    .map(path => path.trim());
}

/**
 * 为路径验证错误提供上下文信息
 * @param paths 失败的路径数组
 * @param validationResults 验证结果数组
 * @returns 上下文错误信息
 */
export function generatePathValidationSummary(paths: string[], validationResults: PathValidationResult[]): string {
  const total = paths.length;
  const failed = validationResults.filter(result => !result.valid).length;
  const successful = total - failed;

  if (failed === 0) {
    return `所有 ${total} 个路径验证成功`;
  }

  if (successful === 0) {
    return `所有 ${total} 个路径验证失败`;
  }

  return `${total} 个路径中，${successful} 个成功，${failed} 个失败`;
}