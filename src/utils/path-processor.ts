import { resolve, normalize, isAbsolute, sep, posix } from 'path';
import { promises as fs } from 'fs';

/**
 * 路径处理核心工具模块
 * 提供跨平台的路径解析、验证和规范化功能
 */

/**
 * 将相对路径解析为绝对路径
 * @param inputPath 输入路径（可能是相对路径或绝对路径）
 * @param basePath 基准路径，默认为当前工作目录
 * @returns 解析后的绝对路径
 */
export function resolveRelativePath(inputPath: string, basePath: string = process.cwd()): string {
  if (isAbsolute(inputPath)) {
    return normalize(inputPath);
  }
  return resolve(basePath, inputPath);
}

/**
 * 将路径统一转换为绝对路径并规范化
 * @param inputPath 输入路径
 * @param basePath 基准路径，默认为当前工作目录
 * @returns 规范化的绝对路径
 */
export function normalizeToAbsolute(inputPath: string, basePath: string = process.cwd()): string {
  const resolved = resolveRelativePath(inputPath, basePath);
  return normalize(resolved);
}

/**
 * 检查字符串是否包含基本的通配符模式
 * @param pattern 要检查的模式字符串
 * @returns 如果包含通配符则返回true
 */
export function containsWildcards(pattern: string): boolean {
  return /[*?[\]{}]/.test(pattern);
}

/**
 * 检查是否为有效的glob模式
 * 扩展现有的isGlobPattern功能，提供更详细的验证
 * @param pattern 要验证的模式字符串
 * @returns 如果是有效的glob模式则返回true
 */
export function isValidGlobPattern(pattern: string): boolean {
  // 基本的glob字符检查
  if (!containsWildcards(pattern)) {
    return false;
  }

  try {
    // 检查是否包含无效的字符组合
    // 避免一些明显错误的模式
    if (pattern.includes('***') || pattern.includes('??*?')) {
      return false;
    }

    // 检查括号匹配
    const braceCount = (pattern.match(/\{/g) || []).length - (pattern.match(/\}/g) || []).length;
    const bracketCount = (pattern.match(/\[/g) || []).length - (pattern.match(/\]/g) || []).length;
    
    if (braceCount !== 0 || bracketCount !== 0) {
      return false;
    }

    return true;
  } catch {
    return false;
  }
}

/**
 * 清理和规范化路径字符串
 * 处理特殊字符、多余的分隔符等
 * @param inputPath 输入路径
 * @returns 清理后的路径
 */
export function sanitizePath(inputPath: string): string {
  if (!inputPath || typeof inputPath !== 'string') {
    throw new Error('路径必须是非空字符串');
  }

  // 移除多余的空格
  let cleaned = inputPath.trim();
  
  // 移除多余的路径分隔符
  cleaned = cleaned.replace(/[/\\]+/g, sep);
  
  // 移除末尾的路径分隔符（除非是根路径）
  if (cleaned.length > 1 && cleaned.endsWith(sep)) {
    cleaned = cleaned.slice(0, -1);
  }

  return cleaned;
}

/**
 * 跨平台路径处理
 * 统一不同操作系统的路径格式
 * @param inputPath 输入路径
 * @returns 跨平台兼容的路径
 */
export function crossPlatformPath(inputPath: string): string {
  const sanitized = sanitizePath(inputPath);
  
  // 在Windows上处理UNC路径和驱动器字母
  if (process.platform === 'win32') {
    // 规范化Windows路径分隔符
    return sanitized.replace(/\//g, '\\');
  } else {
    // Unix-like系统，统一使用正斜杠
    return sanitized.replace(/\\/g, '/');
  }
}

/**
 * 将简单通配符模式转换为glob模式
 * 支持常见的通配符模式如 *.tsx, test-*.js 等
 * @param wildcardPattern 通配符模式
 * @returns 转换后的glob模式
 */
export function expandWildcards(wildcardPattern: string): string {
  if (!containsWildcards(wildcardPattern)) {
    return wildcardPattern;
  }

  // 已经是复杂glob模式，直接返回
  if (wildcardPattern.includes('**') || wildcardPattern.includes('{') || wildcardPattern.includes('[')) {
    return wildcardPattern;
  }

  // 转换简单通配符为glob模式
  let globPattern = wildcardPattern;
  
  // 如果只是文件名模式（不包含路径分隔符），添加当前目录前缀
  if (!globPattern.includes('/') && !globPattern.includes('\\')) {
    globPattern = `./${globPattern}`;
  }

  return globPattern;
}

/**
 * 检查路径是否超出系统限制
 * @param inputPath 要检查的路径
 * @returns 如果路径长度合理则返回true
 */
export function isPathLengthValid(inputPath: string): boolean {
  // Windows路径长度限制通常为260字符
  // Unix-like系统通常为4096字符
  // 我们使用保守的限制
  const maxLength = process.platform === 'win32' ? 260 : 1024;
  return inputPath.length <= maxLength;
}

/**
 * 验证路径字符是否合法
 * 允许glob模式中的通配符
 * @param inputPath 要验证的路径
 * @returns 如果路径字符合法则返回true
 */
export function hasValidPathCharacters(inputPath: string): boolean {
  // 如果是glob模式，允许通配符
  if (containsWildcards(inputPath)) {
    // Windows禁用字符（排除glob通配符）: < > : " | 以及控制字符
    // 允许的glob字符: * ? [ ] { }
    const windowsInvalidCharsForGlob = /[<>:"|^\x00-\x1f]/;
    
    // Unix-like系统主要禁用空字符
    const unixInvalidChars = /\x00/;
    
    if (process.platform === 'win32') {
      return !windowsInvalidCharsForGlob.test(inputPath);
    } else {
      return !unixInvalidChars.test(inputPath);
    }
  }
  
  // 非glob模式使用严格验证
  // Windows禁用字符: < > : " | ? * 以及控制字符
  const windowsInvalidChars = /[<>:"|?*\x00-\x1f]/;
  
  // Unix-like系统主要禁用空字符
  const unixInvalidChars = /\x00/;
  
  if (process.platform === 'win32') {
    return !windowsInvalidChars.test(inputPath);
  } else {
    return !unixInvalidChars.test(inputPath);
  }
}

/**
 * 综合路径验证函数
 * @param inputPath 要验证的路径
 * @returns 验证结果，包含是否有效和错误信息
 */
export function validatePathFormat(inputPath: string): { valid: boolean; error?: string } {
  if (!inputPath || typeof inputPath !== 'string') {
    return { valid: false, error: '路径必须是非空字符串' };
  }

  if (!isPathLengthValid(inputPath)) {
    return { valid: false, error: '路径长度超出系统限制' };
  }

  if (!hasValidPathCharacters(inputPath)) {
    return { valid: false, error: '路径包含非法字符' };
  }

  return { valid: true };
}

/**
 * 获取路径的显示名称（用于错误信息等）
 * 确保在错误信息中显示用户输入的原始路径
 * @param originalPath 用户输入的原始路径
 * @param resolvedPath 解析后的绝对路径
 * @returns 用于显示的路径名称
 */
export function getDisplayPath(originalPath: string, resolvedPath: string): string {
  // 如果原始路径是相对路径，优先显示原始路径
  if (!isAbsolute(originalPath)) {
    return originalPath;
  }
  
  // 否则显示解析后的路径
  return resolvedPath;
}