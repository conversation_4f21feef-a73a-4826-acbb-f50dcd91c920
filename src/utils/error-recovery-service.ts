import { promises as fs } from 'fs';
import { existsSync, statSync } from 'fs';
import { dirname, isAbsolute, resolve } from 'path';
import { FileReadError, CodeFrameError } from '../core/errors';
import { normalizeToAbsolute, getDisplayPath } from './path-processor';

export interface ErrorRecoveryOptions {
  maxRetries?: number;
  retryDelay?: number;
  fallbackEncoding?: string[];
  enableCaching?: boolean;
  logErrors?: boolean;
}

export interface RecoveryAttempt {
  attempt: number;
  strategy: string;
  success: boolean;
  error?: Error;
  duration: number;
}

export interface ErrorRecoveryResult<T> {
  success: boolean;
  result?: T;
  error?: Error;
  attempts: RecoveryAttempt[];
  strategy: string;
  totalDuration: number;
}

/**
 * 错误恢复服务
 * 提供文件读取失败的恢复机制和错误处理
 */
export class ErrorRecoveryService {
  private cache = new Map<string, string>();
  private failureCache = new Map<string, { timestamp: number; count: number }>();
  private readonly options: Required<ErrorRecoveryOptions>;

  constructor(options: ErrorRecoveryOptions = {}) {
    this.options = {
      maxRetries: options.maxRetries ?? 3,
      retryDelay: options.retryDelay ?? 1000,
      fallbackEncoding: options.fallbackEncoding ?? ['utf-8', 'utf8', 'latin1'],
      enableCaching: options.enableCaching ?? true,
      logErrors: options.logErrors ?? true
    };
  }

  /**
   * 安全读取文件内容，带有错误恢复机制
   */
  async readFileWithRecovery(filePath: string): Promise<ErrorRecoveryResult<string>> {
    const startTime = performance.now();
    const attempts: RecoveryAttempt[] = [];
    let lastError: Error | undefined;

    // 统一转换为绝对路径用于内部处理
    const absolutePath = normalizeToAbsolute(filePath);
    const displayPath = getDisplayPath(filePath, absolutePath);

    // 检查文件基本可访问性
    try {
      await this.validateFileAccess(filePath); // 这里传入原始路径，让validateFileAccess处理转换
    } catch (error) {
      return this.createFailureResult(
        startTime,
        attempts,
        'validation',
        error as Error
      );
    }

    // 尝试从缓存获取（使用绝对路径作为缓存键）
    if (this.options.enableCaching && this.cache.has(absolutePath)) {
      return this.createSuccessResult(
        startTime,
        attempts,
        'cache',
        this.cache.get(absolutePath)!
      );
    }

    // 多重编码尝试策略
    for (const encoding of this.options.fallbackEncoding) {
      for (let attempt = 1; attempt <= this.options.maxRetries; attempt++) {
        const attemptStart = performance.now();
        
        try {
          const content = await fs.readFile(absolutePath, encoding as BufferEncoding);
          const attemptDuration = performance.now() - attemptStart;
          
          attempts.push({
            attempt,
            strategy: `read-${encoding}`,
            success: true,
            duration: attemptDuration
          });

          // 缓存成功结果（使用绝对路径作为键）
          if (this.options.enableCaching) {
            this.cache.set(absolutePath, content);
          }

          // 清除失败记录（使用绝对路径）
          this.failureCache.delete(absolutePath);

          return this.createSuccessResult(
            startTime,
            attempts,
            `read-${encoding}`,
            content
          );

        } catch (error) {
          const attemptDuration = performance.now() - attemptStart;
          lastError = error as Error;
          
          attempts.push({
            attempt,
            strategy: `read-${encoding}`,
            success: false,
            error: lastError,
            duration: attemptDuration
          });

          this.logError(`文件读取失败 (尝试 ${attempt}/${this.options.maxRetries}, 编码: ${encoding}): ${filePath}`, lastError);

          // 如果不是最后一次尝试，等待后重试
          if (attempt < this.options.maxRetries) {
            await this.delay(this.options.retryDelay);
          }
        }
      }
    }

    // 记录失败次数（使用绝对路径）
    this.recordFailure(absolutePath);

    // 尝试降级处理（需要传递绝对路径）
    const fallbackResult = await this.attemptFallbackReading(absolutePath, attempts, startTime);
    if (fallbackResult.success) {
      return fallbackResult;
    }

    return this.createFailureResult(
      startTime,
      attempts,
      'all-strategies-failed',
      lastError || new Error('所有读取策略都失败')
    );
  }

  /**
   * 生成代码框架的错误恢复
   */
  async generateCodeFrameWithRecovery(
    filePath: string,
    line: number,
    column: number,
    generateFrame: (content: string) => string | Promise<string>
  ): Promise<ErrorRecoveryResult<string>> {
    const startTime = performance.now();
    const attempts: RecoveryAttempt[] = [];

    try {
      // 首先尝试读取文件
      const fileResult = await this.readFileWithRecovery(filePath);
      attempts.push(...fileResult.attempts);

      if (!fileResult.success || !fileResult.result) {
        return this.createFailureResult(
          startTime,
          attempts,
          'file-read-failed',
          fileResult.error || new Error('文件读取失败')
        );
      }

      // 尝试生成代码框架
      const attemptStart = performance.now();
      try {
        const frame = await generateFrame(fileResult.result);
        const attemptDuration = performance.now() - attemptStart;
        
        attempts.push({
          attempt: 1,
          strategy: 'generate-frame',
          success: true,
          duration: attemptDuration
        });

        return this.createSuccessResult(
          startTime,
          attempts,
          'generate-frame',
          frame
        );

      } catch (error) {
        const attemptDuration = performance.now() - attemptStart;
        attempts.push({
          attempt: 1,
          strategy: 'generate-frame',
          success: false,
          error: error as Error,
          duration: attemptDuration
        });

        // 生成降级代码框架
        const fallbackFrame = this.generateFallbackFrame(filePath, line, column);
        
        attempts.push({
          attempt: 1,
          strategy: 'fallback-frame',
          success: true,
          duration: 0
        });

        return this.createSuccessResult(
          startTime,
          attempts,
          'fallback-frame',
          fallbackFrame
        );
      }

    } catch (error) {
      return this.createFailureResult(
        startTime,
        attempts,
        'unexpected-error',
        error as Error
      );
    }
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cache.clear();
    this.failureCache.clear();
  }

  /**
   * 获取错误统计信息
   */
  getErrorStats(): {
    cacheSize: number;
    failureCount: number;
    failedFiles: string[];
  } {
    return {
      cacheSize: this.cache.size,
      failureCount: this.failureCache.size,
      failedFiles: Array.from(this.failureCache.keys())
    };
  }

  // 私有方法

  private async validateFileAccess(filePath: string): Promise<void> {
    // 将相对路径自动转换为绝对路径，而不是抛出错误
    const absolutePath = normalizeToAbsolute(filePath);
    const displayPath = getDisplayPath(filePath, absolutePath);

    // 检查文件是否存在
    if (!existsSync(absolutePath)) {
      throw new FileReadError(displayPath, new Error('文件不存在'));
    }

    try {
      const stats = statSync(absolutePath);
      
      // 检查是否为文件
      if (!stats.isFile()) {
        throw new FileReadError(displayPath, new Error('路径不是一个文件'));
      }

      // 检查文件大小（避免读取过大文件）
      const maxFileSize = 10 * 1024 * 1024; // 10MB
      if (stats.size > maxFileSize) {
        throw new FileReadError(displayPath, new Error(`文件过大 (${Math.round(stats.size / 1024 / 1024)}MB > 10MB)`));
      }

    } catch (error) {
      if (error instanceof FileReadError) {
        throw error;
      }
      throw new FileReadError(displayPath, error as Error);
    }
  }

  private async attemptFallbackReading(
    filePath: string,
    attempts: RecoveryAttempt[],
    startTime: number
  ): Promise<ErrorRecoveryResult<string>> {
    const attemptStart = performance.now();
    
    try {
      // 尝试以二进制方式读取，然后转换
      const buffer = await fs.readFile(filePath);
      const content = buffer.toString('utf-8', 0, Math.min(buffer.length, 1024 * 1024)); // 限制为1MB
      
      const attemptDuration = performance.now() - attemptStart;
      attempts.push({
        attempt: 1,
        strategy: 'binary-fallback',
        success: true,
        duration: attemptDuration
      });

      if (this.options.enableCaching) {
        this.cache.set(filePath, content);
      }

      return this.createSuccessResult(
        startTime,
        attempts,
        'binary-fallback',
        content
      );

    } catch (error) {
      const attemptDuration = performance.now() - attemptStart;
      attempts.push({
        attempt: 1,
        strategy: 'binary-fallback',
        success: false,
        error: error as Error,
        duration: attemptDuration
      });

      return this.createFailureResult(
        startTime,
        attempts,
        'binary-fallback-failed',
        error as Error
      );
    }
  }

  private generateFallbackFrame(filePath: string, line: number, column: number): string {
    const relativePath = filePath.replace(process.cwd(), '.');
    return [
      `  ${line} | [无法生成代码框架]`,
      `      | 文件: ${relativePath}`,
      `      | 位置: 第 ${line} 行，第 ${column} 列`,
      `      | 提示: 文件可能包含特殊字符或编码问题`
    ].join('\n');
  }

  private recordFailure(filePath: string): void {
    const now = Date.now();
    const existing = this.failureCache.get(filePath);
    
    if (existing) {
      this.failureCache.set(filePath, {
        timestamp: now,
        count: existing.count + 1
      });
    } else {
      this.failureCache.set(filePath, {
        timestamp: now,
        count: 1
      });
    }
  }

  private logError(message: string, error?: Error): void {
    if (this.options.logErrors) {
      console.warn(`[ErrorRecovery] ${message}`);
      if (error) {
        console.warn(`[ErrorRecovery] 错误详情: ${error.message}`);
      }
    }
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private createSuccessResult<T>(
    startTime: number,
    attempts: RecoveryAttempt[],
    strategy: string,
    result: T
  ): ErrorRecoveryResult<T> {
    return {
      success: true,
      result,
      attempts,
      strategy,
      totalDuration: performance.now() - startTime
    };
  }

  private createFailureResult<T>(
    startTime: number,
    attempts: RecoveryAttempt[],
    strategy: string,
    error: Error
  ): ErrorRecoveryResult<T> {
    return {
      success: false,
      error,
      attempts,
      strategy,
      totalDuration: performance.now() - startTime
    };
  }
}

// 单例实例
let instance: ErrorRecoveryService | null = null;

export function getErrorRecoveryService(options?: ErrorRecoveryOptions): ErrorRecoveryService {
  if (!instance) {
    instance = new ErrorRecoveryService(options);
  }
  return instance;
}