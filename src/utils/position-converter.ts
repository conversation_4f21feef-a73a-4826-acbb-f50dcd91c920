/**
 * 位置转换工具
 * 专注于SWC解析器字节偏移量与行列位置之间的纯粹转换
 * 支持行映射缓存以优化内存和性能
 *
 * 职责：
 * - 字节偏移量与行列位置的转换
 * - 基于偏移量的高效行内容获取
 * - 智能缓存管理提升大文件处理性能
 * - 智能保底位置生成和错误恢复
 *
 * 不包含：
 * - span 位置修正逻辑
 * - 代码内容语义分析
 * - 复杂的错误恢复机制
 */

import type { EmergencyPositionContext } from '../core/types';

export interface Position {
  line: number;
  column: number;
}

/**
 * 行映射缓存条目
 */
interface LineMapCacheEntry {
  lineMap: number[];
  timestamp: number;
  accessCount: number;
}

/**
 * 文件级行分析缓存条目
 */
interface LineAnalysisCacheEntry {
  insignificantLines: Set<number>;
  timestamp: number;
  accessCount: number;
}

/**
 * 位置转换器
 * 提供SWC span偏移量与行列位置之间的纯粹转换功能
 * 内置智能缓存系统优化大文件处理性能
 *
 * 简化后的职责：
 * - 仅负责精确的位置转换，不包含修正逻辑
 * - 基于偏移量的高效行内容获取
 * - 智能缓存管理和性能优化
 */
export class PositionConverter {
  private static lineMapCache = new Map<string, LineMapCacheEntry>();
  private static lineAnalysisCache = new Map<string, LineAnalysisCacheEntry>();
  private static maxCacheSize = 100; // 最大缓存条目数
  private static cacheCleanupThreshold = 150; // 清理阈值

  /**
   * 将SWC span的字节偏移量转换为行列位置（高性能版本）
   * 提供直接、快速的位置转换，不包含智能回退或修正逻辑
   * @param sourceCode 源代码内容
   * @param spanStart span的起始偏移量
   * @returns 行列位置 (1-based)
   */
  public static fastSpanToPosition(sourceCode: string, spanStart: number): Position {
    const lineMap = this.getCachedLineMap(sourceCode);

    // 验证span有效性
    if (spanStart < 0 || spanStart > sourceCode.length) {
      return { line: 1, column: 1 };
    }

    // 使用二分查找找到对应的行（优化性能）
    const lineIndex = this.binarySearchLineIndex(lineMap, spanStart);

    // 计算列位置
    const lineStartOffset = lineMap[lineIndex]!;
    const column = spanStart - lineStartOffset + 1; // 1-based

    return {
      line: lineIndex + 1, // 1-based
      column: Math.max(1, column), // 确保至少为1
    };
  }

  /**
   * 将SWC span的字节偏移量转换为行列位置（默认推荐版本）
   * 集成智能回退、SWC偏移修正、语义分析等功能，提供最佳的用户体验
   * @param sourceCode 源代码内容
   * @param spanStart span的起始偏移量
   * @param spanEnd span的结束偏移量（可选）
   * @param filePath 文件路径（可选，用于缓存优化）
   * @param nodeType AST节点类型（用于SWC偏移修正）
   * @returns 智能优化后的行列位置
   */
  public static spanToPosition(
    sourceCode: string,
    spanStart: number,
    spanEnd?: number,
    filePath?: string,
    nodeType?: string
  ): Position {
    // SWC 特定节点类型的位置修正
    const correctedSpanStart = this.correctSWCSpanOffset(sourceCode, spanStart, nodeType);

    // 首先进行基础位置转换（使用高性能版本）
    const basePosition = this.fastSpanToPosition(sourceCode, correctedSpanStart);

    // 如果基础位置已经是有意义的行，直接返回
    if (!this.isInsignificantLine(sourceCode, basePosition.line, filePath)) {
      return basePosition;
    }

    // 智能回退策略 1: 使用 span 范围信息进行优化
    if (spanEnd !== undefined && spanEnd > spanStart) {
      const endPosition = this.fastSpanToPosition(sourceCode, spanEnd);

      // 如果 span 跨越多行，尝试找到范围内最有意义的行
      if (endPosition.line > basePosition.line) {
        const meaningfulLine = this.findMostMeaningfulLineInRange(
          sourceCode,
          basePosition.line,
          endPosition.line,
          filePath
        );
        if (meaningfulLine !== basePosition.line) {
          return {
            line: meaningfulLine,
            column: 1, // 对于跨行span，使用行首作为列位置
          };
        }
      }

      // 检查结束位置是否更有意义
      if (!this.isInsignificantLine(sourceCode, endPosition.line, filePath)) {
        return this.findLastMeaningfulLine(sourceCode, endPosition.line, 3, filePath) === endPosition.line
          ? endPosition
          : { line: this.findLastMeaningfulLine(sourceCode, endPosition.line, 3, filePath), column: 1 };
      }
    }

    // 智能回退策略 2: 双向搜索最近的有意义行
    const nearestMeaningfulLine = this.findNearestMeaningfulLine(
      sourceCode,
      basePosition.line,
      5, // 默认搜索范围
      filePath
    );

    if (nearestMeaningfulLine !== basePosition.line) {
      return {
        line: nearestMeaningfulLine,
        column: basePosition.column, // 保持原始列位置，除非跨行
      };
    }

    // 智能回退策略 3: 处理极端边界情况
    if (basePosition.line <= 3) {
      // 文件开头区域，向下搜索
      const lineMap = this.getCachedLineMap(sourceCode);
      for (let line = basePosition.line + 1; line <= Math.min(lineMap.length, basePosition.line + 10); line++) {
        if (!this.isInsignificantLine(sourceCode, line, filePath)) {
          return { line, column: 1 };
        }
      }
    }

    const lineMap = this.getCachedLineMap(sourceCode);
    if (basePosition.line >= lineMap.length - 3) {
      // 文件末尾区域，向上搜索
      const lastMeaningfulLine = this.findLastMeaningfulLine(
        sourceCode,
        basePosition.line,
        15, // 扩大搜索范围
        filePath
      );
      if (lastMeaningfulLine !== basePosition.line) {
        return { line: lastMeaningfulLine, column: 1 };
      }
    }

    // 所有智能策略失败，返回基础位置
    return basePosition;
  }

  /**
   * 修正 SWC 解析器特定节点类型的 span 偏移问题
   * @param sourceCode 源代码内容
   * @param spanStart 原始 span 开始位置
   * @param nodeType AST 节点类型
   * @returns 修正后的 span 开始位置
   */
  private static correctSWCSpanOffset(sourceCode: string, spanStart: number, nodeType?: string): number {
    // 对于 IfStatement，SWC 可能将 span.start 指向 'f' 而不是 'i'
    if (nodeType === 'IfStatement' && spanStart > 0) {
      const charAtSpan = sourceCode[spanStart];
      const charBefore = sourceCode[spanStart - 1];

      // 如果当前字符是 'f' 且前一个字符是 'i'，说明需要修正
      if (charAtSpan === 'f' && charBefore === 'i') {
        return spanStart - 1;
      }
    }

    // 其他节点类型暂时不需要修正
    return spanStart;
  }

  /**
   * 将行列位置转换为字节偏移量
   * @param sourceCode 源代码内容
   * @param line 行号 (1-based)
   * @param column 列号 (1-based)
   * @returns 字节偏移量
   */
  public static lineColumnToOffset(sourceCode: string, line: number, column: number): number {
    const lineMap = this.getCachedLineMap(sourceCode);

    // 转换为0-based索引
    const lineIndex = line - 1;
    const columnIndex = column - 1;

    // 边界检查
    if (lineIndex < 0 || lineIndex >= lineMap.length) {
      throw new Error(`Line ${line} is out of range. File has ${lineMap.length} lines.`);
    }

    const lineStartOffset = lineMap[lineIndex]!;

    // 获取行的内容长度（不包括换行符）
    const lineEndOffset =
      lineIndex + 1 < lineMap.length
        ? lineMap[lineIndex + 1]! - 1 // 减去换行符
        : sourceCode.length;

    const lineLength = lineEndOffset - lineStartOffset;

    // 列边界检查
    if (columnIndex < 0 || columnIndex > lineLength) {
      throw new Error(`Column ${column} is out of range. Line ${line} has ${lineLength} characters.`);
    }

    return lineStartOffset + columnIndex;
  }

  /**
   * 获取指定行的内容
   * @param sourceCode 源代码内容
   * @param line 行号 (1-based)
   * @returns 行内容（不包括换行符）
   */
  public static getLineContent(sourceCode: string, line: number): string {
    const lineMap = this.getCachedLineMap(sourceCode);
    const lineIndex = line - 1; // 转换为0-based

    if (lineIndex < 0 || lineIndex >= lineMap.length) {
      throw new Error(`Line ${line} is out of range. File has ${lineMap.length} lines.`);
    }

    const lineStartOffset = lineMap[lineIndex]!;
    const lineEndOffset =
      lineIndex + 1 < lineMap.length
        ? lineMap[lineIndex + 1]! - 1 // 减去换行符
        : sourceCode.length;

    return sourceCode.slice(lineStartOffset, lineEndOffset);
  }

  /**
   * 获取span覆盖的代码片段
   * @param sourceCode 源代码内容
   * @param span SWC span信息
   * @returns 代码片段
   */
  public static extractSpanText(sourceCode: string, span: { start: number; end: number }): string {
    if (span.start < 0 || span.end > sourceCode.length || span.start > span.end) {
      throw new Error(`Invalid span: start=${span.start}, end=${span.end}, sourceLength=${sourceCode.length}`);
    }

    return sourceCode.slice(span.start, span.end);
  }

  /**
   * 验证位置是否有效
   * @param sourceCode 源代码内容
   * @param position 位置信息
   * @returns 是否有效
   */
  public static isValidPosition(sourceCode: string, position: Position): boolean {
    try {
      this.lineColumnToOffset(sourceCode, position.line, position.column);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 获取缓存的行映射，支持智能缓存管理
   * @param sourceCode 源代码内容
   * @param filePath 文件路径（可选，用于更精确的缓存）
   * @returns 行映射数组
   */
  private static getCachedLineMap(sourceCode: string, filePath?: string): number[] {
    // 生成内容哈希作为缓存键（对于大文件使用采样哈希以提高性能）
    const cacheKey = this.generateCacheKey(sourceCode, filePath);

    // 检查缓存
    const cached = this.lineMapCache.get(cacheKey);
    if (cached) {
      cached.accessCount++;
      cached.timestamp = Date.now();
      return cached.lineMap;
    }

    // 缓存未命中，构建行映射
    const lineMap = this.buildLineMap(sourceCode);

    // 缓存管理
    this.manageCacheSize();

    // 添加到缓存
    this.lineMapCache.set(cacheKey, {
      lineMap,
      timestamp: Date.now(),
      accessCount: 1,
    });

    return lineMap;
  }

  /**
   * 生成源代码的缓存键
   * 对于大文件使用采样策略以提高性能，支持文件路径参数
   * @param sourceCode 源代码内容
   * @param filePath 文件路径（可选，用于更精确的缓存键）
   */
  private static generateCacheKey(sourceCode: string, filePath?: string): string {
    let contentKey: string;

    if (sourceCode.length <= 10000) {
      // 小文件直接使用内容长度和前后100字符
      const start = sourceCode.substring(0, 100);
      const end = sourceCode.substring(Math.max(0, sourceCode.length - 100));
      contentKey = `${sourceCode.length}-${start}-${end}`;
    } else {
      // 大文件使用采样策略：长度 + 开头 + 中间 + 结尾
      const middle = sourceCode.substring(
        Math.floor(sourceCode.length / 2) - 50,
        Math.floor(sourceCode.length / 2) + 50
      );
      const start = sourceCode.substring(0, 100);
      const end = sourceCode.substring(Math.max(0, sourceCode.length - 100));
      contentKey = `${sourceCode.length}-${start}-${middle}-${end}`;
    }

    // 如果提供了文件路径，将其集成到缓存键中
    return filePath ? `${filePath}:${contentKey}` : contentKey;
  }

  /**
   * 管理缓存大小，防止内存泄漏
   */
  private static manageCacheSize(): void {
    if (this.lineMapCache.size <= this.maxCacheSize) {
      return;
    }

    // 当缓存超过清理阈值时，执行清理
    if (this.lineMapCache.size >= this.cacheCleanupThreshold) {
      this.performCacheCleanup();
    }
  }

  /**
   * 执行缓存清理，移除最少使用的条目
   */
  private static performCacheCleanup(): void {
    const entries = Array.from(this.lineMapCache.entries());

    // 按访问频率和时间排序（LRU + LFU混合策略）
    entries.sort((a, b) => {
      const scoreA = a[1].accessCount * 0.7 + (Date.now() - a[1].timestamp) * -0.3;
      const scoreB = b[1].accessCount * 0.7 + (Date.now() - b[1].timestamp) * -0.3;
      return scoreA - scoreB;
    });

    // 移除最低分的条目，保留75%的缓存
    const removeCount = Math.floor(this.lineMapCache.size * 0.25);
    const toRemove = entries.slice(0, removeCount);

    for (const [key] of toRemove) {
      this.lineMapCache.delete(key);
    }
  }

  /**
   * 使用二分查找定位行索引（优化大文件性能）
   */
  private static binarySearchLineIndex(lineMap: number[], offset: number): number {
    let left = 0;
    let right = lineMap.length - 1;

    while (left <= right) {
      const mid = Math.floor((left + right) / 2);
      const midOffset = lineMap[mid]!;
      const nextOffset = lineMap[mid + 1];

      if (offset >= midOffset && (nextOffset === undefined || offset < nextOffset)) {
        return mid;
      } else if (offset < midOffset) {
        right = mid - 1;
      } else {
        left = mid + 1;
      }
    }

    // 后备方案：线性搜索
    for (let i = 0; i < lineMap.length - 1; i++) {
      if (offset >= lineMap[i]! && offset < lineMap[i + 1]!) {
        return i;
      }
    }

    return Math.max(0, lineMap.length - 1);
  }

  /**
   * 构建行映射表
   * 创建一个数组，其中每个元素是对应行的起始字节偏移量
   * @param sourceCode 源代码内容
   * @returns 行起始偏移量数组
   */
  private static buildLineMap(sourceCode: string): number[] {
    const lineMap: number[] = [];
    lineMap.push(0); // 第一行从偏移量0开始

    // 优化：对于大文件使用更高效的搜索方式
    if (sourceCode.length > 50000) {
      // 大文件：分块处理以减少内存压力
      const chunkSize = 10000;
      for (let start = 0; start < sourceCode.length; start += chunkSize) {
        const end = Math.min(start + chunkSize, sourceCode.length);
        const chunk = sourceCode.slice(start, end);

        for (let i = 0; i < chunk.length; i++) {
          if (chunk[i] === '\n') {
            lineMap.push(start + i + 1);
          }
        }
      }
    } else {
      // 小文件：直接处理
      for (let i = 0; i < sourceCode.length; i++) {
        if (sourceCode[i] === '\n') {
          lineMap.push(i + 1); // 下一行从换行符后开始
        }
      }
    }

    return lineMap;
  }

  /**
   * 清除缓存（用于测试和内存管理）
   */
  public static clearCache(): void {
    this.lineMapCache.clear();
    this.lineAnalysisCache.clear();
  }

  /**
   * 获取缓存统计信息
   */
  public static getCacheStats(): {
    size: number;
    maxSize: number;
    hitRate: number;
    memoryEstimate: number;
    lineAnalysisCacheSize: number;
  } {
    let totalAccess = 0;
    let memoryEstimate = 0;

    const entries = Array.from(this.lineMapCache.values());
    for (const entry of entries) {
      totalAccess += entry.accessCount;
      memoryEstimate += entry.lineMap.length * 8; // 估算字节数
    }

    // 计算行分析缓存内存占用
    const analysisEntries = Array.from(this.lineAnalysisCache.values());
    for (const entry of analysisEntries) {
      memoryEstimate += entry.insignificantLines.size * 8; // 估算Set内存占用
    }

    return {
      size: this.lineMapCache.size,
      maxSize: this.maxCacheSize,
      hitRate: totalAccess > 0 ? (totalAccess - this.lineMapCache.size) / totalAccess : 0,
      memoryEstimate,
      lineAnalysisCacheSize: this.lineAnalysisCache.size,
    };
  }

  // =================== L0 层基础语义分析方法 ===================

  /**
   * 判断指定行是否为语义上不重要的行
   * 支持现代 JavaScript/TypeScript 语法的语义分析
   * @param sourceCode 源代码内容
   * @param line 行号 (1-based)
   * @param filePath 文件路径（可选，用于缓存键生成）
   * @returns 是否为不重要的行
   */
  public static isInsignificantLine(sourceCode: string, line: number, filePath?: string): boolean {
    try {
      // 如果提供了文件路径，优先使用缓存
      let insignificantLines: Set<number> | null = null;
      if (filePath) {
        insignificantLines = this.getCachedLineAnalysis(sourceCode, filePath);
        if (insignificantLines.has(line)) {
          return true;
        }
        if (insignificantLines.size > 0) {
          // 如果缓存中有数据但该行不在其中，说明该行是有意义的
          // 但我们还需要检查该行是否存在
          const lineMap = this.getCachedLineMap(sourceCode, filePath);
          if (line <= lineMap.length) {
            return false; // 行存在且不在不重要行集合中
          }
        }
      }

      const lineContent = this.getLineContent(sourceCode, line).trim();

      // 空行总是不重要
      if (!lineContent) {
        return true;
      }

      // 现代语法模式匹配 - 支持更多语义不重要的行
      const insignificantPatterns = [
        // 基础不重要模式
        /^\/\/.*$/, // 单行注释
        /^\/\*.*\*\/$/, // 单行块注释
        /^\/\*.*$/, // 块注释开始
        /^.*\*\/$/, // 块注释结束
        /^\*.*$/, // 块注释内容行
        /^import\s/, // ES6 导入
        /^export\s.*from\s/, // ES6 重导出
        /^export\s*\{[^}]*\}\s*;?\s*$/, // 仅导出声明

        // 现代语法支持 - 修正正则表达式
        /^type\s+\w+\s*=\s*[^{;]+;?\s*$/, // 简单类型别名
        /^interface\s+\w+\s*\{?\s*$/, // 接口声明开始
        /^\s*\}\s*;?\s*$/, // 单独的闭合括号
        /^\s*\{\s*$/, // 单独的开放括号
        /^\s*\[\s*$/, // 数组开始
        /^\s*\]\s*[,;]?\s*$/, // 数组结束

        // JSX/TSX 支持
        /^import.*from\s+['"].*['"];?\s*$/, // 标准导入语句
        /^export\s*\*\s*from\s+['"].*['"];?\s*$/, // 重导出所有

        // 装饰器和注解
        /^@\w+(\([^)]*\))?\s*$/, // 装饰器行

        // 简单声明行 - 仅类型声明，无初始化
        /^const\s+\w+\s*:\s*[^=]+;\s*$/, // 仅类型声明的常量
        /^let\s+\w+\s*:\s*[^=]+;\s*$/, // 仅类型声明的变量

        // 现代模块语法
        /^export\s+default\s+\w+;\s*$/, // 简单默认导出

        // JSX 注释行
        /^\s*\{\/\*.*\*\/\}\s*$/, // JSX 注释
      ];

      return insignificantPatterns.some((pattern) => pattern.test(lineContent));
    } catch {
      // 如果获取行内容失败，认为该行不重要（避免错误传播）
      return true;
    }
  }

  /**
   * 查找最近的语义有意义的行（双向搜索）
   * 支持智能回退和现代语法感知
   * @param sourceCode 源代码内容
   * @param targetLine 目标行号 (1-based)
   * @param maxDistance 最大搜索距离（默认5行）
   * @param filePath 文件路径（可选，用于缓存）
   * @returns 最近的有意义行号，如果找不到则返回目标行
   */
  public static findNearestMeaningfulLine(
    sourceCode: string,
    targetLine: number,
    maxDistance: number = 5,
    filePath?: string
  ): number {
    // 首先检查目标行本身
    if (!this.isInsignificantLine(sourceCode, targetLine, filePath)) {
      return targetLine;
    }

    const lineMap = this.getCachedLineMap(sourceCode, filePath);
    const totalLines = lineMap.length;

    // 双向搜索，优先向上搜索（通常更有意义）
    for (let distance = 1; distance <= maxDistance; distance++) {
      // 向上搜索
      const upperLine = targetLine - distance;
      if (upperLine >= 1 && !this.isInsignificantLine(sourceCode, upperLine, filePath)) {
        return upperLine;
      }

      // 向下搜索
      const lowerLine = targetLine + distance;
      if (lowerLine <= totalLines && !this.isInsignificantLine(sourceCode, lowerLine, filePath)) {
        return lowerLine;
      }
    }

    // 如果在指定距离内找不到有意义的行，返回目标行
    return targetLine;
  }

  /**
   * 查找最后一个语义有意义的行（处理边界情况）
   * 专门用于处理 span 末尾位置的智能回退
   * @param sourceCode 源代码内容
   * @param fromLine 起始搜索行号 (1-based)
   * @param maxLookback 最大回退行数（默认10行）
   * @param filePath 文件路径（可选，用于缓存）
   * @returns 最后的有意义行号
   */
  public static findLastMeaningfulLine(
    sourceCode: string,
    fromLine: number,
    maxLookback: number = 10,
    filePath?: string
  ): number {
    const lineMap = this.getCachedLineMap(sourceCode, filePath);
    const actualFromLine = Math.min(fromLine, lineMap.length);

    // 从指定行开始向上搜索
    for (let line = actualFromLine; line >= Math.max(1, actualFromLine - maxLookback); line--) {
      if (!this.isInsignificantLine(sourceCode, line, filePath)) {
        return line;
      }
    }

    // 如果在回退范围内找不到有意义的行，使用扩展搜索
    // 这种情况可能发生在大段注释或空行区域
    const extendedLookback = Math.min(maxLookback * 2, actualFromLine - 1);
    for (let line = actualFromLine - maxLookback - 1; line >= Math.max(1, actualFromLine - extendedLookback); line--) {
      if (!this.isInsignificantLine(sourceCode, line, filePath)) {
        return line;
      }
    }

    // 极端情况：返回文件开始或实际搜索起点
    return Math.max(1, actualFromLine);
  }

  /**
   * 在指定行范围内找到最有意义的行
   * 用于智能回退中的范围优化策略
   * @param sourceCode 源代码内容
   * @param startLine 起始行号 (1-based)
   * @param endLine 结束行号 (1-based)
   * @param filePath 文件路径（可选，用于缓存）
   * @returns 最有意义的行号
   */
  private static findMostMeaningfulLineInRange(
    sourceCode: string,
    startLine: number,
    endLine: number,
    filePath?: string
  ): number {
    // 优先级策略：寻找具有实际代码逻辑的行
    const meaningfulLines: Array<{ line: number; score: number }> = [];

    for (let line = startLine; line <= endLine; line++) {
      if (!this.isInsignificantLine(sourceCode, line, filePath)) {
        const score = this.calculateLineMeaningfulnessScore(sourceCode, line);
        meaningfulLines.push({ line, score });
      }
    }

    if (meaningfulLines.length === 0) {
      // 如果范围内没有有意义的行，返回起始行
      return startLine;
    }

    // 返回得分最高的行
    meaningfulLines.sort((a, b) => b.score - a.score);
    return meaningfulLines[0].line;
  }

  /**
   * 计算行的语义重要性得分
   * 用于在多个候选行中选择最有意义的一行
   * @param sourceCode 源代码内容
   * @param line 行号 (1-based)
   * @returns 语义重要性得分 (越高越重要)
   */
  private static calculateLineMeaningfulnessScore(sourceCode: string, line: number): number {
    try {
      const lineContent = this.getLineContent(sourceCode, line).trim();
      let score = 1; // 基础分数

      // 高价值模式（增加分数）
      const highValuePatterns = [
        { pattern: /function\s+\w+/, score: 10 }, // 函数声明
        { pattern: /class\s+\w+/, score: 10 }, // 类声明
        { pattern: /if\s*\(/, score: 8 }, // 条件语句
        { pattern: /for\s*\(/, score: 8 }, // 循环语句
        { pattern: /while\s*\(/, score: 8 }, // while循环
        { pattern: /switch\s*\(/, score: 8 }, // switch语句
        { pattern: /return\s+/, score: 7 }, // 返回语句
        { pattern: /throw\s+/, score: 7 }, // 异常抛出
        { pattern: /catch\s*\(/, score: 7 }, // 异常捕获
        { pattern: /const\s+\w+\s*=/, score: 5 }, // 常量声明
        { pattern: /let\s+\w+\s*=/, score: 5 }, // 变量声明
        { pattern: /var\s+\w+\s*=/, score: 4 }, // var声明
        { pattern: /\w+\s*\(.*\)/, score: 6 }, // 函数调用
        { pattern: /\w+\.\w+/, score: 4 }, // 属性访问
        { pattern: /[=!<>]=/, score: 3 }, // 比较操作
        { pattern: /[+\-*/]=/, score: 3 }, // 算术赋值
      ];

      // 现代语法模式
      const modernPatterns = [
        { pattern: /async\s+function/, score: 9 }, // 异步函数
        { pattern: /await\s+/, score: 8 }, // await语句
        { pattern: /=>\s*/, score: 6 }, // 箭头函数
        { pattern: /const\s+\{.*\}\s*=/, score: 5 }, // 解构赋值
        { pattern: /\.\.\./, score: 4 }, // 展开操作符
        { pattern: /`.*\$\{.*\}.*`/, score: 4 }, // 模板字符串
      ];

      // JSX/TSX 模式
      const jsxPatterns = [
        { pattern: /<\w+[^>]*>/, score: 6 }, // JSX元素开始
        { pattern: /<\/\w+>/, score: 4 }, // JSX元素结束
        { pattern: /\{.*\}/, score: 3 }, // JSX表达式
      ];

      const allPatterns = [...highValuePatterns, ...modernPatterns, ...jsxPatterns];

      for (const { pattern, score: patternScore } of allPatterns) {
        if (pattern.test(lineContent)) {
          score += patternScore;
        }
      }

      // 长度奖励（更长的行通常包含更多逻辑）
      score += Math.min(lineContent.length / 20, 3);

      // 复杂性奖励（包含多个操作符的行）
      const operators = lineContent.match(/[+\-*/=<>!&|?:]/g);
      if (operators) {
        score += Math.min(operators.length * 0.5, 2);
      }

      return score;
    } catch {
      return 0; // 错误情况下返回最低分
    }
  }

  /**
   * 获取缓存的行语义分析结果
   * 实现文件级缓存以提升大文件的重复分析性能
   * @param sourceCode 源代码内容
   * @param filePath 文件路径（用于生成缓存键）
   * @returns 不重要行号的集合
   */
  private static getCachedLineAnalysis(sourceCode: string, filePath?: string): Set<number> {
    const cacheKey = this.generateLineAnalysisCacheKey(sourceCode, filePath);

    // 检查缓存
    const cached = this.lineAnalysisCache.get(cacheKey);
    if (cached) {
      cached.accessCount++;
      cached.timestamp = Date.now();
      return cached.insignificantLines;
    }

    // 缓存未命中，进行全文件分析
    const insignificantLines = new Set<number>();
    const lineMap = this.getCachedLineMap(sourceCode, filePath);

    for (let line = 1; line <= lineMap.length; line++) {
      if (this.isInsignificantLine(sourceCode, line, filePath)) {
        insignificantLines.add(line);
      }
    }

    // 缓存管理
    this.manageLineAnalysisCacheSize();

    // 添加到缓存
    this.lineAnalysisCache.set(cacheKey, {
      insignificantLines,
      timestamp: Date.now(),
      accessCount: 1,
    });

    return insignificantLines;
  }

  /**
   * 生成行分析缓存键
   * 支持文件路径参数以实现更精确的缓存
   */
  private static generateLineAnalysisCacheKey(sourceCode: string, filePath?: string): string {
    const contentKey = this.generateCacheKey(sourceCode);
    return filePath ? `${filePath}:${contentKey}` : contentKey;
  }

  /**
   * 管理行分析缓存大小
   */
  private static manageLineAnalysisCacheSize(): void {
    if (this.lineAnalysisCache.size <= this.maxCacheSize) {
      return;
    }

    // 当缓存超过清理阈值时，执行清理
    if (this.lineAnalysisCache.size >= this.cacheCleanupThreshold) {
      this.performLineAnalysisCacheCleanup();
    }
  }

  /**
   * 智能保底位置生成器
   * 基于上下文的智能默认位置选择，提供有意义的位置而非简单的 (1,0)
   * @param sourceCode 源代码内容
   * @param context 紧急位置上下文
   * @returns 智能生成的保底位置
   */
  public static generateIntelligentDefaultPosition(sourceCode: string, context: EmergencyPositionContext): number {
    try {
      // 策略1：基于函数上下文的位置生成
      if (context.functionContext) {
        const functionBasedPosition = this.generateFunctionBasedPosition(sourceCode, context.functionContext);
        if (functionBasedPosition !== null) {
          return functionBasedPosition;
        }
      }

      // 策略2：基于节点类型的上下文推断
      const typeBasedPosition = this.generateTypeBasedPosition(sourceCode, context.originalNode.type);
      if (typeBasedPosition !== null) {
        return typeBasedPosition;
      }

      // 策略3：基于源代码结构的智能推断
      const structureBasedPosition = this.generateStructureBasedPosition(sourceCode, context.sourceMetadata);
      if (structureBasedPosition !== null) {
        return structureBasedPosition;
      }

      // 策略4：基于失败策略的反向推断
      const failureBasedPosition = this.generateFailureBasedPosition(sourceCode, context.failedStrategies);
      if (failureBasedPosition !== null) {
        return failureBasedPosition;
      }

      // 最终保底：返回文件中第一个有意义的代码位置
      return this.findFirstMeaningfulCodePosition(sourceCode);
    } catch (error) {
      // 极端错误情况：返回文件开始位置
      console.warn('智能保底位置生成失败，使用文件开始位置:', error);
      return 0;
    }
  }

  /**
   * 基于函数上下文生成位置
   * @param sourceCode 源代码内容
   * @param functionContext 函数上下文
   * @returns 函数基础位置，如果无法生成则返回 null
   */
  private static generateFunctionBasedPosition(
    sourceCode: string,
    functionContext: { name: string; startPosition: number }
  ): number | null {
    try {
      // 使用函数开始位置
      if (functionContext.startPosition >= 0 && functionContext.startPosition < sourceCode.length) {
        // 验证位置的有效性
        const position = this.spanToPosition(sourceCode, functionContext.startPosition);
        if (position.line >= 1 && !this.isInsignificantLine(sourceCode, position.line)) {
          return functionContext.startPosition;
        }
      }

      // 如果函数开始位置无效，尝试在附近查找函数定义
      const functionKeywordPosition = this.findFunctionKeywordPosition(sourceCode, functionContext.name);
      if (functionKeywordPosition !== null) {
        return functionKeywordPosition;
      }

      return null;
    } catch (error) {
      console.debug('基于函数上下文的位置生成失败:', error);
      return null;
    }
  }

  /**
   * 基于节点类型生成上下文相关位置
   * @param sourceCode 源代码内容
   * @param nodeType 节点类型
   * @returns 类型基础位置，如果无法生成则返回 null
   */
  private static generateTypeBasedPosition(sourceCode: string, nodeType: string): number | null {
    try {
      // 根据节点类型查找相关的关键字
      const keywordMap: Record<string, string[]> = {
        IfStatement: ['if'],
        WhileStatement: ['while'],
        ForStatement: ['for'],
        SwitchStatement: ['switch'],
        TryStatement: ['try'],
        CatchClause: ['catch'],
        FunctionDeclaration: ['function'],
        FunctionExpression: ['function'],
        ArrowFunctionExpression: ['=>'],
        ConditionalExpression: ['?'],
        ClassDeclaration: ['class'],
        MethodDefinition: ['get', 'set', 'async', 'static'],
        ImportDeclaration: ['import'],
        ExportDeclaration: ['export'],
        ReturnStatement: ['return'],
        ThrowStatement: ['throw'],
      };

      const keywords = keywordMap[nodeType];
      if (!keywords) {
        return null;
      }

      // 在代码中查找这些关键字的第一个出现位置
      for (const keyword of keywords) {
        const position = this.findFirstKeywordOccurrence(sourceCode, keyword);
        if (position !== null) {
          return position;
        }
      }

      return null;
    } catch (error) {
      console.debug('基于节点类型的位置生成失败:', error);
      return null;
    }
  }

  /**
   * 基于源代码结构生成位置
   * @param sourceCode 源代码内容
   * @param metadata 源代码元数据
   * @returns 结构基础位置，如果无法生成则返回 null
   */
  private static generateStructureBasedPosition(
    sourceCode: string,
    metadata: { totalLines: number; totalLength: number; hasValidCode: boolean }
  ): number | null {
    try {
      if (!metadata.hasValidCode) {
        return 0; // 无有效代码时返回文件开始
      }

      // 策略1：查找文件中第一个函数定义
      const firstFunctionPosition = this.findFirstFunctionDefinition(sourceCode);
      if (firstFunctionPosition !== null) {
        return firstFunctionPosition;
      }

      // 策略2：查找文件中第一个类定义
      const firstClassPosition = this.findFirstClassDefinition(sourceCode);
      if (firstClassPosition !== null) {
        return firstClassPosition;
      }

      // 策略3：查找第一个实质性语句（排除导入等）
      const firstStatementPosition = this.findFirstSubstantialStatement(sourceCode);
      if (firstStatementPosition !== null) {
        return firstStatementPosition;
      }

      // 策略4：基于文件结构的启发式位置
      const heuristicPosition = this.generateHeuristicPosition(sourceCode, metadata);
      if (heuristicPosition !== null) {
        return heuristicPosition;
      }

      return null;
    } catch (error) {
      console.debug('基于结构的位置生成失败:', error);
      return null;
    }
  }

  /**
   * 基于失败策略的反向推断生成位置
   * @param sourceCode 源代码内容
   * @param failedStrategies 失败的策略列表
   * @returns 反向推断位置，如果无法生成则返回 null
   */
  private static generateFailureBasedPosition(sourceCode: string, failedStrategies: string[]): number | null {
    try {
      // 分析失败模式，确定可能的替代策略
      const failurePatterns = {
        tokenAnalysis: failedStrategies.includes('token-analysis'),
        patternMatching: failedStrategies.includes('pattern-matching'),
        spanValidation: failedStrategies.includes('span-validation'),
        parentFallback: failedStrategies.includes('parent-fallback'),
      };

      // 如果Token分析失败，尝试基于字符串的简单搜索
      if (failurePatterns.tokenAnalysis) {
        const simpleSearchPosition = this.performSimpleKeywordSearch(sourceCode);
        if (simpleSearchPosition !== null) {
          return simpleSearchPosition;
        }
      }

      // 如果模式匹配失败，尝试更宽泛的匹配
      if (failurePatterns.patternMatching) {
        const broadMatchPosition = this.performBroadPatternMatch(sourceCode);
        if (broadMatchPosition !== null) {
          return broadMatchPosition;
        }
      }

      // 如果父节点回退失败，尝试同级节点搜索
      if (failurePatterns.parentFallback) {
        const siblingPosition = this.findSiblingNodePosition(sourceCode);
        if (siblingPosition !== null) {
          return siblingPosition;
        }
      }

      return null;
    } catch (error) {
      console.debug('基于失败策略的位置生成失败:', error);
      return null;
    }
  }

  /**
   * 查找文件中第一个有意义的代码位置
   * @param sourceCode 源代码内容
   * @returns 第一个有意义的代码位置
   */
  private static findFirstMeaningfulCodePosition(sourceCode: string): number {
    const lines = sourceCode.split('\n');
    let offset = 0;

    for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
      const line = lines[lineIndex];
      const trimmedLine = line.trim();

      // 跳过空行、注释和简单导入语句
      if (
        trimmedLine &&
        !trimmedLine.startsWith('//') &&
        !trimmedLine.startsWith('/*') &&
        !trimmedLine.startsWith('*') &&
        !trimmedLine.match(/^import\s.*from\s/)
      ) {
        // 找到第一个非空白字符的位置
        const firstNonSpace = line.search(/\S/);
        return firstNonSpace >= 0 ? offset + firstNonSpace : offset;
      }

      offset += line.length + 1; // +1 for newline
    }

    return 0; // 如果没有找到有意义的代码，返回文件开始
  }

  // 辅助方法实现

  private static findFunctionKeywordPosition(sourceCode: string, functionName: string): number | null {
    const patterns = [
      new RegExp(`function\\s+${functionName}\\b`),
      new RegExp(`const\\s+${functionName}\\s*=`),
      new RegExp(`let\\s+${functionName}\\s*=`),
      new RegExp(`${functionName}\\s*[:=]\\s*function`),
      new RegExp(`${functionName}\\s*[:=]\\s*\\(`),
    ];

    for (const pattern of patterns) {
      const match = sourceCode.match(pattern);
      if (match && match.index !== undefined) {
        return match.index;
      }
    }

    return null;
  }

  private static findFirstKeywordOccurrence(sourceCode: string, keyword: string): number | null {
    const regex = new RegExp(`\\b${keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`);
    const match = sourceCode.match(regex);
    return match && match.index !== undefined ? match.index : null;
  }

  private static findFirstFunctionDefinition(sourceCode: string): number | null {
    const patterns = [
      /\bfunction\s+\w+/,
      /\w+\s*=\s*function/,
      /\w+\s*:\s*function/,
      /\w+\s*=\s*\(/,
      /\w+\s*=\s*async\s*\(/,
    ];

    for (const pattern of patterns) {
      const match = sourceCode.match(pattern);
      if (match && match.index !== undefined) {
        return match.index;
      }
    }

    return null;
  }

  private static findFirstClassDefinition(sourceCode: string): number | null {
    const match = sourceCode.match(/\bclass\s+\w+/);
    return match && match.index !== undefined ? match.index : null;
  }

  private static findFirstSubstantialStatement(sourceCode: string): number | null {
    const lines = sourceCode.split('\n');
    let offset = 0;

    for (const line of lines) {
      const trimmedLine = line.trim();

      // 查找实质性语句（非导入、非注释、非简单声明）
      if (
        trimmedLine &&
        !trimmedLine.startsWith('//') &&
        !trimmedLine.startsWith('/*') &&
        !trimmedLine.match(/^import\s/) &&
        !trimmedLine.match(/^export\s.*from/) &&
        trimmedLine.length > 10
      ) {
        const firstNonSpace = line.search(/\S/);
        return firstNonSpace >= 0 ? offset + firstNonSpace : offset;
      }

      offset += line.length + 1;
    }

    return null;
  }

  private static generateHeuristicPosition(sourceCode: string, metadata: any): number | null {
    // 基于文件长度的启发式选择
    if (metadata.totalLength < 1000) {
      // 小文件：选择文件中间位置附近的第一个有意义行
      const midpoint = Math.floor(metadata.totalLength / 2);
      return this.findNearestMeaningfulPositionFromOffset(sourceCode, midpoint);
    } else {
      // 大文件：选择前1/4位置附近的有意义行
      const quarterPoint = Math.floor(metadata.totalLength / 4);
      return this.findNearestMeaningfulPositionFromOffset(sourceCode, quarterPoint);
    }
  }

  private static findNearestMeaningfulPositionFromOffset(sourceCode: string, offset: number): number | null {
    const position = this.spanToPosition(sourceCode, offset);
    const meaningfulLine = this.findNearestMeaningfulLine(sourceCode, position.line, 10);

    if (meaningfulLine !== position.line) {
      return this.lineColumnToOffset(sourceCode, meaningfulLine, 1);
    }

    return offset;
  }

  private static performSimpleKeywordSearch(sourceCode: string): number | null {
    const commonKeywords = ['function', 'class', 'const', 'let', 'if', 'for', 'while', 'return'];

    for (const keyword of commonKeywords) {
      const position = this.findFirstKeywordOccurrence(sourceCode, keyword);
      if (position !== null) {
        return position;
      }
    }

    return null;
  }

  private static performBroadPatternMatch(sourceCode: string): number | null {
    // 宽泛的模式匹配，查找任何看起来像代码的内容
    const broadPatterns = [/\w+\s*[=:]\s*[^\s]/, /\w+\s*\(/, /\{[^}]*\}/, /\[[^\]]*\]/, /[+\-*/=<>!]/];

    for (const pattern of broadPatterns) {
      const match = sourceCode.match(pattern);
      if (match && match.index !== undefined) {
        return match.index;
      }
    }

    return null;
  }

  private static findSiblingNodePosition(sourceCode: string): number | null {
    // 简化实现：查找文件中任何分号或大括号，这通常标识语句边界
    const match = sourceCode.match(/[;{}]/);
    return match && match.index !== undefined ? match.index : null;
  }

  /**
   * 执行行分析缓存清理，移除最少使用的条目
   */
  private static performLineAnalysisCacheCleanup(): void {
    const entries = Array.from(this.lineAnalysisCache.entries());

    // 按访问频率和时间排序（LRU + LFU混合策略）
    entries.sort((a, b) => {
      const scoreA = a[1].accessCount * 0.7 + (Date.now() - a[1].timestamp) * -0.3;
      const scoreB = b[1].accessCount * 0.7 + (Date.now() - b[1].timestamp) * -0.3;
      return scoreA - scoreB;
    });

    // 移除最低分的条目，保留75%的缓存
    const removeCount = Math.floor(this.lineAnalysisCache.size * 0.25);
    const toRemove = entries.slice(0, removeCount);

    for (const [key] of toRemove) {
      this.lineAnalysisCache.delete(key);
    }
  }
}
