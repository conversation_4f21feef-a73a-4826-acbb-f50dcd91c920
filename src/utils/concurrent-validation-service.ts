/**
 * 并发验证服务
 * 为CLI参数验证提供高性能并发处理核心
 * 支持批量验证、并发限制和错误聚合
 */

import type { CLIOptions } from '../config/types';
import { hasValidPathCharacters } from './path-processor';

export interface ValidationRule {
  name: string;
  validate: (options: CLIOptions) => Promise<ValidationResult> | ValidationResult;
  priority: number; // 优先级，数字越小优先级越高
}

export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
  executionTime?: number;
}

export interface ConcurrentValidationOptions {
  maxConcurrency?: number;
  timeoutMs?: number;
  enableProfiling?: boolean;
  failFast?: boolean; // 遇到第一个错误就停止
}

export interface ValidationSummary {
  valid: boolean;
  totalRules: number;
  passedRules: number;
  failedRules: number;
  errors: string[];
  warnings: string[];
  executionTime: number;
  ruleResults: Array<{
    ruleName: string;
    result: ValidationResult;
  }>;
}

/**
 * 并发验证服务
 * 提供高性能的CLI参数验证并发处理
 */
export class ConcurrentValidationService {
  private rules: ValidationRule[] = [];
  private readonly options: Required<ConcurrentValidationOptions>;

  constructor(options: ConcurrentValidationOptions = {}) {
    this.options = {
      maxConcurrency: options.maxConcurrency ?? 10,
      timeoutMs: options.timeoutMs ?? 5000,
      enableProfiling: options.enableProfiling ?? false,
      failFast: options.failFast ?? false
    };
  }

  /**
   * 注册验证规则
   */
  addRule(rule: ValidationRule): void {
    this.rules.push(rule);
    // 按优先级排序
    this.rules.sort((a, b) => a.priority - b.priority);
  }

  /**
   * 批量注册验证规则
   */
  addRules(rules: ValidationRule[]): void {
    rules.forEach(rule => this.addRule(rule));
  }

  /**
   * 并发验证CLI选项
   */
  async validateConcurrently(options: CLIOptions): Promise<ValidationSummary> {
    const startTime = performance.now();
    const ruleResults: Array<{
      ruleName: string;
      result: ValidationResult;
    }> = [];

    // 如果没有规则直接返回成功
    if (this.rules.length === 0) {
      return this.createSummary(ruleResults, startTime);
    }

    // 创建并发执行池
    const executionPromises: Promise<void>[] = [];
    const semaphore = new Semaphore(this.options.maxConcurrency);
    let hasErrors = false;

    // 为每个规则创建执行任务
    for (const rule of this.rules) {
      const task = async () => {
        // 如果启用快速失败且已有错误，跳过后续规则
        if (this.options.failFast && hasErrors) {
          return;
        }

        await semaphore.acquire();
        
        try {
          const ruleResult = await this.executeRuleWithTimeout(rule, options);
          
          ruleResults.push({
            ruleName: rule.name,
            result: ruleResult
          });

          // 检查是否有错误
          if (!ruleResult.valid && ruleResult.errors.length > 0) {
            hasErrors = true;
          }

        } finally {
          semaphore.release();
        }
      };

      executionPromises.push(task());
    }

    // 等待所有验证完成
    await Promise.allSettled(executionPromises);

    // 按规则名称排序结果，确保结果顺序一致
    ruleResults.sort((a, b) => a.ruleName.localeCompare(b.ruleName));

    return this.createSummary(ruleResults, startTime);
  }

  /**
   * 同步验证所有规则（用于简单场景）
   */
  validateSync(options: CLIOptions): ValidationSummary {
    const startTime = performance.now();
    const ruleResults: Array<{
      ruleName: string;
      result: ValidationResult;
    }> = [];

    for (const rule of this.rules) {
      const ruleStartTime = performance.now();
      
      try {
        const result = rule.validate(options);
        const validationResult = result instanceof Promise ? 
          { valid: false, errors: ['同步验证不支持异步规则'], warnings: [] } : result;
        
        validationResult.executionTime = performance.now() - ruleStartTime;
        
        ruleResults.push({
          ruleName: rule.name,
          result: validationResult
        });

        // 快速失败检查
        if (this.options.failFast && !validationResult.valid && validationResult.errors.length > 0) {
          break;
        }

      } catch (error) {
        ruleResults.push({
          ruleName: rule.name,
          result: {
            valid: false,
            errors: [`规则执行失败: ${error instanceof Error ? error.message : String(error)}`],
            warnings: [],
            executionTime: performance.now() - ruleStartTime
          }
        });
      }
    }

    return this.createSummary(ruleResults, startTime);
  }

  /**
   * 验证单个规则
   */
  async validateSingleRule(ruleName: string, options: CLIOptions): Promise<ValidationResult | null> {
    const rule = this.rules.find(r => r.name === ruleName);
    if (!rule) {
      return null;
    }

    return this.executeRuleWithTimeout(rule, options);
  }

  /**
   * 获取已注册的规则列表
   */
  getRules(): Array<{ name: string; priority: number }> {
    return this.rules.map(rule => ({
      name: rule.name,
      priority: rule.priority
    }));
  }

  /**
   * 清除所有规则
   */
  clearRules(): void {
    this.rules = [];
  }

  /**
   * 获取服务统计信息
   */
  getStats(): {
    totalRules: number;
    maxConcurrency: number;
    timeoutMs: number;
    failFast: boolean;
  } {
    return {
      totalRules: this.rules.length,
      maxConcurrency: this.options.maxConcurrency,
      timeoutMs: this.options.timeoutMs,
      failFast: this.options.failFast
    };
  }

  // 私有方法

  /**
   * 执行带超时的规则验证
   */
  private async executeRuleWithTimeout(rule: ValidationRule, options: CLIOptions): Promise<ValidationResult> {
    const ruleStartTime = performance.now();
    
    try {
      // 创建超时Promise
      const timeoutPromise = new Promise<ValidationResult>((_, reject) => {
        setTimeout(() => {
          reject(new Error(`规则 ${rule.name} 执行超时`));
        }, this.options.timeoutMs);
      });

      // 执行规则
      const validationPromise = Promise.resolve(rule.validate(options));

      // 竞争执行：规则验证 vs 超时
      const result = await Promise.race([validationPromise, timeoutPromise]);
      
      // 记录执行时间
      result.executionTime = performance.now() - ruleStartTime;
      
      return result;

    } catch (error) {
      return {
        valid: false,
        errors: [`规则执行失败: ${error instanceof Error ? error.message : String(error)}`],
        warnings: [],
        executionTime: performance.now() - ruleStartTime
      };
    }
  }

  /**
   * 创建验证摘要
   */
  private createSummary(
    ruleResults: Array<{ ruleName: string; result: ValidationResult }>,
    startTime: number
  ): ValidationSummary {
    const allErrors: string[] = [];
    const allWarnings: string[] = [];
    let passedRules = 0;
    let failedRules = 0;

    for (const { result } of ruleResults) {
      if (result.valid) {
        passedRules++;
      } else {
        failedRules++;
      }
      
      allErrors.push(...result.errors);
      allWarnings.push(...result.warnings);
    }

    return {
      valid: failedRules === 0,
      totalRules: ruleResults.length,
      passedRules,
      failedRules,
      errors: allErrors,
      warnings: allWarnings,
      executionTime: performance.now() - startTime,
      ruleResults
    };
  }
}

/**
 * 信号量实现，用于限制并发数
 */
class Semaphore {
  private permits: number;
  private waiting: Array<() => void> = [];

  constructor(permits: number) {
    this.permits = permits;
  }

  async acquire(): Promise<void> {
    if (this.permits > 0) {
      this.permits--;
      return Promise.resolve();
    }

    return new Promise<void>((resolve) => {
      this.waiting.push(resolve);
    });
  }

  release(): void {
    if (this.waiting.length > 0) {
      const resolve = this.waiting.shift()!;
      resolve();
    } else {
      this.permits++;
    }
  }
}

// 预定义的验证规则

/**
 * 详细模式上下文依赖验证规则
 */
export const detailsContextDependencyRule: ValidationRule = {
  name: 'details-context-dependency',
  priority: 1,
  validate: (options: CLIOptions): ValidationResult => {
    const errors: string[] = [];
    const warnings: string[] = [];

    // --show-context 必须与 --details 组合使用
    if (options.showContext && !options.details) {
      errors.push('--show-context 参数必须与 --details 参数组合使用');
    }

    // --show-all-context 必须与 --details 组合使用
    if (options.showAllContext && !options.details) {
      errors.push('--show-all-context 参数必须与 --details 参数组合使用');
    }

    // --context-lines 仅在启用上下文时有效
    if (options.contextLines !== undefined && !options.showContext && !options.showAllContext) {
      warnings.push('--context-lines 参数仅在使用 --show-context 或 --show-all-context 时有效');
    }

    // --context-lines 参数范围验证
    if (options.contextLines !== undefined) {
      if (options.contextLines < 0) {
        errors.push('--context-lines 必须为非负整数');
      } else if (options.contextLines > 20) {
        warnings.push('--context-lines 过大（>20）可能影响性能和输出可读性，建议使用较小的值');
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }
};

/**
 * 智能过滤参数验证规则
 */
export const smartFilterValidationRule: ValidationRule = {
  name: 'smart-filter-validation',
  priority: 2,
  validate: (options: CLIOptions): ValidationResult => {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 验证 maxContextItems
    if (options.maxContextItems !== undefined) {
      if (!Number.isInteger(options.maxContextItems) || options.maxContextItems < 1) {
        errors.push('--max-context-items 必须是正整数');
      } else if (options.maxContextItems > 1000) {
        warnings.push('--max-context-items 设置过高可能影响性能');
      }
    }

    // 验证 minComplexityIncrement
    if (options.minComplexityIncrement !== undefined) {
      if (!Number.isFinite(options.minComplexityIncrement) || options.minComplexityIncrement < 0) {
        errors.push('--min-complexity-increment 必须是非负数');
      } else if (options.minComplexityIncrement > 10) {
        warnings.push('--min-complexity-increment 设置过高可能过滤掉大部分上下文');
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }
};

/**
 * 路径验证规则
 */
export const pathValidationRule: ValidationRule = {
  name: 'path-validation',
  priority: 3,
  validate: (options: CLIOptions): ValidationResult => {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 验证路径不为空
    if (!options.paths || options.paths.length === 0) {
      errors.push('必须指定至少一个要分析的文件或目录路径');
      return { valid: false, errors, warnings };
    }

    // 检查路径长度限制
    const longPaths = options.paths.filter(path => path.length > 260);
    if (longPaths.length > 0) {
      warnings.push(`以下路径可能超过系统限制: ${longPaths.slice(0, 3).join(', ')}${longPaths.length > 3 ? '...' : ''}`);
    }

    // 检查特殊字符（使用更智能的验证）
    const invalidPaths = options.paths.filter(path => !hasValidPathCharacters(path));
    if (invalidPaths.length > 0) {
      errors.push(`路径包含无效字符: ${invalidPaths.slice(0, 3).join(', ')}`);
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }
};

/**
 * 输出格式验证规则
 */
export const outputFormatValidationRule: ValidationRule = {
  name: 'output-format-validation',
  priority: 4,
  validate: (options: CLIOptions): ValidationResult => {
    const errors: string[] = [];
    const warnings: string[] = [];

    // JSON输出与某些选项的兼容性检查
    if (options.format === 'json') {
      if (options.showContext || options.showAllContext) {
        warnings.push('JSON格式输出可能不包含完整的代码上下文信息');
      }
    }

    // HTML输出与quiet模式的冲突
    if (options.format === 'html' && options.quiet) {
      warnings.push('HTML格式在静默模式下生成的报告可能缺少交互功能');
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }
};

/**
 * 调试参数验证规则
 */
export const debugParameterValidationRule: ValidationRule = {
  name: 'debug-parameter-validation',
  priority: 5,
  validate: (options: CLIOptions): ValidationResult => {
    const warnings: string[] = [];
    
    // --debug-level 需要 --debug (排除默认值 'info')
    if (options.debugLevel && options.debugLevel !== 'info' && !options.debug) {
      warnings.push('--debug-level 参数仅在使用 --debug 时有效');
    }
    
    // --debug-output 需要 --debug
    if (options.debugOutput && !options.debug) {
      warnings.push('--debug-output 参数仅在使用 --debug 时有效');
    }
    
    // --visual-report 需要 --debug
    if (options.visualReport && !options.debug) {
      warnings.push('--visual-report 参数仅在使用 --debug 时有效');
    }
    
    return {
      valid: true, // 警告不影响程序继续执行
      errors: [],
      warnings
    };
  }
};

/**
 * 断点参数验证规则
 */
export const breakpointParameterValidationRule: ValidationRule = {
  name: 'breakpoint-parameter-validation',
  priority: 6,
  validate: (options: CLIOptions): ValidationResult => {
    const warnings: string[] = [];
    
    // --break-on-rule 需要 --enable-breakpoints
    if (options.breakOnRule && !options.enableBreakpoints) {
      warnings.push('--break-on-rule 参数仅在使用 --enable-breakpoints 时有效');
    }
    
    // --break-on-complexity 需要 --enable-breakpoints
    if (options.breakOnComplexity && !options.enableBreakpoints) {
      warnings.push('--break-on-complexity 参数仅在使用 --enable-breakpoints 时有效');
    }
    
    // --step-by-step 需要 --enable-breakpoints
    if (options.stepByStep && !options.enableBreakpoints) {
      warnings.push('--step-by-step 参数仅在使用 --enable-breakpoints 时有效');
    }
    
    return {
      valid: true,
      errors: [],
      warnings
    };
  }
};

/**
 * UI参数验证规则
 */
export const uiParameterValidationRule: ValidationRule = {
  name: 'ui-parameter-validation',
  priority: 7,
  validate: (options: CLIOptions): ValidationResult => {
    const warnings: string[] = [];
    
    // --open 需要 --ui
    if (options.open && !options.ui) {
      warnings.push('--open 参数仅在使用 --ui 时有效');
    }
    
    return {
      valid: true,
      errors: [],
      warnings
    };
  }
};

/**
 * 智能过滤依赖验证规则
 */
export const smartFilterDependencyRule: ValidationRule = {
  name: 'smart-filter-dependency',
  priority: 8,
  validate: (options: CLIOptions): ValidationResult => {
    const warnings: string[] = [];
    const hasContextDisplay = options.showContext || options.showAllContext;
    
    // --max-context-items 需要上下文显示
    if (options.maxContextItems !== undefined && !hasContextDisplay) {
      warnings.push('--max-context-items 参数仅在使用 --show-context 或 --show-all-context 时有效');
    }
    
    // --min-complexity-increment 需要上下文显示
    if (options.minComplexityIncrement !== undefined && !hasContextDisplay) {
      warnings.push('--min-complexity-increment 参数仅在使用 --show-context 或 --show-all-context 时有效');
    }
    
    return {
      valid: true,
      errors: [],
      warnings
    };
  }
};

/**
 * 输出增强建议规则
 */
export const outputEnhancementSuggestionRule: ValidationRule = {
  name: 'output-enhancement-suggestion',
  priority: 9,
  validate: (options: CLIOptions): ValidationResult => {
    const warnings: string[] = [];
    
    // 建议使用更好的输出格式
    if (options.outputDir && options.format === 'text') {
      warnings.push('建议使用 --format json 或 --format html 以获得更好的文件输出效果');
    }
    
    return {
      valid: true,
      errors: [],
      warnings
    };
  }
};

/**
 * 文件复杂度过滤参数验证规则
 */
export const fileComplexityFilterValidationRule: ValidationRule = {
  name: 'file-complexity-filter-validation',
  priority: 10,
  validate: (options: CLIOptions): ValidationResult => {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 验证 minFileComplexity 参数
    if (options.minFileComplexity !== undefined) {
      const value = options.minFileComplexity;
      
      // 检查是否为有效数字
      if (!Number.isFinite(value)) {
        errors.push('--min-file-complexity 必须是一个有效的数字');
      } else if (value < 0) {
        errors.push('--min-file-complexity 必须是非负数（>= 0）');
      } else if (!Number.isInteger(value)) {
        errors.push('--min-file-complexity 必须是一个整数');
      } else {
        // 提供使用建议
        if (value === 0) {
          warnings.push('--min-file-complexity 设置为 0 将显示所有文件（包括零复杂度文件）');
        } else if (value > 50) {
          warnings.push('--min-file-complexity 设置过高可能隐藏大部分文件，建议使用较小的值');
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }
};

// 默认验证服务实例
let defaultValidationService: ConcurrentValidationService | null = null;

/**
 * 获取默认的并发验证服务
 */
export function getDefaultValidationService(): ConcurrentValidationService {
  if (!defaultValidationService) {
    defaultValidationService = new ConcurrentValidationService({
      maxConcurrency: 5,
      timeoutMs: 3000,
      failFast: false
    });
    
    // 注册所有验证规则（按优先级顺序）
    defaultValidationService.addRules([
      detailsContextDependencyRule,          // 优先级 1
      smartFilterValidationRule,             // 优先级 2
      pathValidationRule,                    // 优先级 3
      outputFormatValidationRule,            // 优先级 4
      debugParameterValidationRule,          // 优先级 5
      breakpointParameterValidationRule,     // 优先级 6
      uiParameterValidationRule,             // 优先级 7
      smartFilterDependencyRule,             // 优先级 8
      outputEnhancementSuggestionRule,       // 优先级 9
      fileComplexityFilterValidationRule     // 优先级 10
    ]);
  }
  
  return defaultValidationService;
}

/**
 * 创建自定义验证服务
 */
export function createValidationService(options?: ConcurrentValidationOptions): ConcurrentValidationService {
  return new ConcurrentValidationService(options);
}

/**
 * 验证 minFileComplexity 参数的独立函数
 * 用于在参数解析阶段进行快速验证
 */
export function validateMinFileComplexity(value: string): number {
  // 检查空值
  if (!value || value.trim() === '') {
    throw new Error(`参数 --min-file-complexity 的值不能为空`);
  }
  
  // 尝试解析为数字
  const numValue = Number(value);
  
  // 检查是否为有效数字
  if (!Number.isFinite(numValue)) {
    throw new Error(`参数 --min-file-complexity 的值 "${value}" 无效，期望一个有效的数字`);
  }
  
  // 检查是否为非负数
  if (numValue < 0) {
    throw new Error(`参数 --min-file-complexity 的值 "${value}" 无效，必须是非负数（>= 0）`);
  }
  
  // 检查是否为整数
  if (!Number.isInteger(numValue)) {
    throw new Error(`参数 --min-file-complexity 的值 "${value}" 无效，必须是一个整数`);
  }
  
  return numValue;
}