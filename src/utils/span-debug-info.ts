/**
 * 最小化 SWC Span 调试信息工具
 * 生成紧凑的单行调试信息，优化 token 使用效率
 */

import chalk from 'chalk';
import { getGlobalFileCache } from './file-cache';
import { type DetailStep } from '../core/types';
import type { CLIOptions } from '../config/types';

/**
 * 安全转义代码片段
 */
function escapeSnippet(snippet: string, maxLength = 20): string {
  if (!snippet) return '';

  const escaped = snippet.replace(/\n/g, '\\n').replace(/\t/g, ' ').replace(/\r/g, '');

  return escaped.length > maxLength ? escaped.slice(0, maxLength - 3) + '...' : escaped;
}

/**
 * 生成最小化调试信息 - 单行格式
 * 格式: [DEBUG] NodeType@start-end: "code snippet"
 */
export async function formatSpanDebugInfo(
  step: DetailStep,
  filePath: string,
  options?: CLIOptions
): Promise<string | null> {
  if (!step?.span || !step.nodeType) {
    return null;
  }

  const { start, end } = step.span;
  const nodeType = step.nodeType;

  // 获取代码片段
  let snippet = '';
  try {
    const fileCache = getGlobalFileCache();
    const sourceCode = await fileCache.getFileContent(filePath);

    if (start >= 0 && end <= sourceCode.length && start < end) {
      snippet = escapeSnippet(sourceCode.slice(start, end));
    }
  } catch {
    // 忽略文件读取错误，使用空代码片段
  }

  // 单行紧凑格式
  const spanInfo = `${nodeType}@${start}-${end}`;
  const codeInfo = snippet ? `: "${snippet}"` : '';

  return chalk.gray(`[DEBUG] ${spanInfo}${codeInfo}`);
}
